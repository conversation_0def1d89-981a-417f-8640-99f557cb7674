package com.hengtiansoft.privilege.dao;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.google.common.collect.Lists;
import com.hengtiansoft.common.enumeration.DeleteFlagEnum;
import com.hengtiansoft.privilege.entity.dto.FreeTrialUserDTO;
import com.hengtiansoft.privilege.entity.dto.MiniMessageSubscribeVisitsDTO;
import com.hengtiansoft.privilege.entity.po.MiniMessageSubscribe;
import com.hengtiansoft.privilege.entity.vo.FreeTrialUserVO;
import com.hengtiansoft.privilege.enums.MsgSendStatusEnum;
import com.hengtiansoft.privilege.mapper.MiniMessageSubscribeMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Component
public class MessageSubscribeDao {

    @Autowired
    private MiniMessageSubscribeMapper miniMessageSubscribeMapper;

    /**
     * 新增订阅消息
     *
     * @param messageSubscribe
     */
    public void insertMessage(MiniMessageSubscribe messageSubscribe) {
        miniMessageSubscribeMapper.insertSelective(messageSubscribe);
    }


    @DS("selectDB")
    public int countByMsgType(Integer msgType) {
        return miniMessageSubscribeMapper.countByMsgType(msgType);
    }

    @DS("selectDB")
    public int countUserByMsgType(Integer msgType) {
        return miniMessageSubscribeMapper.countUserByMsgType(msgType);
    }

    public void messageVisitAdd(Long msgId){
        miniMessageSubscribeMapper.messageVisitAdd(msgId);
    }

    public void messageAmountAdd(Long msgId, Integer amount) {
        miniMessageSubscribeMapper.messageAmountAdd(msgId, amount);
    }

    public List<MiniMessageSubscribe> getUserByMsgIdTemplateId(Long msgId, String templateId) {
        return miniMessageSubscribeMapper.getUserByMsgIdTemplateId(msgId, templateId);
    }
    public void updateByPrimaryKeySelective(MiniMessageSubscribe subscribe){
        miniMessageSubscribeMapper.updateByPrimaryKeySelective(subscribe);
    }

    public Integer getSuccessCnt(Long msgId) {
        return miniMessageSubscribeMapper.getSuccessCnt(msgId);
    }

    public Integer getVisitCnt(Long msgId) {
        return miniMessageSubscribeMapper.getVisitCnt(msgId);
    }

    public Integer getAmount(Long msgId) {
        return miniMessageSubscribeMapper.getAmount(msgId);
    }

    @Transactional(rollbackFor = Exception.class)
    public Integer updateMsgId(Long msgId, String templateId){
        return miniMessageSubscribeMapper.updateMsgId(msgId, templateId);
    }
    public Integer updateMsgIdActivityId(Long msgId, String templateId, Long activityId) {
        return miniMessageSubscribeMapper.updateMsgIdActivityId(msgId, templateId, activityId);
    }

    public Integer updateMsgIdPtSuccess(Long msgId, String templateId, Long ptOrderId) {
        return miniMessageSubscribeMapper.updateMsgIdPtSuccess(msgId, templateId, ptOrderId);
    }

    public Integer updateMsgIdTargetId(Long msgId, String templateId, Long activityId, Long targetId) {
        return miniMessageSubscribeMapper.updateMsgIdTargetId(msgId, templateId, activityId, targetId);
    }

    public Integer updateMsgIdActivityIdList(Long msgId, String templateId, List<Long> activityIds) {
        if(CollectionUtils.isEmpty(activityIds)){
            return 0;
        }
        return miniMessageSubscribeMapper.updateMsgIdActivityIdList(msgId, templateId, activityIds);
    }

    public Integer updateMsgIdActivityIdDraw(Long msgId, String templateId, Long activityId, List<Integer> drawTypeList) {
        return miniMessageSubscribeMapper.updateMsgIdActivityIdDraw(msgId, templateId, activityId, drawTypeList);
    }

    @Transactional(rollbackFor = Exception.class)
    public Integer updateMsgIdByLabel(Long msgId, String templateId, List<Long> labelIds){
        return miniMessageSubscribeMapper.updateMsgIdByLabel(msgId, templateId, labelIds);
    }

    @Transactional(rollbackFor = Exception.class)
    public Integer updateMsgIdByNewUser(Long msgId, String templateId){
        return miniMessageSubscribeMapper.updateMsgIdByNewUser(msgId, templateId);
    }

    public Integer getPayCnt(Long msgId) {
        return miniMessageSubscribeMapper.getPayCnt(msgId);
    }

    public void deleteSubscribeByOpenId(String openId) {
        Condition condition = new Condition(MiniMessageSubscribe.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("openId", openId);
        criteria.andIn("sendFlag", Lists.newArrayList(MsgSendStatusEnum.NOT_PUSHED.getCode(), MsgSendStatusEnum.ERROR_PUSH.getCode()));
        criteria.andEqualTo("delflag", DeleteFlagEnum.IS_NOT_DELETE.getCode());
        MiniMessageSubscribe subscribe = new MiniMessageSubscribe();
        subscribe.setDelflag(DeleteFlagEnum.IS_DELETE.getCode());
        miniMessageSubscribeMapper.updateByConditionSelective(subscribe, condition);
    }

    public List<MiniMessageSubscribe> getSubscribeTime(Long userId, Integer messageEnumCode, Long activityId) {
        Example example = new Example(MiniMessageSubscribe.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("msgType", messageEnumCode);
        if(Objects.nonNull(activityId)){
            criteria.andEqualTo("activityId", activityId);
        }
        criteria.andEqualTo("userId", userId);
        criteria.andEqualTo("delflag", DeleteFlagEnum.IS_NOT_DELETE.getCode());
        example.orderBy("createTime").asc();
        return miniMessageSubscribeMapper.selectByExample(example);
    }

    public List<MiniMessageSubscribe> getSubscribeTime(Long userId, List<Integer> messageEnumCodes, List<Long> activityIds) {
        Example example = new Example(MiniMessageSubscribe.class);
        Example.Criteria criteria = example.createCriteria();
        if(CollectionUtils.isNotEmpty(messageEnumCodes)){
            criteria.andIn("msgType", messageEnumCodes);
        }
        if(CollectionUtils.isNotEmpty(activityIds)){
            criteria.andIn("activityId", activityIds);
        }
        criteria.andEqualTo("userId", userId);
        criteria.andEqualTo("delflag", DeleteFlagEnum.IS_NOT_DELETE.getCode());
        example.orderBy("createTime").asc();
        return miniMessageSubscribeMapper.selectByExample(example);
    }

    public Integer findRssCount(Long activityId, Integer msgType) {
        return miniMessageSubscribeMapper.findRssCount(activityId, msgType);
    }

    public Integer getRssCount(Long activityId, List<Integer> msgTypeList){
        return miniMessageSubscribeMapper.getRssCount(activityId, msgTypeList);
    }

    public Integer getCount(List<Integer> subscribeList, Integer sendFlag) {
        return miniMessageSubscribeMapper.getCount(subscribeList, sendFlag);
    }

    public Integer getCountByActivity(List<Integer> subscribeList, Integer sendFlag, Long activityId) {
        return miniMessageSubscribeMapper.getCountByActivity(subscribeList, sendFlag, activityId);
    }

    public List<FreeTrialUserVO> findByConditionSql(FreeTrialUserDTO dto) {
        return miniMessageSubscribeMapper.findByConditionSql(dto);
    }

    public List<MiniMessageSubscribe> findMaxSendTime(List<Long> userIds) {
        return miniMessageSubscribeMapper.findMaxSendTime(userIds);
    }

    public List<Long> findSuccessUsers(Long msgId) {
        return miniMessageSubscribeMapper.findSuccessUsers(msgId);
    }

    public void cleanMsgIdById(Long id){
        miniMessageSubscribeMapper.cleanMsgIdById(id);
    }

    public int countByTemplateIds(List<String> templateIds) {
        return miniMessageSubscribeMapper.countByTemplateIds(templateIds);
    }

    public int countUnreachCntByTemplateIds(List<String> templateIds) {
        return miniMessageSubscribeMapper.countUnreachCntByTemplateIds(templateIds);
    }

    public List<MiniMessageSubscribeVisitsDTO> selectVisitsUser(List<Long> userIds){
        if(CollectionUtils.isEmpty(userIds)){
            return new ArrayList<>();
        }
        return miniMessageSubscribeMapper.selectVisitsUser(userIds);
    }

    @DS("db3")
    public List<MiniMessageSubscribe> selectMaxSendTimeByUserId(List<Long> userIds, Date startTime, Date endTime){
        return miniMessageSubscribeMapper.selectMaxSendTimeByUserId(userIds, startTime, endTime);
    }
}
