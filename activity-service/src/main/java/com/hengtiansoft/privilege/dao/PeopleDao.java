package com.hengtiansoft.privilege.dao;

import com.hengtiansoft.common.enumeration.DeleteFlagEnum;
import com.hengtiansoft.privilege.entity.po.People;
import com.hengtiansoft.privilege.enums.LabelBuyTypeEnum;
import com.hengtiansoft.privilege.mapper.PeopleMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;
import org.springframework.util.Assert;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
@Slf4j
public class PeopleDao {
    @Resource
    private PeopleMapper peopleMapper;

    public int insert(People people) {
        return peopleMapper.insertSelective(people);
    }

    public int batchInsert(List<People> pos){
        return peopleMapper.insertList(pos);
    }

    public int update(People people) {
        return peopleMapper.updateByPrimaryKeySelective(people);
    }

    public People findById(Long id) {
        return peopleMapper.selectByPrimaryKey(id);
    }

    public List<People> findByLabelId(Long labelId) {
        Condition condition = new Condition(People.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("labelId", labelId);
        criteria.andEqualTo("delflag", DeleteFlagEnum.IS_NOT_DELETE.getCode());
        return peopleMapper.selectByExample(condition);
    }

    public List<People> findByLableIdAndUserId(Long labelId, Long userId) {
        Condition condition = new Condition(People.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("labelId", labelId);
        criteria.andEqualTo("userId", userId);
        criteria.andEqualTo("delflag", DeleteFlagEnum.IS_NOT_DELETE.getCode());
        return peopleMapper.selectByExample(condition);
    }

    public int findByLabelIdCnt(Long labelId) {
        return peopleMapper.findByLabelIdCnt(labelId);
    }

    public void updateById(People record){
        peopleMapper.updateByPrimaryKeySelective(record);
    }

    public int deleteByLabel(Long labelId) {
        Condition condition = new Condition(People.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("labelId", labelId);
        criteria.andEqualTo("delflag", DeleteFlagEnum.IS_NOT_DELETE.getCode());
        People po = new People();
        po.setDelflag(DeleteFlagEnum.IS_DELETE.getCode());
        po.setLabelId(labelId);
        return peopleMapper.updateByExampleSelective(po, condition);
    }

    public int deleteByList(List<People> rangeFromDb) {
        for (People people : rangeFromDb) {
            people.setDelflag(DeleteFlagEnum.IS_DELETE.getCode());
        }
        return this.updateBatch(rangeFromDb);
    }

    public int updateBatch(List<People> pos){
        // return peopleMapper.updateBatch(pos);
        return 0;
    }

    public List<People> findByImportIdAndPhones(Long importId, List<String> phones) {
        Assert.notNull(importId, "属性为空");
        Assert.isTrue(CollectionUtils.isNotEmpty(phones), "属性为空");
        Condition condition = new Condition(People.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andIn("phone", phones);
        criteria.andEqualTo("peopleImprotId", importId);
        criteria.andEqualTo("delflag", DeleteFlagEnum.IS_NOT_DELETE.getCode());
        condition.orderBy("updateTime").desc();
        return peopleMapper.selectByExample(condition);
    }

    public int deleteByLabelId(Long labelId){
        Condition condition = new Condition(People.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("labelId", labelId);
        criteria.andEqualTo("delflag", DeleteFlagEnum.IS_NOT_DELETE.getCode());
        People po = new People();
        po.setDelflag(DeleteFlagEnum.IS_DELETE.getCode());
        return peopleMapper.updateByExampleSelective(po ,condition);
    }

    public int updateByImportId(Long importId, People people4Update) {
        Condition condition = new Condition(People.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("peopleImprotId", importId);
        return peopleMapper.updateByExampleSelective(people4Update, condition);
    }

    public People findByLabelIdFirst(Long labelId) {
        return peopleMapper.findByLabelIdFirst(labelId);
    }

    public People findByImportIdFirst(Long importId) {
        return peopleMapper.findByImportIdFirst(importId);
    }

    public int deleteByImportId(Long importId) {
        Condition condition = new Condition(People.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("peopleImprotId", importId);
        People po = new People();
        po.setDelflag(DeleteFlagEnum.IS_DELETE.getCode());
        return peopleMapper.updateByExampleSelective(po ,condition);
    }

    public int realDeleteByTime(Date time) {
        return peopleMapper.realDeleteByTime(time);
    }

    public int realDeleteByTimeAndLabelFlag(Date time) {
        return peopleMapper.realDeleteByTimeAndLabelFlag(time);
    }
}
