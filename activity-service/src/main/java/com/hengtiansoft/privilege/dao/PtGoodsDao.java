package com.hengtiansoft.privilege.dao;

import com.hengtiansoft.common.enumeration.DeleteFlagEnum;
import com.hengtiansoft.privilege.entity.po.PtGoods;
import com.hengtiansoft.privilege.mapper.PtGoodsMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * @program: milk-card-server
 * @description:
 * @author: haiyang
 * @create: 2024-04-23 17:39
 **/
@Repository
public class PtGoodsDao {

    @Resource
    private PtGoodsMapper ptGoodsMapper;

    public void insert(PtGoods ptGoods) {
        ptGoodsMapper.insert(ptGoods);
    }

    public void deleteByPtActivityId(Long ptActivityId, String userName) {
        ptGoodsMapper.deleteByPtActivityId(ptActivityId, userName);
    }

    public PtGoods getByActivityId(Long ptActivityId) {
        Example example = new Example(PtGoods.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("ptActivityId", ptActivityId);
        criteria.andEqualTo("delflag", DeleteFlagEnum.IS_NOT_DELETE.getCode());
        return ptGoodsMapper.selectOneByExample(example);
    }

    public List<PtGoods> getByActivityIds(List<Long> activityIds) {
        if (CollectionUtils.isEmpty(activityIds)) return Collections.emptyList();
        Example example = new Example(PtGoods.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andIn("ptActivityId", activityIds);
        criteria.andEqualTo("delflag", DeleteFlagEnum.IS_NOT_DELETE.getCode());
        return ptGoodsMapper.selectByExample(example);
    }

    public List<Long> selectInProgressNoShareCouponProductIds() {
        return ptGoodsMapper.selectInProgressNoShareCouponProductIds();
    }

    public List<PtGoods> findByProductId(Long productId) {
        Example example = new Example(PtGoods.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("productId", productId);
        criteria.andEqualTo("delflag", DeleteFlagEnum.IS_NOT_DELETE.getCode());
        return ptGoodsMapper.selectByExample(example);
    }

    public List<PtGoods> findByProductIds(List<Long> productIds) {
        if(CollectionUtils.isEmpty(productIds)){
            return Collections.emptyList();
        }
        Example example = new Example(PtGoods.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andIn("productId", productIds);
        criteria.andEqualTo("delflag", DeleteFlagEnum.IS_NOT_DELETE.getCode());
        return ptGoodsMapper.selectByExample(example);
    }

    public List<PtGoods> findBySkuIds(List<Long> skuIds) {
        if(CollectionUtils.isEmpty(skuIds)){
            return Collections.emptyList();
        }
        Example example = new Example(PtGoods.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andIn("skuId", skuIds);
        criteria.andEqualTo("delflag", DeleteFlagEnum.IS_NOT_DELETE.getCode());
        return ptGoodsMapper.selectByExample(example);
    }

    public List<PtGoods> findByProductIdAndSkuId(Long productId, Long skuId) {
        Example example = new Example(PtGoods.class,true, true);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("productId", productId);
        criteria.andEqualTo("skuId", skuId);
        criteria.andEqualTo("delflag", DeleteFlagEnum.IS_NOT_DELETE.getCode());
        return ptGoodsMapper.selectByExample(example);
    }

    public List<PtGoods> findBySkuId(Long skuId) {
        Example example = new Example(PtGoods.class,true, true);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("skuId", skuId);
        criteria.andEqualTo("delflag", DeleteFlagEnum.IS_NOT_DELETE.getCode());
        return ptGoodsMapper.selectByExample(example);
    }
}
