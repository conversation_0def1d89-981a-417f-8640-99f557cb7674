package com.hengtiansoft.operation.report.controller;

import com.hengtiansoft.common.entity.vo.PageVO;
import com.hengtiansoft.common.entity.vo.Response;
import com.hengtiansoft.common.factory.ResponseFactory;
import com.hengtiansoft.item.entity.dto.*;
import com.hengtiansoft.item.entity.vo.*;
import com.hengtiansoft.order.entity.dto.CardSummaryFormDTO;
import com.hengtiansoft.order.service.ReportFormService;
import com.hengtiansoft.order.dao.OrderSkuDao;
import com.hengtiansoft.order.dao.ShopInvoiceDao;
import com.hengtiansoft.user.entity.vo.ShopParamVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.List;

/**
 * 财务报表controller
 *
 * <AUTHOR>
 * @date 2020/9/22 16:57
 */
@RestController
@Api(tags = "财务报表-徐峰")
@RequestMapping("/report/form")
public class ReportFormController {

    @Resource
    private ReportFormService reportFormService;
    @Resource
    private OrderSkuDao orderSkuDao;
    @Resource
    private ShopInvoiceDao shopInvoiceDao;

    @ApiOperation("开票方式配置列表")
    @PostMapping("/invoiceTypeList")
    public Response<PageVO<InvoiceTypeFormListVO>> invoiceTypeList(@RequestBody InvoiceTypeFormListDTO dto) {
        return ResponseFactory.success(reportFormService.invoiceTypeList(dto));
    }

    @ApiOperation("开票方式配置详情")
    @GetMapping("/invoiceTypeOne")
    public Response<InvoiceTypeFormListVO> invoiceTypeOne(@RequestParam Long id) {
        return ResponseFactory.success(reportFormService.invoiceTypeOne(id));
    }

    @ApiOperation("保存开票方式")
    @PostMapping("/saveInvoiceType")
    public Response<Object> addInvoiceType(@RequestBody InvoiceTypeSaveDTO dto) {
        reportFormService.saveInvoiceType(dto);
        return ResponseFactory.success();
    }

    @Deprecated
    @ApiOperation("导入开票方式")
    @PostMapping(value = "importInvoiceType")
    public Response<String> importInvoiceType(MultipartFile file){
        reportFormService.importInvoiceType(file);
        return ResponseFactory.success();
    }

    @ApiOperation("静态财务报表")
    @PostMapping("/static")
    public Response<PageVO<ReportFormListVO>> listStatic(@RequestBody ReportFormListDTO dto) {
        return ResponseFactory.success(reportFormService.listStatic(dto));
    }

    @ApiOperation("动态财务报表")
    @PostMapping("/dynamic")
    public Response<PageVO<ReportFormListVO>> listDynamic(@RequestBody ReportFormListDTO dto) {
        return ResponseFactory.success(reportFormService.listDynamic(dto));
    }

    @ApiOperation("财务报表导入开票时间")
    @PostMapping(value = "importFormInvoiceTime")
    public Response<String> importFormInvoiceTime(MultipartFile file){
        reportFormService.importFormInvoiceTime(file);
        return ResponseFactory.success("导入成功");
    }

    @ApiOperation("修改金额")
    @PostMapping(value = "changeAmount")
    public Response<String> changeAmount(ChangeAmountDTO changeAmountDTO){
        reportFormService.changeAmount(changeAmountDTO);
        return ResponseFactory.success("导入成功");
    }

    @ApiOperation("修改对账金额")
    @PostMapping(value = "reconciliationAmount")
    public Response<String> reconciliationAmount(@RequestBody ChangeAmountDTO changeAmountDTO){
        reportFormService.reconciliationAmount(changeAmountDTO);
        return ResponseFactory.success("修改成功");
    }

    @ApiOperation("导入修改金额/对账金额和对账时间")
    @PostMapping(value = "importChangedAmount")
    public Response<String> importChangedAmount(MultipartFile file){
        reportFormService.importChangedAmount(file);
        return ResponseFactory.success("导入成功");
    }

    @ApiOperation("修改开票方式")
    @PostMapping(value = "changeInvoiceType")
    public Response<String> changeInvoiceType(ChangeInvoiceTypeDTO changeInvoiceTypeDTO) {
        reportFormService.changeInvoiceType(changeInvoiceTypeDTO);
        return ResponseFactory.success("修改开票方式成功");
    }

    @ApiOperation("批量修改开票方式")
    @PostMapping(value = "importChangeInvoiceType")
    public Response<String> importChangeInvoiceType(MultipartFile file) {
        reportFormService.batchChangeInvoiceType(file);
        return ResponseFactory.success("批量修改开票方式成功");
    }

    @ApiOperation("月提货统计报表")
    @PostMapping("/monthly")
    public Response<PageVO<MonthlyPlanFormListVO>> monthlyList(@RequestBody MonthlyPlanFormListDTO dto) {
        return ResponseFactory.success(reportFormService.monthlyList(dto));
    }

    @ApiOperation("校验备份表的时间")
    @GetMapping("/matchDay")
    public Response matchDay(@RequestParam String date) {
        reportFormService.matchDay(date);
        return ResponseFactory.success();
    }

    @ApiOperation("校验进销存备份表的时间")
    @PostMapping("/inventoryMatchDay")
    public Response inventoryMatchDay(@RequestBody InventoryListDTO dto) {
        reportFormService.inventoryMatchDay(dto.getSearchTimeStart(), dto.getSearchTimeEnd());
        return ResponseFactory.success();
    }

    @ApiOperation(value = "删除开票方式")
    @GetMapping(value = "/deleteInvoiceType")
    public Response<String> deleteInvoiceType(@RequestParam Long id){
        String response = reportFormService.deleteInvoiceType(id);
        return ResponseFactory.success(response);
    }

    @ApiOperation("导入开票时间")
    @PostMapping(value = "importInvoiceTime")
    public Response<String> importInvoiceTime(MultipartFile file){
        reportFormService.importInvoiceTime(file);
        return ResponseFactory.success("导入成功");
    }

    @ApiOperation("进销存报表")
    @PostMapping("/inventory")
    public Response<PageVO<InventoryFormListVO>> inventoryList(@RequestBody ReportFormListDTO dto) {
        return ResponseFactory.success(reportFormService.inventoryList(dto));
    }

    @ApiOperation("奶卡汇总余额表")
    @PostMapping("/cardSummary")
    public Response<PageVO<CardSummaryFormListVO>> cardSummaryList(@RequestBody CardSummaryFormDTO dto) {
        return ResponseFactory.success(reportFormService.cardSummaryList(dto));
    }

    @ApiOperation("获取所有渠道")
    @GetMapping("/getAllShop")
    public Response<List<ShopParamVO>> getAll() {
        return ResponseFactory.success(reportFormService.getAllShopParam());
    }

    @ApiOperation("获取所有售卖奶卡的渠道")
    @GetMapping("/getAllNaiKaShop")
    public Response<List<ShopParamVO>> getAllNaiKaShop() {
        return ResponseFactory.success(reportFormService.getAllNaiKaShop());
    }

    @ApiOperation("模糊搜索渠道")
    @GetMapping("/queryFuzzyShop")
    public Response<List<ShopParamVO>> queryFuzzyShopParam(@RequestParam String shopName) {
        return ResponseFactory.success(reportFormService.queryFuzzyShopParam(shopName));
    }

    @ApiOperation("模糊搜索渠道By渠道编码")
    @GetMapping("/queryFuzzyShopByNo")
    public Response<List<ShopParamVO>> queryFuzzyShopParamByNo(@RequestParam String shopNo) {
        return ResponseFactory.success(reportFormService.queryFuzzyShopParamByNo(shopNo));
    }

    @ApiOperation("调整金额")
    @PostMapping("/adjustAmount")
    public Response<Object> updateAdjustAmount(@RequestBody CardAmountUpdateDTO dto) {
        reportFormService.updateAdjustAmount(dto);
        return ResponseFactory.success();
    }

}
