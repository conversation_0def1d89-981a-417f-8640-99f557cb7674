package com.hengtiansoft.operation.order.service.impl;

import com.hengtiansoft.operation.order.service.OrderService;
import com.hengtiansoft.order.entity.dto.OrderSearchDTO;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.context.web.WebAppConfiguration;

import javax.annotation.Resource;


/**
 * <AUTHOR>
 */
@SpringBootTest
@WebAppConfiguration
@RunWith(SpringRunner.class)
@Slf4j
public class OrderServiceImplTest {
    @Resource
    private OrderService orderService;

    @Test
    void export() {
        OrderSearchDTO dto = new OrderSearchDTO();
        dto.setPageNum(1);
        dto.setPageSize(Integer.MAX_VALUE);
        orderService.export(dto);
    }
}