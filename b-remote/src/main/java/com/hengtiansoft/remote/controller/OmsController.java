package com.hengtiansoft.remote.controller;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.hengtiansoft.common.entity.vo.Response;
import com.hengtiansoft.common.factory.ResponseFactory;
import com.hengtiansoft.item.entity.dto.ChangeAmountDTO;
import com.hengtiansoft.order.adapter.OrderSyncAdapter;
import com.hengtiansoft.order.service.ReportFormService;
import com.hengtiansoft.remote.config.RemoteRequest;
import com.hengtiansoft.remote.entity.dto.ItemSkuDataDTO;
import com.hengtiansoft.remote.entity.dto.ShopParamDTO;
import com.hengtiansoft.remote.service.CardService;
import com.hengtiansoft.remote.service.MaindataService;
import com.hengtiansoft.remote.service.OmsService;
import com.hengtiansoft.thirdpart.entity.dto.oms.*;
import com.hengtiansoft.thirdpart.entity.vo.oms.OmsCardVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Author: <EMAIL>
 */
@RestController
@Api(tags = "OMS系统回调接口")
@RequestMapping("/oms")
@Slf4j
public class OmsController {
    @Autowired
    private MaindataService maindataService;
    @Autowired
    private CardService cardService;
    @Autowired
    private ReportFormService reportFormService;
    @Autowired
    private OrderSyncAdapter orderSyncAdapter;
    @Resource
    private OmsService omsService;

    @RemoteRequest
    @ApiOperation(value = "奶卡解冻")
    @PostMapping("/cardUnfreeze")
    public Response<Object> cardUnfreeze(TmallRequestDTO<OmsUnfreezeCardDTO> param){
        log.info("oms cardUnfreeze param = {}", JSON.toJSONString(param.getBody()));
        try {
            omsService.cardUnfreeze(param);
        } catch (Exception e) {
            log.error("奶卡解冻 . 出现错误 --> param: {}", JSON.toJSONString(param.getBody()), e);
            return ResponseFactory.failure(e.getMessage());
        }
        return ResponseFactory.success();
    }

    @RemoteRequest
    @ApiOperation(value = "订单发货")
    @PostMapping("/logisticsSync")
    public Response<Object> logisticsSync(TmallRequestDTO<OmsLogisticsSyncDTO> param){
        log.info("oms logisticsSync param = {}", JSON.toJSONString(param.getBody()));
        try {
            orderSyncAdapter.logisticsSync(param.getBody());
        } catch (Exception e) {
            log.error("订单发货 . 出现错误 --> param: {}", JSON.toJSONString(param.getBody()), e);
            return ResponseFactory.failure(e.getMessage());
        }
        return ResponseFactory.success();
    }

    @RemoteRequest
    @ApiOperation(value = "退货入库回传")
    @PostMapping("/refundInStorage")
    public Response<Object> refundInStorage(TmallRequestDTO<OmsRefundInStorageDTO> param){
        log.info("oms refundInStorage param = {}", JSON.toJSONString(param.getBody()));
        try {
            orderSyncAdapter.refundInStorage(param.getBody());
        } catch (Exception e) {
            log.error("退货入库回传 . 出现错误 --> param: {}", JSON.toJSONString(param.getBody()), e);
            return ResponseFactory.failure(e.getMessage());
        }
        return ResponseFactory.success();
    }

    @RemoteRequest
    @ApiOperation(value = "作废奶卡")
    @PostMapping("/cardInvalid")
    public Response<Boolean> cardInvalid(TmallRequestDTO<OmsCardInvalidDTO> param){
        log.info("oms cardInvalid param = {}", JSON.toJSONString(param.getBody()));
        try {
            boolean isSelfCard = omsService.isSelfCard(param.getBody().getShopCode(), param.getBody().getCardList());
            if(isSelfCard){
                cardService.invalid(param.getBody());
            }else{
                return omsService.cardInvalidFromCreate(param);
            }
        } catch (Exception e) {
            log.error("作废奶卡 . 出现错误 --> cardInvalidDTO: {}", JSON.toJSONString(param), e);
            return ResponseFactory.failure(e.getMessage(),false);
        }
        return ResponseFactory.success(true);
    }

    @RemoteRequest
    @ApiOperation(value = "对账金额回传")
    @PostMapping("/cardReconciliation")
    public Response<Object> cardReconciliation(TmallRequestDTO<OmsChangeAmountDTO> param){
        log.info("oms cardReconciliation param = {}", JSON.toJSONString(param.getBody()));
        try {
            boolean isSelfCard = omsService.isSelfCard(param.getBody().getShopCode(), Lists.newArrayList(param.getBody().getCardNumber()));
            if(isSelfCard){
                ChangeAmountDTO changeAmountDTO = new ChangeAmountDTO();
                changeAmountDTO.setCardNumber(param.getBody().getCardNumber());
                changeAmountDTO.setReconciliationDate(param.getBody().getReconciliationTime());
                changeAmountDTO.setReconciliationAmount(param.getBody().getReconciliationAmount());
                reportFormService.reconciliationAmount(changeAmountDTO);
            }else{
                return omsService.cardReconciliationFromCreate(param);
            }
        } catch (Exception e) {
            log.error("对账金额回传 . 出现错误 --> cardReconciliation: {}", JSON.toJSONString(param), e);
            return ResponseFactory.failure(e.getMessage(),false);
        }
        return ResponseFactory.success();
    }

    @RemoteRequest
    @ApiOperation(value = "店铺档案同步")
    @PostMapping(value = "/shop/sync")
    public Response<Boolean> shopSync(TmallRequestDTO<ShopParamDTO> shopParamDTO){
        log.info("oms shopSync param = {}", JSON.toJSONString(shopParamDTO.getBody()));
        try {
            maindataService.shopSync(shopParamDTO.getBody());
        } catch (Exception e) {
            log.error("店铺档案同步 . 出现错误 --> shopParamDTO: {}", JSON.toJSONString(shopParamDTO), e);
            return ResponseFactory.failure(e.getMessage(),false);
        }
        return ResponseFactory.success(true);
    }

    @RemoteRequest
    @ApiOperation(value = "商品档案同步")
    @PostMapping(value = "/itemSkuData/sync")
    public Response<Boolean> itemSkuDataSync(TmallRequestDTO<ItemSkuDataDTO> itemSkuDataDTO){
        log.info("oms itemSkuDataSync param = {}", JSON.toJSONString(itemSkuDataDTO.getBody()));
        try {
            maindataService.itemSkuDataSync(itemSkuDataDTO.getBody());
        } catch (Exception e) {
            log.error("商品档案同步 . 出现错误 --> itemSkuDataDTO: {}",JSON.toJSONString(itemSkuDataDTO), e);
            return ResponseFactory.failure(e.getMessage(),false);
        }
        return ResponseFactory.success(true);
    }

    @RemoteRequest
    @ApiOperation(value = "奶卡提数查询")
    @PostMapping(value = "/card/query")
    public Response<List<OmsCardVO>> cardQuery(TmallRequestDTO<OmsCardQueryDTO> param){
        log.info("oms cardQueryDTO param = {}", JSON.toJSONString(param.getBody()));
        try {
            List<OmsCardVO> omsCardVOList = omsService.cardQuery(param);
            return ResponseFactory.success(omsCardVOList);
        } catch (Exception e) {
            log.error("奶卡提数查询 . 出现错误 --> cardQueryDTO: {}",JSON.toJSONString(param.getBody()), e);
            return ResponseFactory.failure(e.getMessage());
        }
    }
}
