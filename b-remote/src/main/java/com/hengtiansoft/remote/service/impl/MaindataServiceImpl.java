package com.hengtiansoft.remote.service.impl;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.hengtiansoft.common.entity.exception.BusinessException;
import com.hengtiansoft.common.entity.vo.PageVO;
import com.hengtiansoft.common.util.BeanUtils;
import com.hengtiansoft.common.util.PageUtils;
import com.hengtiansoft.common.util.StreamUtils;
import com.hengtiansoft.item.dao.CardCategoryDao;
import com.hengtiansoft.item.dao.CardDao;
import com.hengtiansoft.item.dao.ItemSkuDataDao;
import com.hengtiansoft.item.dao.ItemSkuDataItemDao;
import com.hengtiansoft.item.entity.dto.CardDataDTO;
import com.hengtiansoft.item.entity.po.Card;
import com.hengtiansoft.item.entity.po.CardCategory;
import com.hengtiansoft.item.entity.po.ItemSkuData;
import com.hengtiansoft.item.entity.po.ItemSkuDataItem;
import com.hengtiansoft.order.dao.OrderAddressDao;
import com.hengtiansoft.order.dao.OrderAfterSaleDao;
import com.hengtiansoft.order.dao.OrderInfoDao;
import com.hengtiansoft.order.dao.OrderSkuDao;
import com.hengtiansoft.order.entity.dto.OrderDataDTO;
import com.hengtiansoft.order.entity.po.OrderAddress;
import com.hengtiansoft.order.entity.po.OrderAfterSale;
import com.hengtiansoft.order.entity.po.OrderInfo;
import com.hengtiansoft.order.entity.po.OrderSku;
import com.hengtiansoft.order.enums.OrderAfterApplyTypeEnum;
import com.hengtiansoft.remote.entity.dto.ItemSkuDataDTO;
import com.hengtiansoft.remote.entity.dto.ShopParamDTO;
import com.hengtiansoft.remote.entity.vo.CardDataVO;
import com.hengtiansoft.remote.entity.vo.OrderDataVO;
import com.hengtiansoft.remote.entity.vo.UserDataVO;
import com.hengtiansoft.remote.service.MaindataService;
import com.hengtiansoft.user.dao.CustomerUserDao;
import com.hengtiansoft.user.dao.ShopParamDao;
import com.hengtiansoft.user.entity.dto.UserDataDTO;
import com.hengtiansoft.user.entity.po.CustomerUser;
import com.hengtiansoft.user.entity.po.ShopParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;

@Service
@Slf4j
public class MaindataServiceImpl implements MaindataService {

    @Autowired
    private ShopParamDao shopParamDao;
    @Autowired
    private ItemSkuDataDao itemSkuDataDao;
    @Autowired
    private ItemSkuDataItemDao itemSkuDataItemDao;
    @Resource
    private CustomerUserDao customerUserDao;
    @Resource
    private CardDao cardDao;
    @Resource
    private CardCategoryDao cardCategoryDao;
    @Resource
    private OrderInfoDao orderInfoDao;
    @Resource
    private OrderSkuDao orderSkuDao;
    @Resource
    private OrderAddressDao orderAddressDao;
    @Resource
    private OrderAfterSaleDao orderAfterSaleDao;

    //私域店铺编号
    private static final List<String>  PRIVATE_SHOP_NO_LIST = Lists.newArrayList("200100", "200015", "200099");


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void shopSync(ShopParamDTO shopParamDTO) {
        if(StringUtils.isBlank(shopParamDTO.getShopName())){
            throw  new BusinessException("店铺名称不能为空");
        }
        ShopParam shopParamFromDB = shopParamDao.findByShopNo(shopParamDTO.getShopNum());
        if(shopParamFromDB == null){
            ShopParam shopParam = new ShopParam();
            BeanUtils.copy(shopParamDTO, shopParam);
            shopParamDao.save(shopParam);
        }else{
            BeanUtils.copy(shopParamDTO, shopParamFromDB);
            shopParamFromDB.setUpdateTime(new Date());
            shopParamDao.updateOne(shopParamFromDB);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void itemSkuDataSync(ItemSkuDataDTO itemSkuDataDTO) {
        ItemSkuData itemSkuDataFromDB = itemSkuDataDao.findBySkuCode(itemSkuDataDTO.getSkuCode());
        List<ItemSkuDataDTO.ItemSkuDataItemDTO> itemList = itemSkuDataDTO.getItemList();
        if(itemSkuDataFromDB == null){
            ItemSkuData itemSkuData = new ItemSkuData();
            BeanUtils.copy(itemSkuDataDTO, itemSkuData);
            itemSkuDataDao.save(itemSkuData);
        }else{
            BeanUtils.copy(itemSkuDataDTO, itemSkuDataFromDB);
            itemSkuDataFromDB.setUpdateTime(new Date());
            itemSkuDataDao.updateOne(itemSkuDataFromDB);
        }
        if (CollectionUtils.isNotEmpty(itemList)) {
            // 先删除 后新增
            itemSkuDataItemDao.deleteBySkuCode(itemSkuDataDTO.getSkuCode());
            itemList.forEach(item -> {
                ItemSkuDataItem itemSkuDataItem = new ItemSkuDataItem();
                BeanUtils.copy(item, itemSkuDataItem);
                itemSkuDataItem.setSkuCode(itemSkuDataDTO.getSkuCode());
                itemSkuDataItem.setCreateTime(new Date());
                itemSkuDataItem.setUpdateTime(new Date());
                itemSkuDataItem.setDelflag(0);
                itemSkuDataItemDao.save(itemSkuDataItem);
            });
        }
    }

    @Override
    public PageVO<UserDataVO> userSync(UserDataDTO userDataDTO) {
        if(!userDataDTO.isValid()){
            throw new BusinessException("客户id或者更新时间，必须有一个必填");
        }
        //最大分页数校验
        userDataDTO.validMaxPageSize();

        PageHelper.startPage(userDataDTO.getPageNum(), userDataDTO.getPageSize());
        List<CustomerUser> customerUserList = customerUserDao.findByPageDTO(userDataDTO);
        if(CollectionUtils.isEmpty(customerUserList)){
            return PageUtils.emptyPage();
        }
        return PageUtils.convert(customerUserList, data -> {
            return convertToUserDataVO(data);
        });
    }

    private UserDataVO convertToUserDataVO(CustomerUser customerUser) {
        UserDataVO userDataVO = new UserDataVO();
        userDataVO.setUserId(customerUser.getId());
        userDataVO.setOpenId(customerUser.getOpenId());
        userDataVO.setUnionId(customerUser.getUnionId());
        userDataVO.setPhone(customerUser.getPhone());
        userDataVO.setCustomerNick(customerUser.getOrderNickName());
        userDataVO.setRealName("");
        userDataVO.setAddTime(customerUser.getFirstLoginTime());
        userDataVO.setLogOff(customerUser.getLogOff());
        userDataVO.setModified(customerUser.getUpdateTime());
        return userDataVO;
    }

    @Override
    public PageVO<OrderDataVO> orderSync(OrderDataDTO orderDataDTO) {
        if(!orderDataDTO.isValid()){
            throw new BusinessException("主订单号或者更新时间，必须有一个必填");
        }
        //最大分页数校验
        orderDataDTO.validMaxPageSize();

        PageHelper.startPage(orderDataDTO.getPageNum(), orderDataDTO.getPageSize());
        List<OrderInfo> orderInfoList = orderInfoDao.findByPageDTO(orderDataDTO);
        List<String> orderNoList = StreamUtils.toList(orderInfoList, OrderInfo::getOrderNo);
        //根据主订单号查询子订单信息
        List<OrderInfo> subOrderInfoAllList = orderInfoDao.queryOrderInfoByOrderNoList(orderNoList);
        List<String> subOrderNoList = StreamUtils.toList(subOrderInfoAllList, OrderInfo::getOrderNo);
        Map<String, List<OrderInfo>> subOrderInfoMap = StreamUtils.group(subOrderInfoAllList, OrderInfo::getOrderParentNo);
        //根据子订单号查询子订单商品信息
        List<OrderSku> orderSkuList = orderSkuDao.queryOrderSkuByOrderNoList(subOrderNoList);
        Map<String, OrderSku> orderSkuMap = StreamUtils.toMap(orderSkuList, OrderSku::getOrderNo);
        //根据主订单号查询订单收货地址
        List<OrderAddress> orderAddressList = orderAddressDao.queryOrderAddressByOrderNoList(orderNoList);
        Map<String, OrderAddress> orderAddressMap = StreamUtils.toMap(orderAddressList, OrderAddress::getOrderNo);
        //根据子订单号查询售后单（主动退款/售后单）
        List<OrderAfterSale> orderAfterSaleList = orderAfterSaleDao.queryOrderAfterSaleByOrderNoList(subOrderNoList);
        Map<String, List<OrderAfterSale>> orderAfterSaleMap = StreamUtils.group(orderAfterSaleList, OrderAfterSale::getOrderNo);
        //根据主订单用户id查询用户信息
        List<CustomerUser> customerUserList = customerUserDao.findByUserIds(StreamUtils.toList(orderInfoList, OrderInfo::getUserId));
        Map<Long, CustomerUser> customerUserMap = StreamUtils.toMap(customerUserList, CustomerUser::getId);

        return PageUtils.convert(orderInfoList, order -> {
            OrderDataVO orderDataVO = new OrderDataVO();
            orderDataVO.setUserId(order.getUserId());
            CustomerUser user = customerUserMap.get(order.getUserId());
            if(Objects.nonNull(user)){
                orderDataVO.setBuyerMobile(user.getPhone());
                orderDataVO.setBuyerName("");
                orderDataVO.setBuyerAdds("");
                orderDataVO.setBuyerNick(user.getOrderNickName());
                orderDataVO.setOpenId(user.getOpenId());
                orderDataVO.setUnionId(user.getUnionId());
            }
            orderDataVO.setBuyerMark(order.getRemark());
            orderDataVO.setTradeNo(order.getOrderParentNo());
            OrderAddress orderAddress = orderAddressMap.get(order.getOrderNo());
            if(Objects.nonNull(orderAddress)){
                orderDataVO.setReceiverName(orderAddress.getReceiverName());
                orderDataVO.setReceiverProvince(orderAddress.getProvince());
                orderDataVO.setReceiverCity(orderAddress.getCity());
                orderDataVO.setReceiverDistrict(orderAddress.getDistrict());
                orderDataVO.setReceiverMobile(orderAddress.getReceiverPhone());
                orderDataVO.setReceiverAdds(orderAddress.getAddress());
            }
            orderDataVO.setTradeTime(order.getCreateTime());
            orderDataVO.setPayTime(order.getPayTime());
            orderDataVO.setModified(order.getUpdateTime());
            orderDataVO.setFinishTime(order.getConfirmTime());
            orderDataVO.setTotalDiscount(order.getProductAmount().subtract(order.getRealAmount()));
            orderDataVO.setReceivable(order.getProductAmount());
            orderDataVO.setActuPayment(order.getRealAmount());
            orderDataVO.setIsAdvanceOrder(0);
            orderDataVO.setTradeType(order.getProductType());
            orderDataVO.setOrderStatus(order.getOrderStatus());
            orderDataVO.setBelongMark("");
            if(StringUtils.isNotBlank(order.getBelongMark())){
                Map<String, String> params = new HashMap<>();
                params.put("userid", order.getBelongMark());
                params.put("open_userid", "");
                orderDataVO.setBelongMark(JSON.toJSONString(params));
            }
            List<OrderDataVO.SubTrade> subTrades = new ArrayList<>();
            List<OrderInfo> subOrderInfoList = subOrderInfoMap.get(order.getOrderParentNo());
            for (OrderInfo subOrderInfo: subOrderInfoList) {
                OrderDataVO.SubTrade subTrade = new OrderDataVO.SubTrade();
                subTrade.setSubTradeNo(subOrderInfo.getOrderNo());
                subTrade.setSubTradeType(subOrderInfo.getProductType());
                OrderSku orderSku = orderSkuMap.get(subOrderInfo.getOrderNo());
                if(Objects.nonNull(orderSku)){
                    subTrade.setGoodsId(orderSku.getProductId());
                    subTrade.setOuterId(orderSku.getSkuCode());
                    subTrade.setGoodsName(orderSku.getProductName());
                    subTrade.setPrice(orderSku.getOriginPrice());
                    subTrade.setNum(orderSku.getCount());
                    subTrade.setShareAmount(orderSku.getRealAmount());
                    subTrade.setPic(orderSku.getSkuPicUrl());
                }
                subTrade.setPostAmount(new BigDecimal("0"));
                subTrade.setYfxFee(new BigDecimal("0"));
                subTrade.setConsignTime(subOrderInfo.getDeliveryTime());
                subTrade.setSubModified(subOrderInfo.getUpdateTime());
                subTrade.setSubOrderStatus(subOrderInfo.getOrderStatus());
                subTrade.setSubRefundState(subOrderInfo.getOrderRefundStatus());

                List<OrderAfterSale> orderAfterSales = orderAfterSaleMap.get(subOrderInfo.getOrderNo());
                subTrade.setSubRefundAmount(new BigDecimal("0"));
                subTrade.setSubDifferenceAmount(new BigDecimal("0"));
                if(CollectionUtils.isNotEmpty(orderAfterSales)){
                    BigDecimal subRefundAmount = orderAfterSales.stream()
                            .filter(x -> Objects.nonNull(x.getRealAmount()) && Objects.equals(x.getApplyType(), OrderAfterApplyTypeEnum.CUSTOMER.getCode()))
                            .map(OrderAfterSale::getRealAmount)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    BigDecimal subDifferenceAmount = orderAfterSales.stream()
                            .filter(x -> Objects.nonNull(x.getRealAmount()) && Objects.equals(x.getApplyType(), OrderAfterApplyTypeEnum.KEFU.getCode()))
                            .map(OrderAfterSale::getRealAmount)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    subTrade.setSubRefundAmount(subRefundAmount);
                    subTrade.setSubDifferenceAmount(subDifferenceAmount);
                }
                subTrades.add(subTrade);
            }
            orderDataVO.setSubTrades(subTrades);
            return orderDataVO;
        });
    }

    @Override
    public PageVO<CardDataVO> cardSync(CardDataDTO cardDataDTO) {
        if(!cardDataDTO.isValid()){
            throw new BusinessException("客户id或者更新时间，必须有一个必填");
        }
        //最大分页数校验
        cardDataDTO.validMaxPageSize();

        PageHelper.startPage(cardDataDTO.getPageNum(), cardDataDTO.getPageSize());
        List<Card> cardList = cardDao.findByPageDTO(cardDataDTO);
        if(CollectionUtils.isEmpty(cardList)){
            return PageUtils.emptyPage();
        }
        List<Long> categoryIds = StreamUtils.convertDistinct(cardList, Card::getCategoryId, Long::compareTo);
        Map<Long, Integer> productCategoryMap = StreamUtils.toMap(cardCategoryDao.selectByPrimaryKey(categoryIds), CardCategory::getId, CardCategory::getProductCategory);
        return PageUtils.convert(cardList, data -> {
            CardDataVO cardDataVO = convertToCardDataVO(data);
            cardDataVO.setProductCategory(productCategoryMap.get(data.getCategoryId()));
            return cardDataVO;
        });
    }

    private CardDataVO convertToCardDataVO(Card card) {
        CardDataVO cardDataVO = new CardDataVO();
        cardDataVO.setUserId(card.getUserId());
        cardDataVO.setDomainType(PRIVATE_SHOP_NO_LIST.contains(card.getShopNo())?1:0);
        cardDataVO.setCardId(card.getId());
        cardDataVO.setCardNumber(card.getCardNumber());
        cardDataVO.setCategoryName(card.getCategoryName());
        cardDataVO.setCardCount(card.getCardCount());
        cardDataVO.setRemainingCount(card.getRemainingCount());
        cardDataVO.setExpirationTime(card.getExpirationTime());
        cardDataVO.setFinishTime(card.getFinishTime());
        cardDataVO.setReceiveTime(card.getReceiveTime());
        cardDataVO.setCreateTime(card.getCreateTime());
        cardDataVO.setCardStatus(card.getCardStatus());
        cardDataVO.setModified(card.getUpdateTime());
        return cardDataVO;
    }
}
