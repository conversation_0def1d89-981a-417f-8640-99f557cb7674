package com.hengtiansoft.mall.ai.controller;

import com.coze.openapi.client.bots.RetrieveBotResp;
import com.coze.openapi.client.chat.CancelChatResp;
import com.coze.openapi.client.common.pagination.PageResp;
import com.coze.openapi.client.connversations.CreateConversationResp;
import com.coze.openapi.client.connversations.message.model.Message;
import com.hengtiansoft.common.entity.vo.Response;
import com.hengtiansoft.common.factory.ResponseFactory;
import com.hengtiansoft.mall.ai.dto.AiGuideDTO;
import com.hengtiansoft.item.entity.dto.AiGuideProductDTO;
import com.hengtiansoft.mall.ai.service.AiGuideService;
import com.hengtiansoft.mall.ai.vo.*;
import com.hengtiansoft.thirdpart.entity.dto.coze.AiGuideChatDTO;
import com.hengtiansoft.user.entity.dto.AiGuideModuleQueryDTO;
import com.hengtiansoft.user.entity.vo.AiGuideModuleMallVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.annotation.Resource;
import java.util.List;

@RestController
@Api(tags = "智能导购")
@RequestMapping("/ai/guide")
@Slf4j
public class AiGuideController {

    @Resource
    private AiGuideService aiGuideService;

    @ApiOperation(value = "创建会话")
    @PostMapping(value = "/conversation/create")
    public Response<CreateConversationResp> conversationCreate(){
        return ResponseFactory.success(aiGuideService.conversationCreate());
    }

    @ApiOperation(value = "查看消息列表")
    @PostMapping(value = "/message/list")
    public Response<PageResp<Message>> messageList(@RequestBody AiGuideChatDTO dto){
        return ResponseFactory.success(aiGuideService.messageList(dto));
    }

    @ApiOperation(value = "消息评价")
    @PostMapping(value = "/message/rate")
    public Response<Void> messageRate(@RequestBody AiGuideChatDTO dto){
        aiGuideService.messageRate(dto);
        return ResponseFactory.success();
    }

    @ApiOperation(value = "发起对话", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    @PostMapping(value = "/chat", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public SseEmitter chat(@RequestBody AiGuideChatDTO dto){
        return aiGuideService.chat(dto);
    }

    @ApiOperation(value = "内容校验")
    @PostMapping(value = "/check")
    public Response<Void> check(@RequestBody AiGuideChatDTO dto){
        aiGuideService.check(dto);
        return ResponseFactory.success();
    }

    @ApiOperation(value = "取消进行中的对话")
    @PostMapping(value = "/chat/cancel")
    public Response<CancelChatResp> chatCancel(@RequestBody AiGuideChatDTO dto){
        return ResponseFactory.success(aiGuideService.chatCancel(dto));
    }

    @ApiOperation(value = "获取智能体配置")
    @GetMapping(value = "/getOnlineInfo")
    public Response<RetrieveBotResp> getOnlineInfo(@RequestParam String botId){
        return ResponseFactory.success(aiGuideService.getOnlineInfo(botId));
    }

    @ApiOperation(value = "用户画像信息")
    @PostMapping(value = "/getUser")
    public Response<AiGuideUserVO> getUser(@RequestBody AiGuideDTO dto){
        return ResponseFactory.success(aiGuideService.getUser(dto));
    }

    @ApiOperation(value = "用户画像信息测试")
    @PostMapping(value = "/getUserTest")
    public Response<AiGuideUserVO> getUserTest(@RequestBody AiGuideDTO dto){
        return ResponseFactory.success(aiGuideService.getUser(dto));
    }

    @ApiOperation(value = "商品动态信息")
    @PostMapping(value = "/getProduct")
    public Response<List<AiGuideProductVO>> getProduct(@RequestBody AiGuideDTO dto){
        return ResponseFactory.success(aiGuideService.getProduct(dto));
    }

    @ApiOperation(value = "活动信息")
    @PostMapping(value = "/getActivity")
    public Response<List<AiGuideActivityVO>> getActivity(@RequestBody AiGuideDTO dto){
        return ResponseFactory.success(aiGuideService.getActivity(dto));
    }

    @ApiOperation(value = "订单信息")
    @PostMapping(value = "/getOrder")
    public Response<AiGuideOrderVO> getOrder(@RequestBody AiGuideDTO dto){
        return ResponseFactory.success(aiGuideService.getOrder(dto));
    }

    @ApiOperation(value = "初始化token")
    @GetMapping(value = "/createToken")
    public Response<String> createToken(@RequestParam String code, @RequestParam String redirectUri){
        return ResponseFactory.success(aiGuideService.createToken(code, redirectUri));
    }

    @ApiOperation(value = "根据环境获取BotId")
    @GetMapping(value = "/getBotId")
    public Response<String> getBotId(@RequestParam(required = false) String code){
        return ResponseFactory.success(aiGuideService.getBotId(code));
    }

    @ApiOperation(value = "获取token")
    @GetMapping(value = "/getToken")
    public Response<String> getToken(){
        return ResponseFactory.success(aiGuideService.getToken());
    }

    @ApiOperation(value = "获取开场白")
    @GetMapping(value = "/getPrologue")
    public Response<List<String>> getPrologue(){
        return ResponseFactory.success(aiGuideService.getPrologue());
    }

    @ApiOperation(value = "配置列表")
    @PostMapping(value = "/module/getList")
    public Response<List<AiGuideModuleMallVO>> moduleList(@RequestBody AiGuideModuleQueryDTO queryDTO){
        return ResponseFactory.success(aiGuideService.moduleList(queryDTO));
    }

    @ApiOperation(value = "商品搜索")
    @PostMapping(value = "/product/query")
    public Response<List<AiGuideProductQueryVO>> productQuery(@RequestBody AiGuideProductDTO dto){
        return ResponseFactory.success(aiGuideService.productQuery(dto));
    }

}
