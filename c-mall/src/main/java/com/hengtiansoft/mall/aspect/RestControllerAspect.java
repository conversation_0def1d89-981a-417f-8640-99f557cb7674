package com.hengtiansoft.mall.aspect;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.hengtiansoft.common.entity.vo.PageVO;
import com.hengtiansoft.common.handler.GlobalExceptionHandler;
import com.hengtiansoft.security.util.UserUtil;
import com.hengtiansoft.user.entity.vo.CustomerUserVO;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Method;
import java.util.List;
import java.util.Objects;

/**
 * 请求参数、响应体统一日志打印
 * 
 * <AUTHOR>
 * @date 2019/10/14
 */
@Component
@Aspect
@Slf4j
public class RestControllerAspect {

    public static List<String> noRespUrlList = Lists.newArrayList("/product/queryByCategory", "/product/hotList", "/index/infoV2", "/index/bottom");

    /**
     * 环绕通知
     * 
     * @param joinPoint
     *            连接点
     * @return 切入点返回值
     * @throws Throwable
     *             异常信息
     */
    @Around("@within(org.springframework.web.bind.annotation.RestController) || @annotation(org.springframework.web.bind.annotation.RestController)")
    public Object apiLog(ProceedingJoinPoint joinPoint) throws Throwable {
        HttpServletRequest request =
            ((ServletRequestAttributes)RequestContextHolder.getRequestAttributes()).getRequest();
        MethodSignature signature = (MethodSignature)joinPoint.getSignature();
        Method method = signature.getMethod();
        String url = request.getServletPath() == null ? "/" : request.getServletPath();
        String requestUrl;
        if (url.contains("?")) {
            requestUrl = url.substring(0, url.indexOf("?"));
        } else {
            requestUrl = url;
        }
        String methodName = this.getMethodName(joinPoint);
        String params = this.getParamsJson(joinPoint);

        Long userId = null;
        if (UserUtil.isLogged()) {
            CustomerUserVO user = UserUtil.getDetails();
            if(Objects.nonNull(user)){
                userId = user.getId();
            }
        }

        log.info("Start Api [{}] method [{}] userId [{}] params [{}]", requestUrl, methodName, userId, params);
        long start = System.currentTimeMillis();
        Object result = joinPoint.proceed();
        log.info("End Api [{}] method [{}] params [{}], response [{}] cost [{}] millis", requestUrl, methodName,
            params, needToLog(method) && !url.contains("list") && !noRespUrlList.contains(url) ? this.deleteSensitiveContent(result) : "******",
            System.currentTimeMillis() - start);
        return result;
    }

    private String getMethodName(ProceedingJoinPoint joinPoint) {
        String methodName = joinPoint.getSignature().toShortString();
        String shortMethodNameSuffix = "(..)";
        if (methodName.endsWith(shortMethodNameSuffix)) {
            methodName = methodName.substring(0, methodName.length() - shortMethodNameSuffix.length());
        }
        return methodName;
    }

    private String getParamsJson(ProceedingJoinPoint joinPoint) {
        Object[] args = joinPoint.getArgs();
        StringBuilder sb = new StringBuilder();
        for (Object arg : args) {
            // 移除敏感内容
            String paramStr;
            if (arg instanceof HttpServletResponse) {
                paramStr = HttpServletResponse.class.getSimpleName();
            } else if (arg instanceof HttpServletRequest) {
                paramStr = HttpServletRequest.class.getSimpleName();
            } else if (arg instanceof MultipartFile) {
                long size = ((MultipartFile)arg).getSize();
                paramStr = MultipartFile.class.getSimpleName() + " size:" + size;
            }  else if (arg instanceof MultipartFile []) {
                long size = ((MultipartFile[])arg).length;
                paramStr = MultipartFile.class.getSimpleName() + " size:" + size;
            } else {
                paramStr = this.deleteSensitiveContent(arg);
            }
            sb.append(paramStr).append(",");
        }
        if (sb.length() > 1) {
            return sb.deleteCharAt(sb.length() - 1).toString();
        }
        return sb.toString();
    }

    private boolean needToLog(Method method) {
        return !method.getDeclaringClass().equals(GlobalExceptionHandler.class);
    }

    /**
     * 删除参数中的敏感内容
     * 
     * @param obj
     *            参数对象
     * @return 去除敏感内容后的参数对象
     */
    private String deleteSensitiveContent(Object obj) {
        JSONObject jsonObject = new JSONObject();
        if (obj == null || obj instanceof Exception) {
            return jsonObject.toJSONString();
        }
        if (obj instanceof PageVO) {
            return "*********";
        }
        return JSON.toJSONString(obj);
    }
}
