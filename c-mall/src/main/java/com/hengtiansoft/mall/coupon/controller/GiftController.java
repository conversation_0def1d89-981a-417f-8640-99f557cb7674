package com.hengtiansoft.mall.coupon.controller;

import com.hengtiansoft.common.entity.vo.Response;
import com.hengtiansoft.common.factory.ResponseFactory;
import com.hengtiansoft.mall.coupon.service.GiftService;
import com.hengtiansoft.order.entity.common.CouponGiftVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@Api(tags = "赠品管理接口")
@RequestMapping("/gift")
public class GiftController {

    @Autowired
    private GiftService giftService;

    @ApiOperation(value = "选择赠品")
    @GetMapping("/couponGiftList")
    public Response<List<CouponGiftVO>> couponGiftList(@RequestParam Long ruleId) {
        return ResponseFactory.success(giftService.queryCouponGift(ruleId));
    }

    @ApiOperation(value = "赠品详情")
    @GetMapping("/couponGiftDetail")
    public Response<CouponGiftVO> couponGiftDetail(@RequestParam Long skuId) {
        return ResponseFactory.success(giftService.couponGiftDetail(skuId));
    }
}
