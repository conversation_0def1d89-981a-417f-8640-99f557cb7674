
package com.hengtiansoft.mall.coupon.dto;


import com.hengtiansoft.common.entity.dto.PageParams;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class SearchCouponDTO extends PageParams {

    private List<String> stockIdList;

    @ApiModelProperty("优惠券规则id")
    private Long ruleId;

    @ApiModelProperty("商品类型")
    private String productType;

    @ApiModelProperty("排序类型，默认30天销量 1-[优惠券门槛-售价]值，负数在前，正数在后排列")
    private Integer sortType;

    private List<Long> ids;

    private Boolean userReceive;
}
