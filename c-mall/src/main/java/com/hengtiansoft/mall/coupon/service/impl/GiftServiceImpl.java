package com.hengtiansoft.mall.coupon.service.impl;

import com.hengtiansoft.common.entity.exception.BusinessException;
import com.hengtiansoft.common.util.StreamUtils;
import com.hengtiansoft.item.entity.dto.SkuProductBaseDTO;
import com.hengtiansoft.item.entity.dto.StockCalculateDTO;
import com.hengtiansoft.item.entity.po.CouponGiftRange;
import com.hengtiansoft.item.interfaces.SkuManager;
import com.hengtiansoft.item.interfaces.StockManager;
import com.hengtiansoft.mall.coupon.service.GiftService;
import com.hengtiansoft.order.entity.common.CouponGiftVO;
import com.hengtiansoft.order.manager.CouponGiftRangeManager;
import com.hengtiansoft.order.manager.CouponRuleManager;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class GiftServiceImpl implements GiftService {

    @Autowired
    private CouponGiftRangeManager couponGiftRangeManager;

    @Autowired
    private SkuManager skuManager;

    @Autowired
    private StockManager stockManager;

    @Autowired
    private CouponRuleManager couponRuleManager;



    @Override
    public List<CouponGiftVO> queryCouponGift(Long ruleId) {
        List<CouponGiftRange> couponGiftList = couponGiftRangeManager.findByRuleIdSort(ruleId);
        if(CollectionUtils.isEmpty(couponGiftList)){
            throw new BusinessException("该赠品券还没有可选赠品！");
        }
        List<Long> skuIdList = couponGiftList.stream().map(CouponGiftRange::getSkuId).collect(Collectors.toList());
        List<SkuProductBaseDTO> skuList = skuManager.skuProductNoDelList(skuIdList);
        Map<Long, SkuProductBaseDTO> skuMap = StreamUtils.toMap(skuList, SkuProductBaseDTO::getId);

        List<CouponGiftVO> couponGiftVOList = new ArrayList<>();
        for (CouponGiftRange couponGift : couponGiftList) {
            SkuProductBaseDTO sku = skuMap.get(couponGift.getSkuId());
            if(null == sku){
                continue;
            }
            CouponGiftVO couponGiftVO = new CouponGiftVO();
            couponGiftVO.setCount(couponGift.getCount());
            couponGiftVO.setPicUrl(sku.getPicUrl());
            couponGiftVO.setProductName(sku.getProductName());
            couponGiftVO.setSkuId(sku.getId());
            couponGiftVO.setSpecs(sku.getSkuSpecValues());
            couponGiftVO.setSpecValueList(sku.getSpecValueList());
            couponGiftVO.setDescription(sku.getDescription());
            couponGiftVO.setStockFlag(true);
            //库存校验
            StockCalculateDTO dto = new StockCalculateDTO();
            dto.setSkuId(couponGift.getSkuId());
            dto.setStockNum(Long.valueOf(couponGift.getCount()));
            if(CollectionUtils.isNotEmpty(stockManager.checkStock(Arrays.asList(dto)))){
                couponGiftVO.setStockFlag(false);
            }
            couponGiftVOList.add(couponGiftVO);
        }
        
        return couponGiftVOList;
    }

    @Override
    public CouponGiftVO couponGiftDetail(Long skuId) {
        List<SkuProductBaseDTO> skuList = skuManager.skuProductNoDelList(Arrays.asList(skuId));
        if(CollectionUtils.isEmpty(skuList)){
            throw new BusinessException("商品不存在");
        }else{
            List<CouponGiftVO> couponGiftVOList = new ArrayList<>();
            for (SkuProductBaseDTO sku: skuList) {
                CouponGiftVO couponGiftVO = new CouponGiftVO();
                couponGiftVO.setPicUrl(sku.getPicUrl());
                couponGiftVO.setProductName(sku.getProductName());
                couponGiftVO.setSkuId(sku.getId());
                couponGiftVO.setSpecs(sku.getSkuSpecValues());
                couponGiftVO.setDetailHtml(sku.getDetailHtml());
                couponGiftVO.setSpecValueList(sku.getSpecValueList());
                couponGiftVO.setImageUrls(sku.getImageUrls());
                couponGiftVO.setDescription(sku.getDescription());
                couponGiftVOList.add(couponGiftVO);
            }
            return couponGiftVOList.get(0);

        }
    }
}
