package com.hengtiansoft.mall.filter;

import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.Collections;
import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.hengtiansoft.common.config.ProfileConfig;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.security.authentication.AuthenticationServiceException;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.authority.SimpleGrantedAuthority;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.hengtiansoft.common.enumeration.CustomerUserStatusEnum;
import com.hengtiansoft.common.util.BeanUtils;
import com.hengtiansoft.security.adapter.AuthenticationAdapter;
import com.hengtiansoft.security.exception.BadTokenException;
import com.hengtiansoft.security.exception.DisableTokenException;
import com.hengtiansoft.security.po.UserAuthenticationToken;
import com.hengtiansoft.thirdpart.interfaces.WeChatUserInfoManager;
import com.hengtiansoft.user.entity.dto.CustomerUserPageDTO;
import com.hengtiansoft.user.entity.vo.CustomerUserVO;
import com.hengtiansoft.user.manager.CustomerUserManager;

import lombok.extern.slf4j.Slf4j;


@Slf4j
public class TestFilter extends AuthenticationAdapter<UserAuthenticationToken> {

    /**
     * 微信CODE
     */
    private static final String PHONE = "phone";


    @Autowired
    private WeChatUserInfoManager weChatUserInfoManager;

    @Autowired
    private CustomerUserManager customerUserManager;

    @Autowired
    private ProfileConfig profileConfig;

    /**
     * 构造方法
     *
     * @param pattern 登录拦截器匹配符
     */
    public TestFilter(String pattern) {
        super(pattern);
    }

    @Override
    public UserAuthenticationToken attemptAuthentication(HttpServletRequest request, HttpServletResponse response) {


        // 加密数据
        String phone;

        try {
            MediaType mediaType = extractMediaType(request.getContentType());

            if (mediaType != null && mediaType.includes(MediaType.APPLICATION_JSON)) {
                String charsetString = request.getCharacterEncoding();
                Charset charset = StringUtils.isBlank(charsetString) ?
                        StandardCharsets.UTF_8 : Charset.forName(charsetString);
                // json请求
                JSONObject loginObject = JSON.parseObject(request.getInputStream(), charset, JSONObject.class);
                phone = loginObject.getString(PHONE);
            } else {
                // x-www-form-urlencoded, query string, form-data 类型
                phone = request.getParameter(PHONE);
            }

            log.debug("用户刷新Token 测试接口 {}", phone);

        } catch (Exception e) {
            throw new AuthenticationServiceException("登录请求参数读取异常", e);


        }

        if (StringUtils.isBlank(phone)) {
            throw new AuthenticationServiceException("参数不能为空！");
        }


        return new UserAuthenticationToken(phone,null );
    }

    @Override
    public UserAuthenticationToken authenticate(Authentication authentication) {

        if (profileConfig.isProd()){
            throw new BadTokenException("功能未开启！");
        }


        String phone = authentication.getPrincipal().toString();

        CustomerUserVO customerUserVO;

        CustomerUserVO userEmpInfo;



        CustomerUserPageDTO customerUserPageDTO = new CustomerUserPageDTO();
        customerUserPageDTO.setPhone(phone);

        List<CustomerUserVO> customerUserVOS = customerUserManager.findAllByCondition(customerUserPageDTO);

        if (CollectionUtils.isNotEmpty(customerUserVOS)) {
            userEmpInfo = customerUserVOS.get(0);

            if (!userEmpInfo.getStatus().equals(CustomerUserStatusEnum.ENABLE.getCode())) {
                throw new DisableTokenException("您的账号被禁用！");
            }

        } else {
            throw new BadTokenException("您的账号未完成认证！");
            // 未认证完成
        }


        customerUserVO = BeanUtils.copy(userEmpInfo, CustomerUserVO::new);
        // 赋予权限
        SimpleGrantedAuthority authority = new SimpleGrantedAuthority("ADMIN");

        log.debug("刷新Token成功！ EmpNo {} UserName {} OpenId {}", customerUserVO.getPhone(), customerUserVO.getUserName(), customerUserVO.getOpenId());

        return new UserAuthenticationToken(
                customerUserVO.getPhone(),
                "",
                Collections.singleton(authority),
                customerUserVO);

    }


    /**
     * 提取媒体类型
     *
     * @param contentType 媒体类型描述
     * @return MediaType
     */
    private MediaType extractMediaType(String contentType) {
        if (StringUtils.isBlank(contentType)) {
            return null;
        } else {
            return MediaType.valueOf(contentType);
        }
    }
}
