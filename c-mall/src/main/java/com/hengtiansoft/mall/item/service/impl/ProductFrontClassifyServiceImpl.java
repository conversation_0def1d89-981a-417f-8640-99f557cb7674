package com.hengtiansoft.mall.item.service.impl;
import com.alibaba.fastjson.JSONObject;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.hengtiansoft.common.entity.exception.BusinessException;
import com.hengtiansoft.common.entity.vo.PageVO;
import com.hengtiansoft.common.enumeration.CommonActivityStatusEnum;
import com.hengtiansoft.common.enumeration.PeopleLimitEnum;
import com.hengtiansoft.common.enumeration.ProductFrontClassifyRelateTargetTypeEnum;
import com.hengtiansoft.common.redis.RedisOperation;
import com.hengtiansoft.common.util.BeanUtils;
import com.hengtiansoft.common.util.PageUtils;
import com.hengtiansoft.common.util.StreamUtils;
import com.hengtiansoft.item.dao.*;
import com.hengtiansoft.item.entity.dto.*;
import com.hengtiansoft.item.entity.po.*;
import com.hengtiansoft.item.enumeration.ItemCategoryLevelEnum;
import com.hengtiansoft.item.enumeration.ProductOrderByEnum;
import com.hengtiansoft.item.enumeration.ProductSaleStatusEnum;
import com.hengtiansoft.item.enumeration.ProductTypeEnum;
import com.hengtiansoft.item.interfaces.ProductManager;
import com.hengtiansoft.item.interfaces.SkuManager;
import com.hengtiansoft.item.manager.DiscountActivityManager;
import com.hengtiansoft.item.manager.ProductFrontClassifyManager;
import com.hengtiansoft.item.manager.ProductFrontClassifyRelateCategoryManager;
import com.hengtiansoft.item.manager.ProductFrontClassifyRelateItemManager;
import com.hengtiansoft.mall.freeTrial.service.FreeTrialService;
import com.hengtiansoft.mall.item.dto.ProductFrontClassifyItemPageDTO;
import com.hengtiansoft.mall.item.service.ProductFrontClassifyService;
import com.hengtiansoft.mall.item.service.ProductService;
import com.hengtiansoft.mall.item.vo.ProductFrontClassifyListVO;
import com.hengtiansoft.mall.pointAmount.util.PointCommonUtil;
import com.hengtiansoft.order.dao.CouponRangeDao;
import com.hengtiansoft.order.entity.common.ProductVO;
import com.hengtiansoft.order.entity.dto.PeopleVerifyDTO;
import com.hengtiansoft.order.entity.po.CouponRange;
import com.hengtiansoft.order.entity.po.CouponRule;
import com.hengtiansoft.order.enums.CouponChannelTypeEnum;
import com.hengtiansoft.order.enums.CouponPublicTypeEnum;
import com.hengtiansoft.order.enums.CouponStatusTypeEnum;
import com.hengtiansoft.order.enums.CouponTypeEnum;
import com.hengtiansoft.order.manager.CouponRuleManager;
import com.hengtiansoft.order.service.PeopleLimitVerifyManger;
import com.hengtiansoft.privilege.entity.dto.FreeTrialDTO;
import com.hengtiansoft.privilege.entity.po.PointAmount;
import com.hengtiansoft.privilege.entity.po.PointAmountItem;
import com.hengtiansoft.privilege.entity.vo.FreeTrialVO;
import com.hengtiansoft.privilege.enums.FreeProductTypeEnum;
import com.hengtiansoft.privilege.manager.PointAmountItemManager;
import com.hengtiansoft.privilege.manager.PointAmountManager;
import com.hengtiansoft.security.util.UserUtil;
import com.hengtiansoft.user.entity.po.CustomerUser;
import com.hengtiansoft.user.entity.vo.CustomerUserVO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @program: milk-card-server
 * @description:
 * @author: haiyang
 * @create: 2024-05-17 13:36
 **/
@Service
public class ProductFrontClassifyServiceImpl implements ProductFrontClassifyService {

    @Autowired
    private ProductFrontClassifyManager productFrontClassifyManager;
    @Autowired
    private ProductFrontClassifyDao productFrontClassifyDao;
    @Autowired
    private PeopleLimitVerifyManger peopleLimitVerifyManger;
    @Autowired
    private ProductFrontClassifyRelateCategoryManager frontClassifyRelateCategoryManager;
    @Autowired
    private ProductFrontClassifyRelateItemManager frontClassifyRelateItemManager;
    @Autowired
    private ItemCategoryDao itemCategoryDao;
    @Autowired
    private CategoryDao categoryDao;
    @Autowired
    private ProductDao productDao;
    @Autowired
    private CouponRuleManager couponRuleManager;
    @Autowired
    private CouponRangeDao couponRangeDao;
    @Autowired
    private PointAmountManager pointAmountManager;
    @Autowired
    private ProductService productService;
    @Autowired
    private PointAmountItemManager pointAmountItemManager;
    @Resource
    private RedisOperation redisOperation;
    @Resource
    private ProductManager productManager;
    @Resource
    private DiscountActivityManager discountActivityManager;
    @Resource
    private FreeTrialService freeTrialService;
    @Autowired
    private SkuManager skuManager;
    @Autowired
    private ProductAttrDao productAttrDao;


    private static final String NO_LOGGED_FRONT_CLASSIFY_INDEX = "noLogged:frontClassify:index";
    private static final String LOGGED_FRONT_CLASSIFY_INDEX_USERID = "Logged:frontClassify:index:";

    @Override
    public List<ProductFrontClassifyListVO> selectTopKList() {
        CustomerUserVO customerUser = UserUtil.isLogged() ? UserUtil.getDetails() : null;
        List<ProductFrontClassify> showFrontClassifyList = Lists.newArrayList();
        if (Objects.isNull(customerUser)) {
            String str = redisOperation.get(NO_LOGGED_FRONT_CLASSIFY_INDEX);
            if (StringUtils.isNotBlank(str)) {
                showFrontClassifyList = JSON.parseArray(str, ProductFrontClassify.class);
            } else {
                PageHelper.startPage(1, 999);
                ProductFrontClassifyListDTO productFrontClassifyListDTO = new ProductFrontClassifyListDTO();
                productFrontClassifyListDTO.setStatus(2);
                productFrontClassifyListDTO.setPeopleLimitList(Lists.newArrayList(PeopleLimitEnum.NO_LIMIT.getCode(), PeopleLimitEnum.NEW_USER.getCode()));
                List<ProductFrontClassify> productFrontClassifyList = productFrontClassifyManager.findByCondition(productFrontClassifyListDTO);
                for (ProductFrontClassify classify : productFrontClassifyList) {
                    if (showFrontClassifyList.size() >= 15) break;
                    showFrontClassifyList = matchFrontClassify(classify, showFrontClassifyList);
                }
                redisOperation.setnx(NO_LOGGED_FRONT_CLASSIFY_INDEX, JSON.toJSONString(showFrontClassifyList), 60L, TimeUnit.SECONDS);
            }
        } else {
            Long userId = customerUser.getId();
            String str = redisOperation.get(LOGGED_FRONT_CLASSIFY_INDEX_USERID + userId);
            if (StringUtils.isNotBlank(str)) {
                showFrontClassifyList = JSON.parseArray(str, ProductFrontClassify.class);
            } else {
                PageHelper.startPage(1, 999);
                ProductFrontClassifyListDTO productFrontClassifyListDTO = new ProductFrontClassifyListDTO();
                productFrontClassifyListDTO.setStatus(2);
                List<ProductFrontClassify> allProductFrontClassifyList = productFrontClassifyDao.findByCondition(productFrontClassifyListDTO);
                for (ProductFrontClassify classify : allProductFrontClassifyList) {
                    if (showFrontClassifyList.size() >= 15) break;
                    PeopleVerifyDTO verifyDTO = new PeopleVerifyDTO();
                    verifyDTO.setPeopleLimit(classify.getPeopleLimit());
                    verifyDTO.setGrade(classify.getGrade());
                    String labelId = classify.getLabelId();
                    if (StringUtils.isNotBlank(labelId)) {
                        List<Long> labelIds = Arrays.stream(labelId.split(",")).map(Long::parseLong).collect(Collectors.toList());
                        verifyDTO.setLabelIds(labelIds);
                    }
                    boolean verifyFlag = peopleLimitVerifyManger.peopleLimitVerify(verifyDTO, BeanUtil.copyProperties(customerUser, CustomerUser.class));
                    if (verifyFlag) {
                        showFrontClassifyList = matchFrontClassify(classify, showFrontClassifyList);
                    }
                }
                redisOperation.setnx(LOGGED_FRONT_CLASSIFY_INDEX_USERID + userId, JSON.toJSONString(showFrontClassifyList), 60L, TimeUnit.SECONDS);
            }
        }
        return assembleClassifyVO(showFrontClassifyList);
    }


    private List<ProductFrontClassifyListVO> assembleClassifyVO(List<ProductFrontClassify> classifyList) {
        List<ProductFrontClassifyListVO> classifyVOList = new ArrayList<>();
        for (ProductFrontClassify classify: classifyList) {
            ProductFrontClassifyListVO vo = new ProductFrontClassifyListVO();
            vo.setId(classify.getId());
            vo.setName(classify.getName());
            vo.setDescription(classify.getDescription());
            vo.setHasBanner(classify.getHasBanner());
            vo.setBannerUrl(classify.getBannerUrl());
            vo.setIcon(classify.getIcon());
            vo.setLinkType(classify.getLinkType());
            vo.setLink(classify.getLink());
            vo.setAppId(classify.getAppId());
            vo.setType(classify.getType());
            vo.setPicData(JSON.parseObject(classify.getPicData()));
            classifyVOList.add(vo);
        }
        return classifyVOList;
    }

    @Override
    public PageVO<ProductVO> hotSaleItem(ProductFrontClassifyItemPageDTO dto) {
        Long productId = dto.getProductId();
        if(Objects.isNull(productId)){
            throw new BusinessException("商品id不能为空");
        }
        Product product = productManager.findById(productId);
        if(Objects.isNull(product)){
            throw new BusinessException("商品不存在");
        }
        Long cateId = product.getCateId();
        Category category = itemCategoryDao.findById(cateId);
        if(Objects.isNull(category)){
            throw new BusinessException("分类不存在");
        }
        ProductBaseSearchDTO searchDTO = new ProductBaseSearchDTO();
        searchDTO.setCateLevel(ItemCategoryLevelEnum.FIRSTCATE.getCode());
        searchDTO.setCateIds(Arrays.asList(category.getParentId()));
        searchDTO.setSortType(ProductOrderByEnum.HOT_LIST_ASC.getCode());
        searchDTO.setPageSize(dto.getPageSize());
        searchDTO.setPageNum(dto.getPageNum());
        searchDTO.setProductTypeList(Arrays.asList(ProductTypeEnum.PACKAGE.getCode(),ProductTypeEnum.SINGLE_PRODUCT.getCode(),ProductTypeEnum.ENTITY_CARD.getCode()));
        searchDTO.setEnableShow(1);
        searchDTO.addSaleStatus(ProductSaleStatusEnum.ON_SHELVES.getCode());
        List<Product> productList = productManager.selectByConditionSort(searchDTO);
        List<Long> productIds = StreamUtils.toList(productList, Product::getId);
        Map<Long, List<SkuBaseDTO>> skuMap = skuManager.getAllSkuMapByProdcutIds(productIds, true);
        List<ProductBaseDTO> productBaseDTOList = BeanUtils.copyList(productList, ProductBaseDTO::new);
        for (ProductBaseDTO baseDTO : productBaseDTOList) {
            baseDTO.setSkus(skuMap.get(baseDTO.getId()));
        }
        List<ProductVO> productVOList = BeanUtils.deepListCopy(productBaseDTOList, ProductVO.class);
        productService.buildMarketing(productVOList);
        return PageUtils.toPageVO(PageUtils.extract(productList), productVOList);
    }

    @Override
    public PageVO<ProductVO> selectItemList(ProductFrontClassifyItemPageDTO pageDTO) {
        Long productFrontClassifyId = pageDTO.getId();
        ProductFrontClassify frontClassify = productFrontClassifyManager.getOne(productFrontClassifyId);
        Assert.notNull(frontClassify, "该分类不存在");
        Integer relateTargetType = frontClassify.getRelateTargetType();

        List<Long> qryCategoryIds = Lists.newArrayList();
        List<Long> qryProductIds = Lists.newArrayList();
        if (relateTargetType.equals(ProductFrontClassifyRelateTargetTypeEnum.CATEGORY.getCode())) {
            List<ProductFrontClassifyRelateCategory> relateCategoryList = frontClassifyRelateCategoryManager.findByClassifyId(productFrontClassifyId);
            List<Long> categoryIds = relateCategoryList.stream().map(ProductFrontClassifyRelateCategory::getCategoryId).collect(Collectors.toList());
            List<Category> categories = itemCategoryDao.listByIds(categoryIds);
            Map<Integer, List<Category>> categoryLevelGroup = categories.stream().collect(Collectors.groupingBy(Category::getCateLevel));
            for (Map.Entry<Integer, List<Category>> entry : categoryLevelGroup.entrySet()) {
                Integer level = entry.getKey();
                List<Category> categoryList = entry.getValue();
                List<Long> cateIds = categoryList.stream().map(Category::getId).collect(Collectors.toList());
                if (level.equals(ItemCategoryLevelEnum.SECONDCATE.getCode())) {
                    qryCategoryIds.addAll(cateIds);
                } else {
                    List<Long> subCateIds = categoryDao.selectByParId(cateIds);
                    qryCategoryIds.addAll(subCateIds);
                }
            }
            if (qryCategoryIds.isEmpty()) {
                return PageUtils.emptyPage();
            }
        }
        if (relateTargetType.equals(ProductFrontClassifyRelateTargetTypeEnum.PRODUCT.getCode())) {
            List<ProductFrontClassifyRelateItem> relateItemList = frontClassifyRelateItemManager.findByClassifyId(productFrontClassifyId);
            List<Long> productIds = relateItemList.stream().map(ProductFrontClassifyRelateItem::getProductId).collect(Collectors.toList());
            qryProductIds.addAll(productIds);
            if (qryProductIds.isEmpty()) {
                return PageUtils.emptyPage();
            }
        }
        ProductBaseSearchDTO dto = new ProductBaseSearchDTO();
        dto.setCateIds(qryCategoryIds);
        dto.setProductIds(qryProductIds);
        dto.setProductTypeList(Lists.newArrayList("c","d","s"));
        dto.setSortType(pageDTO.getSortCode());
        PageHelper.startPage(pageDTO.getPageNum(), pageDTO.getPageSize());
        List<Product> productList = productDao.selectByConditionSort(dto);

        List<ProductBaseDTO> baseDTOs = BeanUtils.copyList(productList, ProductBaseDTO::new);
        List<Long> productIds = StreamUtils.toList(productList, Product::getId);
        Map<Long, List<SkuBaseDTO>> skuMap = skuManager.getAllSkuMapByProdcutIds(productIds, true);
        Map<Long, List<ProductAttrDTO>> attrMap = StreamUtils.mapGroup(productAttrDao.selectByProductIds(productIds),
                e -> BeanUtils.copy(e, ProductAttrDTO::new), ProductAttrDTO::getProductId);
        for (ProductBaseDTO baseDTO : baseDTOs) {
            baseDTO.setSkus(skuMap.get(baseDTO.getId()));
            baseDTO.setProductAttrs(attrMap.get(baseDTO.getId()));
        }
        List<ProductBaseDTO> productBaseDTOS = productManager.buildProductFisrtMapCate(baseDTOs);

        List<ProductVO> productVoList = BeanUtils.deepListCopy(productBaseDTOS, ProductVO.class);

        //加载优惠券信息
        CouponRuleListDTO ruleDto = new CouponRuleListDTO();
        ruleDto.setChannel(CouponChannelTypeEnum.WECHAT_MINI.getCode());
        ruleDto.setIsPublic(CouponPublicTypeEnum.PUBLIC.getCode());
        ruleDto.setStatus(CouponStatusTypeEnum.ONLINE.getCode());
        ruleDto.setEndTimeStart(new Date());
        ruleDto.setCouponType(CouponTypeEnum.PRODUCT_COUPON.getCode());

        CustomerUserVO user = UserUtil.isLogged() ? UserUtil.getDetails() : null;

        //查询可领+已领的优惠券
        List<CouponRule> couponRuleList = couponRuleManager.queryEnableReceiveAndReceivedCoupon(ruleDto, user);
        List<Long> ruleIds = StreamUtils.convert(couponRuleList, CouponRule::getId);
        List<CouponRange> couponRangeList = couponRangeDao.queryByRuleIds(ruleIds);
        Map<Long, List<CouponRange>> couponRangeMap = StreamUtils.group(couponRangeList, CouponRange::getRuleId);

        List<PointAmount> pointAmountList = pointAmountManager.getInProgressList();
        List<PointAmountItem> pointAmountItemList = pointAmountItemManager.findByPointAmountIds(StreamUtils.convert(pointAmountList, PointAmount::getId));
        Map<Long, List<PointAmountItem>> pointAmountItemMap = StreamUtils.group(pointAmountItemList, PointAmountItem::getPointAmountId);
        // TopK处理
        productService.rankCategoryItemHotSale(productVoList);
        for (ProductVO productVO : productVoList) {
            productVO.setCurrentTime(System.currentTimeMillis());
            //促销活动信息
            productService.buildProdctDiscountActivity(productVO, user, true, null);
            //满减满送
            productService.buildProductFullReduce(productVO, user, false, null, 1, null, productVO.getCycleFlag());
            //商品上装载 优惠券信息+券后价（数量为1计算）
            productService.buildProdctAllCouponList(productVO, couponRuleList, couponRangeMap);
            //装载积分抵现活动
            productService.buildProductPointAmount(productVO, user, pointAmountList, pointAmountItemMap);
            //装载商品标签
            PointCommonUtil.buildProductLabel(productVO, 1, null);
            //拼团活动信息
            productService.buildProductPtActivity(productVO, null);
        }
        return PageUtils.toPageVO(productVoList);
    }

    @Override
    public PageVO<ProductVO> discountItem(ProductFrontClassifyItemPageDTO pageDTO) {
        DiscountActivityDTO discountActivityDTO = new DiscountActivityDTO();
        discountActivityDTO.setStatus(CommonActivityStatusEnum.IN_PROGRESS.getCode());
        List<DiscountActivity> discountActivityList = discountActivityManager.findByCondition(discountActivityDTO);
        List<Long> discountActivityIds = StreamUtils.toList(discountActivityList, DiscountActivity::getId);
        ProductBaseSearchDTO searchDTO = new ProductBaseSearchDTO();
        searchDTO.setDiscountActivityIds(discountActivityIds);
        searchDTO.setSortType(pageDTO.getSortCode());
        searchDTO.setPageSize(pageDTO.getPageSize());
        searchDTO.setPageNum(pageDTO.getPageNum());
        searchDTO.setProductTypeList(Arrays.asList(ProductTypeEnum.PACKAGE.getCode(),ProductTypeEnum.SINGLE_PRODUCT.getCode(),ProductTypeEnum.ENTITY_CARD.getCode()));
        searchDTO.setEnableShow(1);
        searchDTO.addSaleStatus(ProductSaleStatusEnum.ON_SHELVES.getCode());
        List<Product> productList = productManager.selectByConditionSort(searchDTO);
        List<Long> productIds = StreamUtils.toList(productList, Product::getId);
        Map<Long, List<SkuBaseDTO>> skuMap = skuManager.getAllSkuMapByProdcutIds(productIds, true);
        List<ProductBaseDTO> productBaseDTOList = BeanUtils.copyList(productList, ProductBaseDTO::new);
        for (ProductBaseDTO baseDTO : productBaseDTOList) {
            baseDTO.setSkus(skuMap.get(baseDTO.getId()));
        }
        List<ProductVO> productVOList = BeanUtils.deepListCopy(productBaseDTOList, ProductVO.class);
        productService.buildMarketing(productVOList);
        return PageUtils.toPageVO(PageUtils.extract(productList), productVOList);
    }

    @Override
    public PageVO<FreeTrialVO> freeTrialItem(ProductFrontClassifyItemPageDTO pageDTO) {
        FreeTrialDTO dto = new FreeTrialDTO();
        dto.setStatusList(Arrays.asList(CommonActivityStatusEnum.END.getCode()));
        dto.setType(FreeProductTypeEnum.FREE.getCode());
        dto.setPageNum(pageDTO.getPageNum());
        dto.setPageSize(pageDTO.getPageSize());
        dto.setHasTrialProduct(true);
        return freeTrialService.getList(dto);
    }

    private List<ProductFrontClassify> matchFrontClassify(ProductFrontClassify classify, List<ProductFrontClassify> showFrontClassifyList) {
        Integer relateTargetType = classify.getRelateTargetType();
        if (Objects.nonNull(relateTargetType) && relateTargetType.equals(ProductFrontClassifyRelateTargetTypeEnum.CATEGORY.getCode())) {
            List<ProductFrontClassifyRelateCategory> classifyRelateCategories = frontClassifyRelateCategoryManager.findByClassifyId(classify.getId());
            // 类目是否公开 0 公开 1不公开
            List<Long> cateIds = classifyRelateCategories.stream().map(ProductFrontClassifyRelateCategory::getCategoryId).collect(Collectors.toList());
            List<Category> categoryList = itemCategoryDao.listByIds(cateIds);
            if (CollectionUtils.isNotEmpty(categoryList) && categoryList.stream().allMatch(e -> e.getIsPublic() == 1)) {
                // 分类全部不公开就不展示
                return showFrontClassifyList;
            }
            List<Long> parentIds = categoryList.stream().filter(e -> Objects.isNull(e.getParentId()) && e.getIsPublic() == 0).map(Category::getId).collect(Collectors.toList());
            List<Long> subCateIds = categoryList.stream().filter(e -> Objects.nonNull(e.getParentId()) && e.getIsPublic() == 0).map(Category::getId).collect(Collectors.toList());
            List<Long> filterSubCateIds = categoryDao.selectByParId(parentIds);
            subCateIds.addAll(filterSubCateIds);
            ProductBaseSearchDTO baseSearchDTO = new ProductBaseSearchDTO();
            baseSearchDTO.setProductTypeList(Lists.newArrayList("c","d","s"));
            baseSearchDTO.addSaleStatus(1);
            baseSearchDTO.setEnableShow(1);
            baseSearchDTO.setCateIds(subCateIds);
            List<Long> filterProductIds = new ArrayList<>();
            if(CollectionUtils.isNotEmpty(subCateIds)){
                filterProductIds = productDao.findIdsByCondition(baseSearchDTO);
            }
            if (CollectionUtils.isNotEmpty(filterProductIds) || Objects.equals(classify.getType(), 1)) {
                showFrontClassifyList.add(classify);
            }

        } else {
            List<ProductFrontClassifyRelateItem> itemList = frontClassifyRelateItemManager.findByClassifyId(classify.getId());
            List<Long> productIds = itemList.stream().map(ProductFrontClassifyRelateItem::getProductId).collect(Collectors.toList());
            List<Product> productList = productDao.checkOnSaleByIds(productIds);
            if (CollectionUtils.isNotEmpty(productList)|| Objects.equals(classify.getType(), 1)) {
                showFrontClassifyList.add(classify);
            }
        }
        return showFrontClassifyList;
    }
}
