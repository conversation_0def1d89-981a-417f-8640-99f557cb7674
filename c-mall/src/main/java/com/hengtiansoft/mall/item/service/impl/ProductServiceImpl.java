/*
 * Project Name: operation
 * File Name: ProductServiceImpl.java
 * Class Name: ProductServiceImpl
 *
 * Copyright 2014 Hengtian Software Inc
 *
 * Licensed under the Hengtiansoft
 *
 * http://www.hengtiansoft.com
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or
 * implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.hengtiansoft.mall.item.service.impl;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.hengtiansoft.common.constant.PageConstants;
import com.hengtiansoft.common.entity.dto.PageParams;
import com.hengtiansoft.common.entity.dto.Pagination;
import com.hengtiansoft.common.entity.exception.BusinessException;
import com.hengtiansoft.common.entity.vo.PageVO;
import com.hengtiansoft.common.enumeration.*;
import com.hengtiansoft.common.redis.RedisOperation;
import com.hengtiansoft.common.util.*;
import com.hengtiansoft.item.dao.*;
import com.hengtiansoft.item.entity.dto.*;
import com.hengtiansoft.item.entity.po.*;
import com.hengtiansoft.item.entity.vo.CategoryListVO;
import com.hengtiansoft.item.entity.vo.DiscountActivityVO;
import com.hengtiansoft.item.entity.vo.GroupTemplateVO;
import com.hengtiansoft.item.entity.vo.SkuSaleStockVO;
import com.hengtiansoft.item.enumeration.*;
import com.hengtiansoft.item.interfaces.GroupTemplateManager;
import com.hengtiansoft.item.interfaces.ProductManager;
import com.hengtiansoft.item.interfaces.SkuManager;
import com.hengtiansoft.item.interfaces.StockManager;
import com.hengtiansoft.item.manager.CardCategoryManager;
import com.hengtiansoft.item.manager.ItemCategoryManager;
import com.hengtiansoft.item.manager.ItemDisplayCaseProManager;
import com.hengtiansoft.item.utils.DiscountUtil;
import com.hengtiansoft.item.utils.ProductUtil;
import com.hengtiansoft.item.utils.SkuUtil;
import com.hengtiansoft.mall.coupon.dto.SearchCouponDTO;
import com.hengtiansoft.mall.coupon.vo.ProductPriceVO;
import com.hengtiansoft.mall.fullReduce.dto.FullSkuWarp;
import com.hengtiansoft.mall.fullReduce.service.FullReduceService;
import com.hengtiansoft.mall.item.dto.ProductCouponGapDTO;
import com.hengtiansoft.mall.item.dto.ProductOrderDTO;
import com.hengtiansoft.mall.item.service.ProductMediaService;
import com.hengtiansoft.mall.item.service.ProductService;
import com.hengtiansoft.mall.item.service.SkuService;
import com.hengtiansoft.mall.item.service.SpecService;
import com.hengtiansoft.mall.item.vo.ProductPromoVO;
import com.hengtiansoft.mall.item.vo.SkuPopVO;
import com.hengtiansoft.mall.pintuan.service.PtActivityService;
import com.hengtiansoft.mall.pointAmount.service.PointAmountService;
import com.hengtiansoft.mall.pointAmount.util.PointCommonUtil;
import com.hengtiansoft.order.dao.CouponInfoDao;
import com.hengtiansoft.order.dao.CouponRangeDao;
import com.hengtiansoft.order.entity.common.*;
import com.hengtiansoft.order.entity.dto.ProductCouponDTO;
import com.hengtiansoft.order.entity.po.CouponInfo;
import com.hengtiansoft.order.entity.po.CouponRange;
import com.hengtiansoft.order.entity.po.CouponRule;
import com.hengtiansoft.order.enums.*;
import com.hengtiansoft.order.manager.CouponRuleManager;
import com.hengtiansoft.order.service.CommonOrderOptService;
import com.hengtiansoft.order.util.CouponUtil;
import com.hengtiansoft.privilege.dao.*;
import com.hengtiansoft.privilege.entity.dto.FullReduceMallDTO;
import com.hengtiansoft.privilege.entity.po.*;
import com.hengtiansoft.privilege.entity.vo.FullReduceInfo;
import com.hengtiansoft.privilege.entity.vo.PrivilegeItemUserVO;
import com.hengtiansoft.privilege.entity.vo.PrivilegeLableVO;
import com.hengtiansoft.privilege.enums.*;
import com.hengtiansoft.privilege.manager.*;
import com.hengtiansoft.privilege.util.DiscountActivityUtil;
import com.hengtiansoft.security.util.UserUtil;
import com.hengtiansoft.thirdpart.entity.dto.wechat.WechatShortLinkResp;
import com.hengtiansoft.thirdpart.interfaces.BaiduManager;
import com.hengtiansoft.thirdpart.interfaces.WeChatMiniCouponManager;
import com.hengtiansoft.thirdpart.interfaces.WeChatMiniSmsManager;
import com.hengtiansoft.user.dao.WorkWxSalesDao;
import com.hengtiansoft.user.entity.po.WorkWxSales;
import com.hengtiansoft.user.entity.vo.CustomerUserVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * Class Name: ProductServiceImpl Description:
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class ProductServiceImpl implements ProductService {

    @Autowired
    private ProductManager productManager;
    @Autowired
    private ProductMediaService productMediaService;
    @Autowired
    private SkuService skuService;
    @Autowired
    private SpecService specService;
    @Autowired
    private ItemDisplayCaseProManager itemDisplayCaseProManager;
    @Autowired
    private StockManager stockManager;
    @Autowired
    private CardCategoryManager cardCategoryManager;
    @Autowired
    private PrivilegeActManager privilegeActManager;
    @Autowired
    private CouponRuleManager couponRuleManager;
    @Autowired
    private RedisOperation redisOperation;
    @Autowired
    private ItemCategoryManager itemCategoryManager;
    @Autowired
    private CouponInfoDao couponInfoDao;
    @Autowired
    private CouponRangeDao couponRangeDao;
    @Resource
    private SkuManager skuManager;
    @Resource
    private HotProductDao hotProductDao;
    @Resource
    private FullReduceManager fullReduceManager;
    @Resource
    private FullReduceService fullReduceService;
    @Resource
    private FullReduceRuleDao fullReduceRuleDao;
    @Resource
    private FullReduceRuleRangeDao fullReduceRuleRangeDao;
    @Resource
    private PointAmountManager pointAmountManager;
    @Resource
    private PointAmountService pointAmountService;
    @Resource
    private PointAmountItemManager pointAmountItemManager;
    @Resource
    private FreeTrialCommentManager freeTrialCommentManager;
    @Resource
    private PtActivityManager ptActivityManager;
    @Resource
    private PtGoodsManager ptGoodsManager;
    @Resource
    private PtActivityService ptActivityService;
    @Resource
    private CategoryDao categoryDao;
    @Resource
    private DiscountActivityRangeDao discountActivityRangeDao;
    @Resource
    private CommonOrderOptService commonOrderOptService;
    @Resource
    private FreeTrialSubActivityProductDao freeTrialSubActivityProductDao;
    @Resource
    private FullReduceProductRangeDao fullReduceProductRangeDao;
    @Resource
    private ItemProductAddonsDao itemProductAddonsDao;
    @Resource
    private ProductDao productDao;
    @Resource
    private PrivilegeItemUserDao privilegeItemUserDao;
    @Resource
    private ProductAttrDao productAttrDao;
    @Resource
    private WorkWxSalesDao workWxSalesDao;
    @Resource
    private BaiduManager baiduManager;
    @Value("${milk.local.h5Domain}")
    private String localH5Domain;
    @Resource
    private WeChatMiniSmsManager weChatMiniSmsManager;
    @Resource
    private GroupTemplateManager groupTemplateManager;
    @Resource
    private WeChatMiniCouponManager weChatMiniCouponManager;


    @Override
    public PageVO<ProductVO> search(ProductSearchDTO dto) {
        if (Objects.nonNull(dto.getCardType()) && !dto.getCardType().equals(0)) {
            Integer numByCode = CardCategoryTypeEnum.getNumByCode(dto.getCardType());
            List<Long> cardCategories = cardCategoryManager.selectByCategoryCount(numByCode);
            dto.setCardCategoryIds(cardCategories);
        } else if (Objects.nonNull(dto.getCardType()) && dto.getCardType().equals(0)) {
            List<Long> cardCategories = cardCategoryManager.findByECardSpecialSql(Arrays.asList(CardCategoryTypeEnum.year.getNum(), CardCategoryTypeEnum.quarter.getNum()));
            dto.setCardCategoryIds(cardCategories);
        }
        //特权商品
        if (Objects.nonNull(dto.getPrivilegeFlag()) && PrivilegeFlagEnum.Privilege.getCode().equals(dto.getPrivilegeFlag())) {
            Date date = new Date();
            CustomerUserVO details = UserUtil.getDetails();
            List<PrivilegeItemUserVO> itemUserVOS = privilegeActManager.findByUserIds(Collections.singletonList(details.getId()));
            if (CollectionUtils.isNotEmpty(itemUserVOS)) {
                List<Long> labelIds = itemUserVOS.stream().map(PrivilegeItemUserVO::getLableId).collect(Collectors.toList());
                List<PrivilegeLableVO> privilegeLablesById = privilegeActManager.findPrivilegeLablesById(labelIds);
                if (CollectionUtils.isNotEmpty(privilegeLablesById)) {
                    List<Long> ids = privilegeLablesById.stream().filter(privilegeLableVO -> privilegeLableVO.getStartTime().before(date) && privilegeLableVO.getEndTime().after(date)
                            && privilegeLableVO.getStatus().equals(1)).map(PrivilegeLableVO::getId).collect(Collectors.toList());
                    List<PrivilegeItemUserVO> privilegeLables = privilegeActManager.findPrivilegeLables(ids, 1);
                    if (CollectionUtils.isNotEmpty(privilegeLables)) {
                        List<Long> itemIds = privilegeLables.stream().map(PrivilegeItemUserVO::getValueId).collect(Collectors.toList());
                        dto.setProductIds(itemIds);
                    } else {
                        return PageUtils.emptyPage(dto);
                    }
                } else {
                    return PageUtils.emptyPage(dto);
                }
            } else {
                return PageUtils.emptyPage(dto);
            }
        }
        dto.setSkuFlag(true);
        dto.addSaleStatus(ProductSaleStatusEnum.ON_SHELVES.getCode());
        if (StringUtils.isNotEmpty(dto.getSkuCode())) {
            List<Long> skuIds = skuService.findIdsBySkuCode(dto.getSkuCode());
            if (CollectionUtils.isEmpty(skuIds)) {
                return PageUtils.emptyPage(dto);
            }
            dto.setSkuIds(skuIds);
        }
        if (StringUtils.isNotEmpty(dto.getOwnerId())) {
            Map<String, String> skuAttrMap = new HashMap<String, String>();
            skuAttrMap.put("ownerId", dto.getOwnerId());
            dto.setSkuAttrMap(skuAttrMap);
        }
        if (dto.getDisplayCaseId() != null) {
            List<DisplayCaseProduct> displayCaseProducts = itemDisplayCaseProManager
                    .selectByDisplayCaseId(dto.getDisplayCaseId());
            List<Long> displayProIds = displayCaseProducts.stream().map(DisplayCaseProduct::getProductId)
                    .collect(Collectors.toList());
            dto.setProductIds(displayProIds);
        }

        PageVO<ProductBaseDTO> basePage = productManager.search(dto);
        if (0 == basePage.getTotal()) {
            return PageUtils.emptyPage(dto);
        }


        List<ProductVO> productVoList = BeanUtils.deepListCopy(basePage.getList(), ProductVO.class);
        return PageUtils.build(basePage.getPagination(), productVoList);
    }

    @Override
    @Deprecated
    public Map<Long, List<ProductVO>> searchAll(ProductSearchDTO dto) {
        dto.setPrivilegeFlag(PrivilegeFlagEnum.GENERAL.getCode());
        //特权商品
        if(UserUtil.isLogged()){
            Date date = new Date();
            CustomerUserVO details = UserUtil.getDetails();
            List<PrivilegeItemUserVO> itemUserVOS = privilegeActManager.findByUserIds(Collections.singletonList(details.getId()));
            if (CollectionUtils.isNotEmpty(itemUserVOS)) {
                List<Long> labelIds = itemUserVOS.stream().map(PrivilegeItemUserVO::getLableId).collect(Collectors.toList());
                List<PrivilegeLableVO> privilegeLablesById = privilegeActManager.findPrivilegeLablesById(labelIds);
                if (CollectionUtils.isNotEmpty(privilegeLablesById)) {
                    List<Long> ids = privilegeLablesById.stream().filter(privilegeLableVO -> privilegeLableVO.getStartTime().before(date) && privilegeLableVO.getEndTime().after(date)
                            && privilegeLableVO.getStatus().equals(1)).map(PrivilegeLableVO::getId).collect(Collectors.toList());
                    List<PrivilegeItemUserVO> privilegeLables = privilegeActManager.findPrivilegeLables(ids, 1);
                    if (CollectionUtils.isNotEmpty(privilegeLables)) {
                        List<Long> itemIds = privilegeLables.stream().map(PrivilegeItemUserVO::getValueId).collect(Collectors.toList());
                        dto.setProductIds(itemIds);
                        dto.setPrivilegeFlag(PrivilegeFlagEnum.Privilege.getCode());
                    }
                }
            }
        }

        dto.setSkuFlag(true);
        dto.addSaleStatus(ProductSaleStatusEnum.ON_SHELVES.getCode());

        List<ProductBaseDTO> baseList = productManager.searchList(dto, false);
        List<ProductBaseDTO> list1 = new ArrayList<>(baseList);
        if(CollectionUtils.isNotEmpty(dto.getProductIds())){
            dto.setProductIds(null);
            dto.setPrivilegeFlag(PrivilegeFlagEnum.GENERAL.getCode());
            List<ProductBaseDTO> baseDTOList = productManager.searchList(dto, false);
            List<ProductBaseDTO> list2 = new ArrayList<>(baseDTOList);
            if(CollectionUtils.isNotEmpty(list2)){
               list1.addAll(list2);
            }
        }

        List<ProductBaseDTO> baseDTOList = productManager.buildProductFisrtMapCate(list1);

        List<ProductVO> productVoList = BeanUtils.deepListCopy(baseDTOList, ProductVO.class);

        //加载优惠券信息
        if (dto.isCouponFlag()) {
            CouponRuleListDTO ruleDto = new CouponRuleListDTO();
            ruleDto.setChannel(CouponChannelTypeEnum.WECHAT_MINI.getCode());
            ruleDto.setIsPublic(CouponPublicTypeEnum.PUBLIC.getCode());
            ruleDto.setStatus(CouponStatusTypeEnum.ONLINE.getCode());
            ruleDto.setEndTimeStart(new Date());
            ruleDto.setCouponType(CouponTypeEnum.PRODUCT_COUPON.getCode());
            CustomerUserVO user = null;
            if(UserUtil.isLogged()){
                user = UserUtil.getDetails();
            }
            //查询可领+已领的优惠券
            List<CouponRule> couponRuleList = couponRuleManager.queryEnableReceiveAndReceivedCoupon(ruleDto, user);
            List<Long> ruleIds = StreamUtils.convert(couponRuleList, CouponRule::getId);
            //优惠券商品范围
            List<CouponRange> couponRangeList = couponRangeDao.queryByRuleIds(ruleIds);
            Map<Long,List<CouponRange>> couponRangeMap = StreamUtils.group(couponRangeList, CouponRange::getRuleId);
            for (ProductVO productVO : productVoList) {
                //商品上装载 优惠券信息+券后价（以第一个sku，数量为1计算）
                buildProdctAllCouponList(productVO, couponRuleList, couponRangeMap);
            }
        }
        Map<Long, List<ProductVO>> allProductMap = StreamUtils.group(productVoList,ProductVO::getFirstCateId);

        //周期购类目
        List<ProductVO> productCycleList = productVoList.stream().filter(productVO -> ProductCycleEnum.CYCLE.getCode().equals(productVO.getCycleFlag())).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(productCycleList)){
            List<CategoryListVO> categoryListVOList = itemCategoryManager.categoryByCateType(ItemCategoryTypeEnum.CYCLE.getCode(),PublicEnum.PUBLIC.getCode());
            if(CollectionUtils.isNotEmpty(categoryListVOList)){
                allProductMap.put(StreamUtils.getFirst(categoryListVOList).getId(),productCycleList);
            }
        }
        return allProductMap;
    }

    @Override
    public List<ProductVO> queryByCategory(Long categoryId, Integer cycleFlag) {
        //获取商品
        List<ProductVO> productVoList = getProductByCategory(categoryId, cycleFlag);
        //封装营销信息
        this.buildMarketing(productVoList);
        return productVoList;
    }

    private List<ProductVO> getProductByCategory(Long categoryId, Integer cycleFlag) {
        ProductSearchDTO dto = new ProductSearchDTO();
        dto.setSortType(ProductOrderByEnum.ECARD_SORT_ASC.getDesc());
        dto.setProductTypeList(Arrays.asList(ProductTypeEnum.PACKAGE.getCode(),ProductTypeEnum.SINGLE_PRODUCT.getCode(),ProductTypeEnum.ENTITY_CARD.getCode()));
        dto.setEnableShow(1);
        dto.setPageSize(null);
        dto.setPageNum(null);
        dto.setSkuFlag(true);
        dto.addSaleStatus(ProductSaleStatusEnum.ON_SHELVES.getCode());
        dto.setCateLevel(ItemCategoryLevelEnum.FIRSTCATE.getCode());
        dto.setCycleFlag(cycleFlag);
        if(!ProductCycleEnum.CYCLE.getCode().equals(cycleFlag)){
            dto.setCateIds(Arrays.asList(categoryId));
        }
        List<ProductBaseDTO> allProductList = productManager.searchList(dto, true);
        List<ProductVO> productVoList = BeanUtils.deepListCopy(allProductList, ProductVO.class);
        return productVoList;
    }

    @Override
    public void buildMarketing(List<ProductVO> productList) {
        CustomerUserVO user = UserUtil.isLogged()? UserUtil.getDetails() : null;
        //加载优惠券信息
        CouponRuleListDTO ruleDto = new CouponRuleListDTO();
        ruleDto.setChannel(CouponChannelTypeEnum.WECHAT_MINI.getCode());
        ruleDto.setIsPublic(CouponPublicTypeEnum.PUBLIC.getCode());
        ruleDto.setStatus(CouponStatusTypeEnum.ONLINE.getCode());
        ruleDto.setEndTimeStart(new Date());
        ruleDto.setCouponType(CouponTypeEnum.PRODUCT_COUPON.getCode());
        //查询可领+已领的优惠券
        List<CouponRule> couponRuleList = couponRuleManager.queryEnableReceiveAndReceivedCoupon(ruleDto, user);
        List<Long> ruleIds = StreamUtils.convert(couponRuleList, CouponRule::getId);
        List<CouponRange> couponRangeList = couponRangeDao.queryByRuleIds(ruleIds);
        Map<Long, List<CouponRange>> couponRangeMap = StreamUtils.group(couponRangeList, CouponRange::getRuleId);

        List<PointAmount> pointAmountList = pointAmountManager.getInProgressList();
        List<PointAmountItem> pointAmountItemList = pointAmountItemManager.findByPointAmountIds(StreamUtils.convert(pointAmountList, PointAmount::getId));
        Map<Long, List<PointAmountItem>> pointAmountItemMap = StreamUtils.group(pointAmountItemList, PointAmountItem::getPointAmountId);
        for (ProductVO productVO : productList) {
            productVO.setCurrentTime(System.currentTimeMillis());
            //促销活动信息
            buildProdctDiscountActivity(productVO, user, true, null);
            //满减满送
            buildProductFullReduce(productVO, user, false, null, 1, null, productVO.getCycleFlag());
            //商品上装载 优惠券信息+券后价（数量为1计算）
            buildProdctAllCouponList(productVO, couponRuleList, couponRangeMap);
            //装载积分抵现活动
            buildProductPointAmount(productVO, user, pointAmountList, pointAmountItemMap);
            //装载商品标签
            PointCommonUtil.buildProductLabel(productVO, 1, null);
            //拼团活动信息
            buildProductPtActivity(productVO, null);
        }
    }

    @Override
    public void rankCategoryItemHotSale(List<ProductVO> productVoList) {
        // 根据一级类目分组
        // 查询每个分组下面的topK数据列表
        // 每个分组中的id去匹配是否
        Map<Long, List<ProductVO>> categoryItemGroup = productVoList.stream().collect(Collectors.groupingBy(ProductVO::getFirstCateId));
        Set<Long> firstCateIds = categoryItemGroup.keySet();
        List<Category> categories = categoryDao.selectAllByIds(Lists.newArrayList(firstCateIds));
        // 一级分类下商品数量
        Map<Long, Integer> cateItemCountMap = productManager.countByFirstCateIds(Lists.newArrayList(firstCateIds));
        Map<Long, String> cateNameMap = categories.stream().collect(Collectors.toMap(Category::getId, Category::getCateName));
        for (Map.Entry<Long, List<ProductVO>> entry : categoryItemGroup.entrySet()) {
            Long cateId = entry.getKey();
            // 一级分类下商品不足四个 不排名
            Integer firstCategoryItemCount = cateItemCountMap.getOrDefault(cateId, 0);
            if (firstCategoryItemCount < 4) {
                continue;
            }
            List<ProductVO> firstCategoryItemList = entry.getValue();
            List<Long> cateIds = categoryDao.selectByParId(Lists.newArrayList(cateId));
            List<Product> products = productManager.selectTopKByCateIds(cateId, cateIds);
            firstCategoryItemList.forEach(e -> {
                for (int i = 0; i < products.size(); i++) {
                    Product product = products.get(i);
                    if (product.getId().equals(e.getId())) {
                        ProductVO.CategoryItemHotSaleRank rank = new ProductVO.CategoryItemHotSaleRank();
                        rank.setFirstCateId(cateId);
                        rank.setFirstCategoryName(cateNameMap.getOrDefault(cateId, "默认"));
                        rank.setRank(i+1);
                        e.setCategoryItemHotSaleRank(rank);
                    }
                }
            });
        }
    }

    @Override
    public PageVO<ProductVO> dispatchList(ProductSearchDTO dto) {
        CustomerUserVO customerUserVO = Objects.requireNonNull(UserUtil.getDetails(), "登录后重试");
        if(CollectionUtils.isEmpty(dto.getCardCategoryIds())){
            throw new BusinessException("参数缺失！");
        }
        int pageNum = dto.getPageNum();
        int pageSize = dto.getPageSize();
        // 提奶商品根据奶卡类别查询
        List<ItemProductAddons> cardIds = itemProductAddonsDao.selectByCardCategoryIds(dto.getCardCategoryIds());
        List<Long> ids = StreamUtils.convertDistinct(cardIds, ItemProductAddons::getProductId, Long::compareTo);
        if (CollectionUtils.isEmpty(ids)) {
            return PageUtils.emptyPage(dto);
        }
        if (CollectionUtils.isNotEmpty(dto.getProductIds())) {
            dto.getProductIds().retainAll(ids);
            if(CollectionUtils.isEmpty(dto.getProductIds())){
                return PageUtils.emptyPage(dto);
            }
        } else {
            dto.setProductIds(ids);
        }
        dto.setCardCategoryIds(null);
        dto.addSaleStatus(ProductSaleStatusEnum.ON_SHELVES.getCode());
        dto.setPageNum(1);
        dto.setPageSize(Integer.MAX_VALUE);
        List<Product> productList = productDao.findByCondition(dto);
        List<Long> generalProductIds = StreamUtils.filterConvert(productList, x -> Objects.equals(x.getPrivilegeFlag(), PrivilegeFlagEnum.GENERAL.getCode()), Product::getId);
        List<Long> privilegeProductIds = StreamUtils.filterConvert(productList, x -> Objects.equals(x.getPrivilegeFlag(), PrivilegeFlagEnum.Privilege.getCode()), Product::getId);
        List<Long> privilegeProductIdsByUser = privilegeItemUserDao.findPrivilegeProductByUserId(customerUserVO.getId());
        // 获取两个列表的交集
        Set<Long> intersection = new HashSet<>(privilegeProductIds);
        intersection.retainAll(new HashSet<>(privilegeProductIdsByUser));
        generalProductIds.addAll(intersection);
        if(CollectionUtils.isEmpty(generalProductIds)){
            return PageUtils.emptyPage(dto);
        }
        dto.setSkuFlag(true);
        dto.setSortType(ProductOrderByEnum.PRIVILEGE_DESC.getCode());
        dto.setProductIds(generalProductIds);
        dto.setPageNum(pageNum);
        dto.setPageSize(pageSize);
        List<Product> productSortList = productDao.selectByConditionSort(dto);
        if (CollectionUtils.isEmpty(productSortList)) {
            return PageUtils.emptyPage(dto);
        }

        List<ProductBaseDTO> productBaseList = BeanUtils.copyList(productSortList, ProductBaseDTO::new);
        List<Long> productIds = StreamUtils.toList(productSortList, Product::getId);
        Map<Long, List<SkuBaseDTO>> skuMap = new HashMap<>();
        Map<Long, List<ProductAttrDTO>> attrMap = new HashMap<>();
        if (dto.isSkuFlag()) {
            skuMap = skuManager.getAllSkuMapByProdcutIds(productIds, true);
        }
        attrMap = StreamUtils.mapGroup(productAttrDao.selectByProductIds(productIds),
                e -> BeanUtils.copy(e, ProductAttrDTO::new), ProductAttrDTO::getProductId);
        Map<Long, List<ItemProductAddons>> group = StreamUtils.group(itemProductAddonsDao.selectList(productIds), ItemProductAddons::getProductId);

        for (ProductBaseDTO baseDTO : productBaseList) {
            if (dto.isSkuFlag()) {
                baseDTO.setSkus(skuMap.get(baseDTO.getId()));
            }
            baseDTO.setProductAttrs(attrMap.get(baseDTO.getId()));
            if (ProductTypeEnum.NORMAL.getCode().equals(baseDTO.getProductType())) {
                baseDTO.setItemProductAddonsList(BeanUtils.deepListCopy(group.get(baseDTO.getId()), ItemProductAddonsDTO.class));
                ProductCycleDTO productCycleDTO = productManager.buildProductCycle(baseDTO.getId(), baseDTO.getProductAttrs());
                baseDTO.setCycleList(productCycleDTO.getCycleList());
                baseDTO.setCycleDefault(productCycleDTO.getCycleDefault());
            }
        }

        return PageUtils.build(PageUtils.extract(productSortList), BeanUtils.deepListCopy(productBaseList, ProductVO.class));
    }

    @Override
    public ProductPromoVO promotion(Long productId) {
        CustomerUserVO customerUserVO = Objects.requireNonNull(UserUtil.getDetails(), "登录后重试");
        Product product = productManager.findById(productId);
        if (null == product) {
            throw new BusinessException("商品不存在或已删除");
        }
        WorkWxSales wxSales = StreamUtils.getFirst(workWxSalesDao.findByPhone(customerUserVO.getPhone()));
        if(null == wxSales){
            throw new BusinessException("小程序登陆手机号未关联企业微信账号!");
        }
        String urlLink = weChatMiniCouponManager.getUrlLink("/packageB/pages/singleDetail/singleDetail", "id=" + productId + "&wxsId=" + wxSales.getId());
        //生成短链
        String pageUrl = "packageB/pages/singleDetail/singleDetail?id=" + productId + "&wxsId=" + wxSales.getId();
        WechatShortLinkResp shortLinkResp = weChatMiniSmsManager.getShortLink(pageUrl, product.getProductName(), false);
        ProductPromoVO productPromoVO = new ProductPromoVO();
        productPromoVO.setProductId(productId);
        productPromoVO.setShortLink(shortLinkResp.getLink());
        productPromoVO.setDwzLink(urlLink);
        productPromoVO.setWxsId(wxSales.getId());
        return productPromoVO;
    }

    @Override
    public List<GroupTemplateVO> groupList(GroupTemplateSearchDTO searchDTO) {
        return groupTemplateManager.list(searchDTO);
    }

    @Override
    public List<ProductVO> queryByIds(List<Long> productIds) {
        ProductSearchDTO dto = new ProductSearchDTO();
        dto.setSortType(ProductOrderByEnum.ECARD_SORT_ASC.getDesc());
        // dto.setProductTypeList(Arrays.asList(ProductTypeEnum.PACKAGE.getCode(),ProductTypeEnum.SINGLE_PRODUCT.getCode(),ProductTypeEnum.ENTITY_CARD.getCode()));
        dto.setEnableShow(1);
        dto.setProductIds(productIds);
        dto.setSkuFlag(true);
        dto.addSaleStatus(ProductSaleStatusEnum.ON_SHELVES.getCode());
        dto.setPageSize(Integer.MAX_VALUE);
        // dto.setCateLevel(ItemCategoryLevelEnum.FIRSTCATE.getCode());
        List<ProductBaseDTO> allProductList = productManager.searchList(dto, false);
        List<ProductVO> productVoList = BeanUtils.deepListCopy(allProductList, ProductVO.class);

        //加载优惠券信息
        CouponRuleListDTO ruleDto = new CouponRuleListDTO();
        ruleDto.setChannel(CouponChannelTypeEnum.WECHAT_MINI.getCode());
        ruleDto.setIsPublic(CouponPublicTypeEnum.PUBLIC.getCode());
        ruleDto.setStatus(CouponStatusTypeEnum.ONLINE.getCode());
        ruleDto.setEndTimeStart(new Date());
        ruleDto.setCouponType(CouponTypeEnum.PRODUCT_COUPON.getCode());

        CustomerUserVO user = null;
        if(UserUtil.isLogged()){
            user = UserUtil.getDetails();
        }
        //查询可领+已领的优惠券
        List<CouponRule> couponRuleList = couponRuleManager.queryEnableReceiveAndReceivedCoupon(ruleDto, user);
        List<Long> ruleIds = StreamUtils.convert(couponRuleList, CouponRule::getId);
        List<CouponRange> couponRangeList = couponRangeDao.queryByRuleIds(ruleIds);
        Map<Long, List<CouponRange>> couponRangeMap = StreamUtils.group(couponRangeList, CouponRange::getRuleId);

        List<PointAmount> pointAmountList = pointAmountManager.getInProgressList();
        List<PointAmountItem> pointAmountItemList = pointAmountItemManager.findByPointAmountIds(StreamUtils.convert(pointAmountList, PointAmount::getId));
        Map<Long, List<PointAmountItem>> pointAmountItemMap = StreamUtils.group(pointAmountItemList, PointAmountItem::getPointAmountId);
        for (ProductVO productVO : productVoList) {
            productVO.setCurrentTime(System.currentTimeMillis());
            //促销活动信息
            buildProdctDiscountActivity(productVO, user, true, null);
            //满减满送
            buildProductFullReduce(productVO, user, false, null, 1, null, productVO.getCycleFlag());
            //商品上装载 优惠券信息+券后价（数量为1计算）
            buildProdctAllCouponList(productVO, couponRuleList, couponRangeMap);
            //装载积分抵现活动
            buildProductPointAmount(productVO, user, pointAmountList, pointAmountItemMap);
            //装载商品标签
            PointCommonUtil.buildProductLabel(productVO, 1, null);
        }
        return productVoList;
    }





    @Override
    public void buildProductPointAmount(ProductVO productVO, CustomerUserVO user, List<PointAmount> pointAmountList, Map<Long, List<PointAmountItem>> pointAmountItemMap) {
        pointAmountService.buildProductPointAmount(productVO, user, pointAmountList, pointAmountItemMap);
    }

    @Override
    public void buildProductPointAmountWithAccess(ProductVO productVO, CustomerUserVO user, List<PointAmount> pointAmountList, Map<Long, List<PointAmountItem>> pointAmountItemMap) {
        if (!productVO.getShareDiscount().contains("1")) {
            return;
        }
        this.buildProductPointAmount(productVO, user, pointAmountList, pointAmountItemMap);
    }


    @Override
    public void buildProductFullReduce(ProductVO productVO,
                                        CustomerUserVO user,
                                        boolean infoFlag,
                                        Long skuId,
                                        Integer count,
                                        Integer times,
                                        Integer cycleFlag) {
        //获取满减活动
        FullReduce fullReduce = fullReduceManager.getFullReduceFromRedis(productVO.getId());
        if(null == fullReduce){
            return;
        }
        //校验人群
        if(!commonOrderOptService.verifyPeopleLimit(fullReduce, user)){
            return;
        }
        //遍历sku
        for (SkuVO skuVO: productVO.getSkus()) {
            //指定sku
            if(null != skuId && !skuId.equals(skuVO.getId())){
                continue;
            }
            if(Objects.nonNull(skuVO.getFreeTrialSubActivityId())){
                if(!skuVO.getFreeTrialSubActivityShareDiscount().contains("3")){
                    continue;
                }
            }
            BigDecimal price = skuVO.getSalePrice();
            boolean isShareDiscount = FullReduceShareTypeEnum.isShareDiscount(fullReduce.getShareType());
            //sku有折扣信息
            if(null != skuVO.getDiscountActivityId()){
                if(DiscountActivityStatusEnum.IN_PROGRESS.getCode().equals(skuVO.getDiscountActivityStatus())){
                    //满减与折扣不共享且限时折扣进行中
                    if(!isShareDiscount){
                        continue;
                    }
                    price = Objects.isNull(skuVO.getDiscountPrice())? skuVO.getSalePrice(): skuVO.getDiscountPrice();
                }
            }
            //获取用来计算满减门槛的价格
            SkuPriceSumDTO skuPriceSumDTO = this.getSkuSalePrice(skuVO, cycleFlag, price, count, times);
            //计算满减价格
            FullReduceVO fullReduceVO = new FullReduceVO();
            //周期购
            if(null != skuPriceSumDTO.getCycleSalePriceSum()){
                BigDecimal fullReducePrice = fullReduceService.calculateFullReducePrice(fullReduce, cycleFlag, count, skuPriceSumDTO.getCycleSalePriceSum());
                if(null != fullReducePrice){
                    if(BasicFlagEnum.YES.getKey().equals(cycleFlag)){
                        fullReduceVO.setSumFullReducePrice(fullReducePrice);
                    }
                    fullReduceVO.setId(fullReduce.getId());
                    fullReduceVO.setShareType(fullReduce.getShareType());
                    if(null != skuPriceSumDTO.getCycleMax()){
                        fullReduceVO.setCycleMax(skuPriceSumDTO.getCycleMax());
                        fullReduceVO.setCycleSingleFullReducePrice(fullReducePrice.divide(skuPriceSumDTO.getCycleMax(), 2, BigDecimal.ROUND_UP).divide(BigDecimal.valueOf(count), 2, BigDecimal.ROUND_UP));
                    }
                    skuVO.setFullReduce(fullReduceVO);
                }
            }
            //单次购买
            if(null != skuPriceSumDTO.getSalePriceSum()){
                BigDecimal fullReducePrice = fullReduceService.calculateFullReducePrice(fullReduce, cycleFlag, count, skuPriceSumDTO.getSalePriceSum());
                if(null != fullReducePrice){
                    if(BasicFlagEnum.NO.getKey().equals(cycleFlag)){
                        fullReduceVO.setSumFullReducePrice(fullReducePrice);
                    }
                    fullReduceVO.setId(fullReduce.getId());
                    fullReduceVO.setShareType(fullReduce.getShareType());
                    fullReduceVO.setFullReducePrice(fullReducePrice.divide(BigDecimal.valueOf(count), 2, BigDecimal.ROUND_UP));
                    skuVO.setFullReduce(fullReduceVO);
                }
            }
            if(null == productVO.getFullReduce()){
                List<String> labelList = getFullReduceLabel(fullReduce);
                FullReduceMallDTO fullReduceMallDTO = BeanUtils.deepCopy(fullReduce, FullReduceMallDTO.class);
                fullReduceMallDTO.setLabelList(labelList);
                if(infoFlag){
                    List<FullReduceInfo> fullReduceInfo = getFullReduceCompleteLabel(fullReduce);
                    fullReduceMallDTO.setFullReduceInfo(fullReduceInfo);
                }
                productVO.setFullReduce(fullReduceMallDTO);
            }
        }
    }

    @Override
    public void buildProductFullReduceWithAccess(ProductVO productVO, CustomerUserVO user, boolean infoFlag, Long skuId, Integer count, Integer times, Integer cycleFlag) {
        if (!productVO.getShareDiscount().contains("3")) {
            return;
        }
        this.buildProductFullReduce(productVO, user, infoFlag, skuId, count, times, cycleFlag);
    }

    private List<String> getFullReduceLabel(FullReduce fullReduce) {
        List<String> labelList = new ArrayList<>();
        List<String> priceLabelList = new ArrayList<>();
        List<String> couponLabelList = new ArrayList<>();
        List<String> giftLabelList = new ArrayList<>();
        List<FullReduceRule> ruleList = fullReduceRuleDao.findByFullId(fullReduce.getId(), "level asc");
        List<FullReduceRuleRange> reduceRuleRangeList = fullReduceRuleRangeDao.findByFullReduceId(fullReduce.getId());
        Map<Long, List<FullReduceRuleRange>> reduceRuleRangeMap = StreamUtils.group(reduceRuleRangeList, FullReduceRuleRange::getFullReduceRuleId);
        for (FullReduceRule fullReduceRule: ruleList) {
            List<FullReduceRuleRange> ruleRangeList = reduceRuleRangeMap.get(fullReduceRule.getId());
            List<FullReduceRuleRange> giftList = StreamUtils.filter(ruleRangeList, x -> FullRuleRangeTypeEnum.GIFT.getCode().equals(x.getType()));
            List<FullReduceRuleRange> couponList = StreamUtils.filter(ruleRangeList, x -> FullRuleRangeTypeEnum.COUNPON.getCode().equals(x.getType()));

            //减钱/打折
            if(null != FullRuleReduceTypeEnum.getEnumByCode(fullReduceRule.getReduceType())){
                priceLabelList.add(getLabel(fullReduce, fullReduceRule, FullRuleReduceModelEnum.REDUCE));
            }
            //赠品
            if(CollectionUtils.isEmpty(giftLabelList) && CollectionUtils.isNotEmpty(giftList)){
                giftLabelList.add(getLabel(fullReduce, fullReduceRule, FullRuleReduceModelEnum.GIFT));
            }
            //优惠券
            if(CollectionUtils.isEmpty(couponLabelList) && CollectionUtils.isNotEmpty(couponList)){
                couponLabelList.add(getLabel(fullReduce, fullReduceRule, FullRuleReduceModelEnum.COUPON));
            }
        }
        labelList.addAll(priceLabelList);
        labelList.addAll(couponLabelList);
        labelList.addAll(giftLabelList);
        return labelList;
    }

    private List<FullReduceInfo> getFullReduceCompleteLabel(FullReduce fullReduce) {
        List<FullReduceInfo> labelList = new ArrayList<>();
        List<FullReduceRule> ruleList = fullReduceRuleDao.findByFullId(fullReduce.getId(), "level asc");
        FullReduceRule fullReduceRule = StreamUtils.getFirst(ruleList);
        List<FullReduceRuleRange> reduceRuleRangeList = fullReduceRuleRangeDao.findByFullReduceId(fullReduce.getId());
        Map<Long, List<FullReduceRuleRange>> reduceRuleRangeMap = StreamUtils.group(reduceRuleRangeList, FullReduceRuleRange::getFullReduceRuleId);
        if(null != fullReduceRule){
            List<FullReduceRuleRange> ruleRangeList = reduceRuleRangeMap.get(fullReduceRule.getId());
            List<FullReduceRuleRange> giftList = StreamUtils.filter(ruleRangeList, x -> FullRuleRangeTypeEnum.GIFT.getCode().equals(x.getType()));
            List<FullReduceRuleRange> couponList = StreamUtils.filter(ruleRangeList, x -> FullRuleRangeTypeEnum.COUNPON.getCode().equals(x.getType()));
            FullReduceInfo info = getCompleteLabel(fullReduce, fullReduceRule, giftList, couponList);
            info.setStartTime(fullReduce.getStartTime());
            info.setEndTime(fullReduce.getEndTime());
            labelList.add(info);
        }
        return labelList;
    }

    private String getLabel(FullReduce fullReduce, FullReduceRule fullReduceRule, FullRuleReduceModelEnum modelEnum) {
        FullReduceTypeEnum reduceTypeEnum = FullReduceTypeEnum.getEnumByCode(fullReduce.getType());
        FullReduceDiscountRuleEnum discountRuleEnum = FullReduceDiscountRuleEnum.getEnumByCode(fullReduce.getDiscountRule());
        StringBuilder sb = new StringBuilder();
        switch (reduceTypeEnum){
            case FULL_MONEY:
                if(FullReduceDiscountRuleEnum.STEP == discountRuleEnum){
                    sb.append("满").append(fullReduceRule.getFullAmount().stripTrailingZeros().toPlainString()).append(modelEnum.getStr(fullReduceRule));
                }else if(FullReduceDiscountRuleEnum.LOOP == discountRuleEnum){
                    sb.append("每满").append(fullReduceRule.getFullAmount().stripTrailingZeros().toPlainString()).append(modelEnum.getStr(fullReduceRule));
                }
                break;
            case FULL_REAL_AMOUNT:
                if(FullReduceDiscountRuleEnum.STEP == discountRuleEnum){
                    sb.append("实付").append(fullReduceRule.getFullAmount().stripTrailingZeros().toPlainString()).append(modelEnum.getStr(fullReduceRule));
                }else if(FullReduceDiscountRuleEnum.LOOP == discountRuleEnum){
                    sb.append("实付每").append(fullReduceRule.getFullAmount().stripTrailingZeros().toPlainString()).append(modelEnum.getStr(fullReduceRule));
                }
                break;
            case FULL_COUNT:
                if(FullReduceDiscountRuleEnum.STEP == discountRuleEnum){
                    sb.append("满").append(fullReduceRule.getFullCount()).append("件").append(modelEnum.getStr(fullReduceRule));
                }else if(FullReduceDiscountRuleEnum.LOOP == discountRuleEnum){
                    sb.append("每满").append(fullReduceRule.getFullCount()).append("件").append(modelEnum.getStr(fullReduceRule));
                }
                break;
        }
        return sb.toString();
    }

    private FullReduceInfo getCompleteLabel(FullReduce fullReduce, FullReduceRule fullReduceRule, List<FullReduceRuleRange> giftList, List<FullReduceRuleRange> couponList) {
        FullReduceTypeEnum reduceTypeEnum = FullReduceTypeEnum.getEnumByCode(fullReduce.getType());
        FullReduceDiscountRuleEnum discountRuleEnum = FullReduceDiscountRuleEnum.getEnumByCode(fullReduce.getDiscountRule());
        Set<Integer> modelList = new HashSet<>();
        StringBuilder sb = new StringBuilder();
        switch (reduceTypeEnum){
            case FULL_MONEY:
                if(FullReduceDiscountRuleEnum.STEP == discountRuleEnum){
                    sb.append("满").append(fullReduceRule.getFullAmount().stripTrailingZeros().toPlainString());
                }else if(FullReduceDiscountRuleEnum.LOOP == discountRuleEnum){
                    sb.append("每满").append(fullReduceRule.getFullAmount().stripTrailingZeros().toPlainString());
                }
                break;
            case FULL_REAL_AMOUNT:
                if(FullReduceDiscountRuleEnum.STEP == discountRuleEnum){
                    sb.append("实付").append(fullReduceRule.getFullAmount().stripTrailingZeros().toPlainString());
                }else if(FullReduceDiscountRuleEnum.LOOP == discountRuleEnum){
                    sb.append("实付每").append(fullReduceRule.getFullAmount().stripTrailingZeros().toPlainString());
                }
                break;
            case FULL_COUNT:
                if(FullReduceDiscountRuleEnum.STEP == discountRuleEnum){
                    sb.append("满").append(fullReduceRule.getFullCount()).append("件");
                }else if(FullReduceDiscountRuleEnum.LOOP == discountRuleEnum){
                    sb.append("每满").append(fullReduceRule.getFullCount()).append("件");
                }
                break;
        }
        FullRuleReduceTypeEnum reduceType = FullRuleReduceTypeEnum.getEnumByCode(fullReduceRule.getReduceType());
        if(null != reduceType){
            if(FullRuleReduceTypeEnum.REDUCE == reduceType){
                sb.append("减").append(fullReduceRule.getReduceAmount().stripTrailingZeros().toPlainString()).append(",").toString();
                modelList.add(FullRuleReduceModelEnum.REDUCE.getCode());
            }else if(FullRuleReduceTypeEnum.DISCOUNT == reduceType){
                sb.append("打").append(fullReduceRule.getDiscount().stripTrailingZeros().toPlainString()).append("折,").toString();
                modelList.add(FullRuleReduceModelEnum.REDUCE.getCode());
            }
        }
        if(CollectionUtils.isNotEmpty(giftList)){
            sb.append("送1件赠品,");
            modelList.add(FullRuleReduceModelEnum.GIFT.getCode());
        }
        if(CollectionUtils.isNotEmpty(couponList)){
            sb.append("送1张优惠券,");
            //赠品/优惠券前端统一当作满赠
            modelList.add(FullRuleReduceModelEnum.GIFT.getCode());
        }
        sb.setLength(sb.length() - 1);
        FullReduceInfo fullReduceInfo = new FullReduceInfo();
        fullReduceInfo.setLabel(sb.toString());
        fullReduceInfo.setFullReduceId(fullReduce.getId());
        fullReduceInfo.setModel(new ArrayList<>(modelList));
        return fullReduceInfo;
    }

    @Override
    public void buildProdctDiscountActivity(ProductVO productVO, CustomerUserVO customerUserVO, boolean noticeFlag, Long skuId) {
        List<SkuVO> skuList = productVO.getSkus();
        List<SkuDiscountActivityDTO> discountActivityList = new ArrayList<>();
        Long discountStock = 0L;
        Long discountUsed = 0L;
        Long discountRemaining = 0L;
        for (SkuVO skuVO : skuList) {
            //指定sku
            if(null != skuId && !skuId.equals(skuVO.getId())){
                continue;
            }
            //限时折扣与满额试用&0元尝鲜默认互斥
            if(Objects.nonNull(skuVO.getFreeTrialSubActivityId())){
                continue;
            }
            String key = DiscountUtil.DISCOUNT_ACTIVITY_KEY + skuVO.getId();
            Map<Object, Object> map = redisOperation.hgetAll(key);
            List<SkuDiscountActivityDTO> skuDiscountActivityList = new ArrayList<>();
            for (Map.Entry<Object, Object> entry : map.entrySet()) {
                String jsonStr = String.valueOf(entry.getValue());
                if(StringUtils.isBlank(jsonStr)){
                    continue;
                }
                SkuDiscountActivityDTO skuDiscountActivityDTO = JSON.parseObject(jsonStr, SkuDiscountActivityDTO.class);
                skuDiscountActivityList.add(skuDiscountActivityDTO);
            }

            //人群限制-过滤不可用的活动
            skuDiscountActivityList = commonOrderOptService.filterPeopleLimit(skuDiscountActivityList, customerUserVO);
            discountActivityList.addAll(skuDiscountActivityList);

            //进行中的活动
            SkuDiscountActivityDTO skuActivity = inProgressActivity(skuDiscountActivityList);
            if(noticeFlag && null == skuActivity){
                //待开始且预告
                skuActivity = noticeActivity(skuDiscountActivityList);
            }
            if(null != skuActivity){
                skuVO.setDiscountPrice(skuActivity.getDiscountPrice());
                skuVO.setDiscountActivityId(skuActivity.getId());
                skuVO.setSharingDiscount(skuActivity.getSharingDiscount());
                skuVO.setPeopleLimit(skuActivity.getPeopleLimit());
                skuVO.setDiscountActivityStatus(skuActivity.getStatus());
                if(Objects.equals(DiscountActivityStatusEnum.IN_PROGRESS.getCode(), skuActivity.getStatus())){
                    DiscountActivityRange discountActivityRange = discountActivityRangeDao.findByDiscountActivityIdAndSkuId(skuActivity.getId(), skuVO.getId());
                    if(Objects.nonNull(discountActivityRange)){
                        Long realStock = discountActivityRange.getStock() - discountActivityRange.getUsed() < 0 ? 0 : discountActivityRange.getStock() - discountActivityRange.getUsed();
                        skuVO.setStock(realStock);
                        skuVO.setPurchase(Math.toIntExact(Objects.isNull(discountActivityRange.getLimitNum()) ? 0 : discountActivityRange.getLimitNum()));
                        skuVO.setMinimumCount(Math.toIntExact(Objects.isNull(discountActivityRange.getMinimumCount()) ? 0 : discountActivityRange.getMinimumCount()));
                        discountStock += discountActivityRange.getStock();
                        discountUsed += discountActivityRange.getUsed();
                        discountRemaining += realStock;
                    }
                }
            }
        }

        //进行中
        SkuDiscountActivityDTO skuActivity = inProgressActivity(discountActivityList);
        //没有进行中的折扣活动
        if(noticeFlag && null == skuActivity){
            //待开始且预告
            skuActivity = noticeActivity(discountActivityList);
        }
        if(null != skuActivity){
            DiscountActivityVO discountActivityVO = new DiscountActivityVO();
            discountActivityVO.setStartTime(skuActivity.getStartTime().getTime());
            discountActivityVO.setEndTime(skuActivity.getEndTime().getTime());
            discountActivityVO.setStatus(skuActivity.getStatus());
            discountActivityVO.setLabel(skuActivity.getLabel());
            discountActivityVO.setId(skuActivity.getId());
            discountActivityVO.setStock(discountStock);
            discountActivityVO.setUsed(discountUsed);
            discountActivityVO.setRemaining(discountRemaining);
            discountActivityVO.setHasSubscribe(null == customerUserVO? 0 : redisOperation.hexists(DiscountActivityUtil.SUBSCRIBE_START + skuActivity.getId() + "-" + productVO.getId(), customerUserVO.getId() + "")? 1 :0);
            productVO.setDiscountActivityVO(discountActivityVO);
        }
    }

    @Override
    public void buildProdctDiscountActivity(ProductVO productVO, CustomerUserVO customerUserVO, boolean noticeFlag, Long skuId, Integer status) {
        List<SkuVO> skuList = productVO.getSkus();
        List<SkuDiscountActivityDTO> discountActivityList = new ArrayList<>();
        Long discountStock = 0L;
        Long discountUsed = 0L;
        Long discountRemaining = 0L;
        for (SkuVO skuVO : skuList) {
            //指定sku
            if(null != skuId && !skuId.equals(skuVO.getId())){
                continue;
            }
            String key = DiscountUtil.DISCOUNT_ACTIVITY_KEY + skuVO.getId();
            Map<Object, Object> map = redisOperation.hgetAll(key);
            List<SkuDiscountActivityDTO> skuDiscountActivityList = new ArrayList<>();
            for (Map.Entry<Object, Object> entry : map.entrySet()) {
                String jsonStr = String.valueOf(entry.getValue());
                if(StringUtils.isBlank(jsonStr)){
                    continue;
                }
                SkuDiscountActivityDTO skuDiscountActivityDTO = JSON.parseObject(jsonStr, SkuDiscountActivityDTO.class);
                skuDiscountActivityList.add(skuDiscountActivityDTO);
            }

            //人群限制-过滤不可用的活动
            skuDiscountActivityList = commonOrderOptService.filterPeopleLimit(skuDiscountActivityList, customerUserVO);
            discountActivityList.addAll(skuDiscountActivityList);

            //进行中的活动
            SkuDiscountActivityDTO skuActivity = null;
            if(Objects.equals(status, DiscountActivityStatusEnum.IN_PROGRESS.getCode())){
                skuActivity = this.inProgressActivity(skuDiscountActivityList);
            }
            if(noticeFlag && Objects.equals(status, DiscountActivityStatusEnum.NOT_STARTED.getCode())){
                //待开始且预告
                skuActivity = this.noticeActivity(skuDiscountActivityList);
            }
            if(null != skuActivity){
                skuVO.setDiscountPrice(skuActivity.getDiscountPrice());
                skuVO.setDiscountActivityId(skuActivity.getId());
                skuVO.setSharingDiscount(skuActivity.getSharingDiscount());
                skuVO.setPeopleLimit(skuActivity.getPeopleLimit());
                skuVO.setDiscountActivityStatus(skuActivity.getStatus());
                DiscountActivityRange discountActivityRange = discountActivityRangeDao.findByDiscountActivityIdAndSkuId(skuActivity.getId(), skuVO.getId());
                if(Objects.nonNull(discountActivityRange)){
                    discountStock += discountActivityRange.getStock();
                    if(Objects.equals(DiscountActivityStatusEnum.IN_PROGRESS.getCode(), skuActivity.getStatus())){
                        Long realStock = discountActivityRange.getStock() - discountActivityRange.getUsed() < 0 ? 0 : discountActivityRange.getStock() - discountActivityRange.getUsed();
                        skuVO.setStock(realStock);
                        skuVO.setPurchase(Math.toIntExact(Objects.isNull(discountActivityRange.getLimitNum()) ? 0 : discountActivityRange.getLimitNum()));
                        discountUsed += discountActivityRange.getUsed();
                        discountRemaining += realStock;
                    }
                }
            }
        }
        SkuDiscountActivityDTO skuActivity = null;
        //进行中
        if(Objects.equals(status, DiscountActivityStatusEnum.IN_PROGRESS.getCode())) {
            skuActivity = this.inProgressActivity(discountActivityList);
        }
        //没有进行中的折扣活动
        if(noticeFlag && Objects.equals(status, DiscountActivityStatusEnum.NOT_STARTED.getCode())) {
            //待开始且预告
            skuActivity = this.noticeActivity(discountActivityList);
        }
        if(null != skuActivity){
            DiscountActivityVO discountActivityVO = new DiscountActivityVO();
            discountActivityVO.setStartTimeDate(skuActivity.getStartTime());
            discountActivityVO.setStartTime(skuActivity.getStartTime().getTime());
            discountActivityVO.setEndTime(skuActivity.getEndTime().getTime());
            discountActivityVO.setEndTimeDate(skuActivity.getEndTime());
            discountActivityVO.setStatus(skuActivity.getStatus());
            discountActivityVO.setLabel(skuActivity.getLabel());
            discountActivityVO.setId(skuActivity.getId());
            discountActivityVO.setStock(discountStock);
            discountActivityVO.setUsed(discountUsed);
            discountActivityVO.setRemaining(discountRemaining);
            discountActivityVO.setHasSubscribe(null == customerUserVO? 0 : redisOperation.hexists(DiscountActivityUtil.SUBSCRIBE_START + skuActivity.getId() + "-" + productVO.getId(), customerUserVO.getId() + "")? 1 :0);
            productVO.setDiscountActivityVO(discountActivityVO);
        }
    }

    @Override
    public void buildProdctDiscountActivity(FullSkuWarp fullSkuWarp) {
        fullSkuWarp.getSku();
        Long skuId;
        if(Objects.nonNull(fullSkuWarp.getSku())){
            skuId = fullSkuWarp.getSku().getSkuId();
            //限时折扣与满额试用&0元尝鲜默认互斥
            if(Objects.nonNull(fullSkuWarp.getSku().getFreeTrialSubActivityId())){
                return;
            }
        }else {
            skuId = fullSkuWarp.getCartItem().getSkuId();
        }
        // 拼团不可兼容限时折扣
        if(null != fullSkuWarp.getPtActivityVO()){
            return;
        }
        String key = DiscountUtil.DISCOUNT_ACTIVITY_KEY + skuId;
        Map<Object, Object> map = redisOperation.hgetAll(key);
        List<SkuDiscountActivityDTO> skuDiscountActivityList = new ArrayList<>();
        for (Map.Entry<Object, Object> entry : map.entrySet()) {
            String jsonStr = String.valueOf(entry.getValue());
            if(StringUtils.isBlank(jsonStr)){
                continue;
            }
            SkuDiscountActivityDTO skuDiscountActivityDTO = JSON.parseObject(jsonStr, SkuDiscountActivityDTO.class);
            skuDiscountActivityList.add(skuDiscountActivityDTO);
        }
        //人群限制-过滤不可用的活动
        skuDiscountActivityList = commonOrderOptService.filterPeopleLimit(skuDiscountActivityList, fullSkuWarp.getCustomerUserVO());

        //进行中的活动
        SkuDiscountActivityDTO skuActivity = inProgressActivity(skuDiscountActivityList);
        if(null != skuActivity){
            fullSkuWarp.setSkuDiscountActivityDTO(skuActivity);
        }
    }

    @Override
    public void buildProductPtActivity(ProductVO productVO, CustomerUserVO user) {
        if(FlagEnum.YES.getCode().equals(productVO.getCycleFlag())){
            return;
        }
        PtActivity ptActivity = ptActivityManager.findByProductId(productVO.getId());
        if(null == ptActivity){
            return;
        }
        PtGoods ptGoods = ptGoodsManager.getByActivityId(ptActivity.getId());
        if(null == ptGoods){
            return;
        }
        ProductVO.PtActivityInfo ptActivityInfo = assemblePtActivityInfo(ptActivity, ptGoods);
        if(null != user){
            ptActivityInfo.setHasSubscribe(ptActivityService.validRemind(ptActivity.getId(), user.getId())? CommonRssEnum.RSS.getCode() : CommonRssEnum.NOT_RSS.getCode());
        }
        productVO.setPtActivityInfo(ptActivityInfo);
    }


    @Override
    public List<ProductVO> assembleProductMarketInfo(List<ProductVO> productVOList) {
        CouponRuleListDTO ruleDto = new CouponRuleListDTO();
        ruleDto.setChannel(CouponChannelTypeEnum.WECHAT_MINI.getCode());
        ruleDto.setIsPublic(CouponPublicTypeEnum.PUBLIC.getCode());
        ruleDto.setStatus(CouponStatusTypeEnum.ONLINE.getCode());
        ruleDto.setEndTimeStart(new Date());
        ruleDto.setCouponType(CouponTypeEnum.PRODUCT_COUPON.getCode());

        CustomerUserVO user = null;
        if(UserUtil.isLogged()){
            user = UserUtil.getDetails();
        }

        //查询可领+已领的优惠券
        List<CouponRule> couponRuleList = couponRuleManager.queryEnableReceiveAndReceivedCoupon(ruleDto, user);
        List<Long> ruleIds = StreamUtils.convert(couponRuleList, CouponRule::getId);
        List<CouponRange> couponRangeList = couponRangeDao.queryByRuleIds(ruleIds);
        Map<Long,List<CouponRange>> couponRangeMap = StreamUtils.group(couponRangeList, CouponRange::getRuleId);

        List<PointAmount> pointAmountList = pointAmountManager.getInProgressList();
        List<PointAmountItem> pointAmountItemList = pointAmountItemManager.findByPointAmountIds(StreamUtils.convert(pointAmountList, PointAmount::getId));
        Map<Long, List<PointAmountItem>> pointAmountItemMap = StreamUtils.group(pointAmountItemList, PointAmountItem::getPointAmountId);
        for (ProductVO productVO : productVOList) {
            //促销活动信息
            buildProdctDiscountActivity(productVO, user, true, null);
            //满减满送
            buildProductFullReduce(productVO, user, false, null, 1, null, productVO.getCycleFlag());
            //优惠券信息
            buildProdctAllCouponList(productVO, couponRuleList, couponRangeMap);
            //装载积分抵现活动
            buildProductPointAmount(productVO, user, pointAmountList, pointAmountItemMap);
            //装载商品标签
            PointCommonUtil.buildProductLabel(productVO, 1, null);
            //拼团活动信息
            buildProductPtActivity(productVO, null);
        }
        return productVOList;
    }

    private SkuDiscountActivityDTO noticeActivity(List<SkuDiscountActivityDTO> discountActivityList) {
        List<SkuDiscountActivityDTO> noticeActivityList  = StreamUtils.filter(discountActivityList, x -> DiscountActivityStatusEnum.NOT_STARTED.getCode().equals(x.getStatus()) &&
                (DiscountActivityNoticeEnum.SUCCESS_NOTICE.getCode().equals(x.getNotice()) || DiscountActivityNoticeEnum.AFTER_HOUR_NOTICE.getCode().equals(x.getNotice())));
        Comparator<SkuDiscountActivityDTO> comparatorByDate = (s1, s2) -> ObjectUtils.compare(s1.getStartTime(), s2.getStartTime());
        List<SkuDiscountActivityDTO> noticeActivitySortList = StreamUtils.sort(noticeActivityList, comparatorByDate);
        Date now = new Date();
        if(CollectionUtils.isNotEmpty(noticeActivitySortList)){
            for (SkuDiscountActivityDTO skuDiscountActivityDTO : noticeActivitySortList) {
                if(DiscountActivityNoticeEnum.SUCCESS_NOTICE.getCode().equals(skuDiscountActivityDTO.getNotice()) && skuDiscountActivityDTO.getEndTime().after(now)){
                    return skuDiscountActivityDTO;
                }else if(DiscountActivityNoticeEnum.AFTER_HOUR_NOTICE.getCode().equals(skuDiscountActivityDTO.getNotice())){
                    Date BeforeStart = DateUtil.getDateBeforeHours(skuDiscountActivityDTO.getStartTime(), skuDiscountActivityDTO.getNoticeHour());
                    if(BeforeStart.before(now) && skuDiscountActivityDTO.getEndTime().after(now)){
                        return skuDiscountActivityDTO;
                    }
                }
            }
        }
        return null;
    }

    private SkuDiscountActivityDTO inProgressActivity(List<SkuDiscountActivityDTO> discountActivityList) {
        List<SkuDiscountActivityDTO> inProgressActivityList = StreamUtils.filter(discountActivityList, x -> DiscountActivityStatusEnum.IN_PROGRESS.getCode().equals(x.getStatus()));
        BigDecimal minDiscountPrice = null;
        Long minDiscountPriceActivityId = null;
        Long skuId = null;
        if(CollectionUtils.isNotEmpty(inProgressActivityList)){
            for (SkuDiscountActivityDTO skuDiscountActivityDTO : inProgressActivityList) {
                if(null == minDiscountPrice || skuDiscountActivityDTO.getDiscountPrice().compareTo(minDiscountPrice) < 0){
                    minDiscountPrice = skuDiscountActivityDTO.getDiscountPrice();
                    minDiscountPriceActivityId = skuDiscountActivityDTO.getId();
                    skuId = skuDiscountActivityDTO.getSkuId();
                }
            }
        }
        Map<String, SkuDiscountActivityDTO> discountActivityDTOMap = StreamUtils.toMap(discountActivityList, x -> x.getSkuId() + ":" + x.getId());
        return discountActivityDTOMap.get(skuId + ":" + minDiscountPriceActivityId);
    }

    @Override
    public List<ProductPriceVO> newPrice(List<ProductOrderDTO> dtos) {
        if(CollectionUtils.isEmpty(dtos)){
            return Lists.newArrayList();
        }
        //用户信息
        CustomerUserVO user = null;
        if(UserUtil.isLogged()){
            user = UserUtil.getDetails();
        }

        List<ProductPriceVO> result = Lists.newArrayList();

        List<Long> skuIds = StreamUtils.toList(dtos, ProductOrderDTO::getSkuId);

        List<SkuBaseDTO> skuBaseDTOList = skuManager.details(skuIds);
        Map<Long, SkuBaseDTO> skuBaseDTOMap = StreamUtils.toMap(skuBaseDTOList, SkuBaseDTO::getId);
        for (ProductOrderDTO dto : dtos) {
            ProductPriceVO priceVO = new ProductPriceVO();
            SkuBaseDTO skuBaseDTO = skuBaseDTOMap.get(dto.getSkuId());
            priceVO.setProductId(dto.getProductId());
            priceVO.setSkuId(dto.getSkuId());
            priceVO.setCycleFlag(dto.getCycleFlag());
            priceVO.setTimes(dto.getTimes());
            priceVO.setSalePrice(skuBaseDTO.getSalePrice());
            if(Objects.equals(ProductCycleEnum.CYCLE.getCode(), dto.getCycleFlag())){
                List<SkuAttrDTO> skuAttrs = skuBaseDTO.getSkuAttrs();
                for (SkuAttrDTO attr : skuAttrs) {
                    if(!Objects.equals("times", attr.getAttrName())){
                        continue;
                    }
                    if(Objects.equals(dto.getTimes().toString(), attr.getAttrValue())){
                        BigDecimal price = new BigDecimal(attr.getAttrSpecValue());
                        if(Objects.isNull(dto.getMilkAmount())){
                            priceVO.setSalePrice(price.multiply(BigDecimal.valueOf(dto.getTimes())));
                        }else{
                            priceVO.setSalePrice(price.multiply(BigDecimal.valueOf(dto.getTimes()))
                                    .multiply(BigDecimal.valueOf(dto.getMilkAmount())));
                        }
                    }
                }
            }else{
                ProductBaseDTO productBaseDTO = productManager.basicDetail(skuBaseDTO.getProductId());
                ProductVO productVO = BeanUtils.deepCopy(productBaseDTO, ProductVO.class);
                buildProdctDiscountActivity(productVO, user , false, dto.getSkuId());
                SkuVO skuVO = productVO.getSku(dto.getSkuId());
                if(null != skuVO.getDiscountPrice()){
                    priceVO.setDiscountPrice(skuVO.getDiscountPrice());
                    priceVO.setSharingDiscount(skuVO.getSharingDiscount());
                }
            }
            result.add(priceVO);
        }
        return result;
    }

    @Override
    public void cacheCpsId(String cpsId) {

        CustomerUserVO customerUser = UserUtil.getDetails();

        if(StringUtils.isBlank(cpsId) || StringUtils.isBlank(customerUser.getOpenId())){
            return;
        }

        String key = "CPS:cache:openId:" + customerUser.getOpenId();
        redisOperation.setnx(key, cpsId, 15L, TimeUnit.DAYS);
    }

    @Override
    public void buildProdctAllCouponDetail(ProductVO productVO, List<CouponRule> couponRuleList, CustomerUserVO customerUser) {
        List<CouponVO> couponVOList = new ArrayList<>();
        if(CollectionUtils.isEmpty(productVO.getSkus())){
            return;
        }
        for (SkuVO skuVO: productVO.getSkus()) {
            //判断当前skuVO是否与优惠券互斥
            //判断限时折扣互斥优惠券
            //判断满减满送互斥优惠券
            if (!isShareCoupon(skuVO)) {
                continue;
            }
            BigDecimal cycleMax = null;
            BigDecimal cycleSalePriceSum = null;
            BigDecimal salePriceSum = null;
            List<CouponVO> skuCycleCouponPriceList = new ArrayList<>();
            List<CouponVO> couponPriceList = new ArrayList<>();

            SkuPriceSumDTO skuPriceSumDTO = this.calculatePrice(skuVO, productVO.getCycleFlag(), 1, null);
            salePriceSum = skuPriceSumDTO.getSalePriceSum();
            cycleSalePriceSum = skuPriceSumDTO.getCycleSalePriceSum();
            cycleMax = skuPriceSumDTO.getCycleMax();
            for (CouponRule rule : couponRuleList) {
                //商品是否在券范围内
                Boolean checkCouponFlag = checkCouponProductRange(rule, productVO);
                //购买类型
                Boolean checkBuyTypeFlag = checkBuyTypeWithCycle(rule, productVO);
                //领券时间判断
                Boolean checkCouponTimeFlag = checkCouponTime(rule);
                //优惠券展示信息
                if(checkCouponFlag && checkBuyTypeFlag && checkCouponTimeFlag){
                    CouponVO couponVO = new CouponVO();
                    BeanUtils.copy(rule, couponVO);
                    couponVOList.add(couponVO);
                }else{
                    continue;
                }
//                //校验用户是否超领
//                if (verifCouponOver(customerUser, rule)){
//                    continue;
//                }
                if(CouponTypeEnum.PRODUCT_COUPON.getCode().equals(rule.getCouponType())){
                    //购买类型校验
                    if(CouponBuyTypeEnum.NO_LIMIT.getCode().equals(rule.getBuyType()) || Objects.isNull(rule.getBuyType())){
                        //购买类型不限
                        computeCouponPrice(cycleSalePriceSum, rule, skuCycleCouponPriceList);
                        computeCouponPrice(salePriceSum, rule, couponPriceList);
                    }else if(CouponBuyTypeEnum.SINGLE.getCode().equals(rule.getBuyType())){
                        computeCouponPrice(salePriceSum, rule, couponPriceList);
                    }else if(CouponBuyTypeEnum.CYCLE.getCode().equals(rule.getBuyType())){
                        computeCouponPrice(cycleSalePriceSum, rule, skuCycleCouponPriceList);
                    }
                }
            }
            //单次购买券后价-排序
            if (CollectionUtils.isNotEmpty(couponPriceList)) {
                List<CouponVO> afterSortedCouponPrice = couponPriceList.stream().sorted(Comparator.comparing(CouponVO::getCouponPrice).thenComparing(CouponVO::getEndTime)).collect(Collectors.toList());
                if(CollectionUtils.isNotEmpty(afterSortedCouponPrice) && null != StreamUtils.getFirst(afterSortedCouponPrice)){
                    skuVO.setCouponPrice(StreamUtils.getFirst(afterSortedCouponPrice));
                }
            }
            //周期购单提券后价-排序
            if (CollectionUtils.isNotEmpty(skuCycleCouponPriceList)) {
                for (CouponVO coupon: skuCycleCouponPriceList) {
                    coupon.setCycleSingleCouponPrice(coupon.getCouponPrice().divide(cycleMax,2, BigDecimal.ROUND_UP));
                }
                List<CouponVO> afterSortedCycleCouponPrice = skuCycleCouponPriceList.stream().sorted(Comparator.comparing(CouponVO::getCycleSingleCouponPrice).thenComparing(CouponVO::getEndTime)).collect(Collectors.toList());
                if(CollectionUtils.isNotEmpty(afterSortedCycleCouponPrice) && null != StreamUtils.getFirst(afterSortedCycleCouponPrice)){
                    skuVO.setCycleCouponPrice(StreamUtils.getFirst(afterSortedCycleCouponPrice));
                }
            }
        }
        //优惠券展示信息-排序，满xxx减xx的提示
        this.couponInfo(StreamUtils.distinct(couponVOList, Comparator.comparing(CouponVO::getId)), productVO);
    }


    /**
     * 详情页 优惠券信息+券后价信息 （若单品支持周期购也需展示周期购券后价信息）
     * @param productVO
     * @param couponRuleList
     * @param customerUser
     */
    private void buildProdctAllCouponList(ProductVO productVO, List<CouponRule> couponRuleList, List<CouponRule> receiveCouponRuleList, CustomerUserVO customerUser) {
        List<CouponVO> couponVOList = new ArrayList<>();
        if(CollectionUtils.isEmpty(productVO.getSkus())){
            return;
        }
        for (SkuVO skuVO: productVO.getSkus()) {
            //判断当前skuVO是否与优惠券互斥
            //判断限时折扣互斥优惠券
            //判断满减满送互斥优惠券
            if (!isShareCoupon(skuVO)) {
                continue;
            }
            BigDecimal cycleMax = null;
            BigDecimal cycleSalePriceSum = null;
            BigDecimal salePriceSum = null;
            List<CouponVO> skuCycleCouponPriceList = new ArrayList<>();
            List<CouponVO> couponPriceList = new ArrayList<>();

            SkuPriceSumDTO skuPriceSumDTO = this.calculatePrice(skuVO, productVO.getCycleFlag(), 1, null);
            salePriceSum = skuPriceSumDTO.getSalePriceSum();
            cycleSalePriceSum = skuPriceSumDTO.getCycleSalePriceSum();
            cycleMax = skuPriceSumDTO.getCycleMax();
            for (CouponRule rule : couponRuleList) {
                //商品是否在券范围内
                Boolean checkCouponFlag = checkCouponProductRange(rule, productVO);
                //购买类型
                Boolean checkBuyTypeFlag = checkBuyTypeWithCycle(rule, productVO);
                //领券时间判断
                Boolean checkCouponTimeFlag = checkCouponTime(rule);
                //优惠券展示信息
                if(checkCouponFlag && checkBuyTypeFlag && checkCouponTimeFlag){
                    CouponVO couponVO = new CouponVO();
                    BeanUtils.copy(rule, couponVO);
                    couponVOList.add(couponVO);
                }else{
                    continue;
                }
/*                //校验用户是否超领
                if (verifCouponOver(customerUser, rule)){
                    continue;
                }*/
                if(rule.getIssuedQuantity() - rule.getReceiveCount() <= 0){
                    continue;
                }
                if(CouponTypeEnum.PRODUCT_COUPON.getCode().equals(rule.getCouponType())){
                    //购买类型校验
                    if(CouponBuyTypeEnum.NO_LIMIT.getCode().equals(rule.getBuyType()) || Objects.isNull(rule.getBuyType())){
                        //购买类型不限
                        computeCouponPrice(cycleSalePriceSum, rule, skuCycleCouponPriceList);
                        computeCouponPrice(salePriceSum, rule, couponPriceList);
                    }else if(CouponBuyTypeEnum.SINGLE.getCode().equals(rule.getBuyType())){
                        computeCouponPrice(salePriceSum, rule, couponPriceList);
                    }else if(CouponBuyTypeEnum.CYCLE.getCode().equals(rule.getBuyType())){
                        computeCouponPrice(cycleSalePriceSum, rule, skuCycleCouponPriceList);
                    }
                }
            }
            //单次购买券后价-排序
            if (CollectionUtils.isNotEmpty(couponPriceList)) {
                List<CouponVO> afterSortedCouponPrice = couponPriceList.stream().sorted(Comparator.comparing(CouponVO::getCouponPrice).thenComparing(CouponVO::getEndTime)).collect(Collectors.toList());
                if(CollectionUtils.isNotEmpty(afterSortedCouponPrice) && null != StreamUtils.getFirst(afterSortedCouponPrice)){
                    skuVO.setCouponPrice(StreamUtils.getFirst(afterSortedCouponPrice));
                }
            }
            //周期购单提券后价-排序
            if (CollectionUtils.isNotEmpty(skuCycleCouponPriceList)) {
                for (CouponVO coupon: skuCycleCouponPriceList) {
                    coupon.setCycleSingleCouponPrice(coupon.getCouponPrice().divide(cycleMax,2, BigDecimal.ROUND_UP));
                }
                List<CouponVO> afterSortedCycleCouponPrice = skuCycleCouponPriceList.stream().sorted(Comparator.comparing(CouponVO::getCycleSingleCouponPrice).thenComparing(CouponVO::getEndTime)).collect(Collectors.toList());
                if(CollectionUtils.isNotEmpty(afterSortedCycleCouponPrice) && null != StreamUtils.getFirst(afterSortedCycleCouponPrice)){
                    skuVO.setCycleCouponPrice(StreamUtils.getFirst(afterSortedCycleCouponPrice));
                }
            }
        }
        //立即领券按钮
        this.getNowCoupon(productVO, receiveCouponRuleList, customerUser);
        //优惠券展示信息-排序，满xxx减xx的提示
        this.couponInfo(StreamUtils.distinct(couponVOList, Comparator.comparing(CouponVO::getId)), productVO);
    }

    private Boolean checkCouponTime(CouponRule rule) {
        Boolean checkCouponFlag = true;
        if(Objects.nonNull(rule.getEffectiveTime())){
            CouponInfo info = computeTime(rule, new Date());
            if(info.getStartTime().after(rule.getEndTime())){
                checkCouponFlag = false;
            }
        }
        return checkCouponFlag;
    }

    @NotNull
    private Boolean checkCouponProductRange(CouponRule rule, ProductVO productVO) {
        Boolean checkRangeFlag = false;
        String keyCoupon = CouponUtil.COUPON_SINGLE_PRODUCT + rule.getId();
        checkRangeFlag = redisOperation.hget(keyCoupon, String.valueOf(productVO.getId()));
        if(Objects.isNull(checkRangeFlag)){
            checkRangeFlag = couponRuleManager.checkCouponAllRange(rule.getId(), rule.getCouponRange(), BeanUtils.deepCopy(productVO, ProductBaseDTO.class));
            redisOperation.hset(keyCoupon, String.valueOf(productVO.getId()), checkRangeFlag);
            redisOperation.expire(keyCoupon, DateUtil.getRemainSeconds(new Date()), TimeUnit.SECONDS);
        }
        return checkRangeFlag;
    }

    private boolean verifCouponOver(CustomerUserVO customerUser, CouponRule rule) {
        if (Objects.nonNull(customerUser)) {
            List<CouponInfo> couponInfoList = couponInfoDao.queryEnableProductCouponByRuleId(customerUser.getId(), rule.getId());
            if(CollectionUtils.isEmpty(couponInfoList)){
                if(couponRuleManager.remaining(rule, customerUser.getId()) <= 0){
                    return true;
                }
                Integer sumReceivedNum = couponInfoDao.countReceivedByRuleId(rule.getId());
                if(rule.getIssuedQuantity() - sumReceivedNum <= 0){
                    return true;
                }
            }
        }else{
            Integer sumReceivedNum = couponInfoDao.countReceivedByRuleId(rule.getId());
            if(rule.getIssuedQuantity() - sumReceivedNum <= 0){
                return true;
            }
        }
        return false;
    }

    private BigDecimal getDiscountPrice(SkuVO skuVO, BigDecimal salePrice) {
        return getDiscountPrice(skuVO.getDiscountActivityId(), skuVO.getDiscountPrice(), skuVO.getDiscountActivityStatus(), salePrice);
    }

    private BigDecimal getDiscountPrice(SkuBaseDTO skuBaseDTO, BigDecimal salePrice) {
        return getDiscountPrice(skuBaseDTO.getDiscountActivityId(), skuBaseDTO.getDiscountPrice(),skuBaseDTO.getDiscountActivityStatus(), salePrice);
    }

    private BigDecimal getDiscountPrice(Long discountActivityId, BigDecimal discountPrice, Integer status, BigDecimal salePrice){
        if(null != discountActivityId && null != discountPrice && DiscountActivityStatusEnum.IN_PROGRESS.getCode().equals(status)){
            salePrice = discountPrice;
        }
        return salePrice;
    }


    private void couponInfo(List<CouponVO> couponVOList, ProductVO productVO) {
        //优惠券展示信息-排序
        if (CollectionUtils.isNotEmpty(couponVOList)) {
            List<CouponVO> afterSorted = new ArrayList<>();
            List reduceCouponList= couponVOList.stream().filter(couponVO -> CouponWayEnum.FULL_REDUCE.getCode().equals(couponVO.getCouponWay())).sorted(Comparator.comparing(CouponVO::getAmountReduce).reversed()).collect(Collectors.toList());
            List discountCouponList= couponVOList.stream().filter(couponVO -> CouponWayEnum.UNLIMITED.getCode().equals(couponVO.getCouponWay())).sorted(Comparator.comparing(couponVO -> couponVO.getDiscount())).collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(reduceCouponList)){
                afterSorted.addAll(reduceCouponList);
            }
            if(CollectionUtils.isNotEmpty(discountCouponList)){
                afterSorted.addAll(discountCouponList);
            }
            productVO.setCouponRuleList(afterSorted);
        } else {
            productVO.setCouponRuleList(couponVOList);
        }
    }

    private void getNowCoupon(ProductVO productVO, List<CouponRule> receiveCouponRuleList, CustomerUserVO customerUser) {
        //立即领券
        List<CouponVO> receiveCouponList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(productVO.getSkus())) {
            for (CouponRule rule : receiveCouponRuleList) {
                //商品范围校验
                Boolean checkRangeFlag = checkCouponProductRange(rule, productVO);
                //领券时间校验
                Boolean checkCouponTimeFlag = checkCouponTime(rule);

                if(!checkRangeFlag || !checkCouponTimeFlag){
                    continue;
                }
                CouponVO couponVO = new CouponVO();
                BeanUtils.copy(rule, couponVO);
                if (Objects.nonNull(customerUser)) {
                    couponVO.setRemainingNum(couponRuleManager.remaining(rule, customerUser.getId()));
                }
                couponVO.setSumRemainingNum(rule.getIssuedQuantity() - rule.getReceiveCount());
                if(Objects.nonNull(couponVO.getRemainingNum()) && couponVO.getRemainingNum() <=0 || couponVO.getSumRemainingNum() <= 0){
                    continue;
                }
                receiveCouponList.add(couponVO);
            }
        }

        //立即领券-排序
        if (CollectionUtils.isNotEmpty(receiveCouponList)) {
            List<CouponVO> afterSorted = new ArrayList<>();

            List reduceCouponList = receiveCouponList.stream().filter(couponVO -> CouponWayEnum.FULL_REDUCE.getCode().equals(couponVO.getCouponWay())).sorted(Comparator.comparing(CouponVO::getAmountReduce).reversed()).collect(Collectors.toList());
            List discountCouponList = receiveCouponList.stream().filter(couponVO -> CouponWayEnum.UNLIMITED.getCode().equals(couponVO.getCouponWay())).sorted(Comparator.comparing(couponVO -> couponVO.getDiscount())).collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(reduceCouponList)){
                afterSorted.addAll(reduceCouponList);
            }

            if(CollectionUtils.isNotEmpty(discountCouponList)){
                afterSorted.addAll(discountCouponList);
            }
            productVO.setReceiveCouponRuleList(afterSorted);
        } else {
            productVO.setReceiveCouponRuleList(receiveCouponList);
        }
    }

    /**
     * 计算券后价
     * @param salePriceSum
     * @param rule
     * @param couponPriceList
     */
    private void computeCouponPrice(BigDecimal salePriceSum, CouponRule rule, List<CouponVO> couponPriceList){
        BigDecimal couponPrice = CouponUtil.computeCouponPrice(salePriceSum, rule);
        if(null == couponPrice){
            return;
        }
        CouponVO couponVO = new CouponVO();
        BeanUtils.copy(rule, couponVO);
        couponVO.setRuleId(rule.getId());
        couponVO.setCouponPrice(couponPrice);
        couponPriceList.add(couponVO);
    }
    
    /**
     * 把skuAttrs分别组装到各个属性上
     * @param productVO
     */
    private void buildProductSku(ProductVO productVO){
        List<SkuVO> skus = productVO.getSkus();
        if (CollectionUtils.isNotEmpty(skus)) {
            for (SkuVO skuVO : skus) {
                skuVO.setPurchase(0);
                for (SkuAttrDTO skuAttr : skuVO.getSkuAttrs()) {
                    switch (skuAttr.getAttrName()){
                        case "purchase":
                            skuVO.setPurchase(Integer.valueOf(skuAttr.getAttrValue()));
                            break;
                        case "ownerId":
                            skuVO.setOwnerId(skuAttr.getAttrName());
                            break;
                        case "safeStockQte":
                            skuVO.setSafeStockQte(Long.getLong(skuAttr.getAttrName()));
                            break;
                        case "times":
                            if(Objects.isNull(skuVO.getTimesList())){
                                skuVO.setTimesList(new ArrayList<>());
                            }
                            skuVO.getTimesList().add(new TimesVO(Integer.valueOf(skuAttr.getAttrValue()), new BigDecimal(skuAttr.getAttrSpecValue()), skuAttr.getSort()));
                            break;
                        default:
                            break;
                    }
                }
                //周期排序
                if(CollectionUtils.isNotEmpty(skuVO.getTimesList())){
                    List<TimesVO> sortedCycleList = skuVO.getTimesList().stream().sorted(Comparator.comparing(TimesVO::getTimes)).collect(Collectors.toList());
                    skuVO.setTimesList(sortedCycleList);
                    BigDecimal cycleCheapPrice = sortedCycleList.get(0).getPrice();
                    for (TimesVO timesVO: sortedCycleList) {
                        BigDecimal cyclePrice = timesVO.getPrice();
                        if(cycleCheapPrice.compareTo(cyclePrice) > 0){
                            cycleCheapPrice = cyclePrice;
                        }
                    }
                    skuVO.setCyclePrice(cycleCheapPrice);
                }
            }
        }
    }

    private SkuPriceSumDTO getSkuSalePrice(SkuVO skuVO, Integer cycleFlag, BigDecimal price, Integer count, Integer times){
        BigDecimal salePriceSum = null;
        BigDecimal cycleSalePriceSum = null;
        BigDecimal cycleMax = null;
        if(BasicFlagEnum.YES.getKey().equals(cycleFlag)){
            List<SkuAttrDTO> skuAttrList = skuVO.getSkuAttrs().stream().filter(skuAttr -> "times".equals(skuAttr.getAttrName())).collect(Collectors.toList());
            if(null != times){
                skuAttrList = StreamUtils.filter(skuAttrList, x-> String.valueOf(times).equals(x.getAttrValue()));
            }
            for (SkuAttrDTO skuAttrDTO: skuAttrList) {
                skuAttrDTO.setAttrValueInt(Integer.valueOf(skuAttrDTO.getAttrValue()));
            }
            //取期数最多的周期购
            skuAttrList = skuAttrList.stream().sorted(Comparator.comparing(SkuAttrDTO::getAttrValueInt).reversed()).collect(Collectors.toList());
            if(CollectionUtils.isEmpty(skuAttrList)){
                throw new BusinessException("周期购期数选择错误,请重新选择！");
            }else{
                SkuAttrDTO skuAttrDTO = StreamUtils.getFirst(skuAttrList);
                BigDecimal cyclePrice = new BigDecimal(skuAttrDTO.getAttrSpecValue());
                cycleMax = new BigDecimal(skuAttrDTO.getAttrValue());
                cycleSalePriceSum = cyclePrice.multiply(cycleMax).multiply(BigDecimal.valueOf(count));
            }
        }
        salePriceSum = SkuUtil.calculatePrice(price, count);
        return SkuPriceSumDTO.builder()
                .salePriceSum(salePriceSum)
                .cycleSalePriceSum(cycleSalePriceSum)
                .cycleMax(cycleMax)
                .build();
    }

    private SkuPriceSumDTO calculatePrice(SkuVO skuVO, Integer cycleFlag, Integer count, Integer times){
        BigDecimal salePrice = skuVO.getSalePrice();

        //有满减满送价就用满减满送价，没有满减满送价有限时折扣价就用限时折扣价
        FullReduceVO fullReduceVO = skuVO.getFullReduce();
        if(null != fullReduceVO){
            SkuPriceSumDTO skuPriceSumDTO = new SkuPriceSumDTO();
            if(null == fullReduceVO.getCycleMax()){
                skuPriceSumDTO.setSalePriceSum(fullReduceVO.getSumFullReducePrice());
                return skuPriceSumDTO;
            }else{
                skuPriceSumDTO.setCycleSalePriceSum(fullReduceVO.getSumFullReducePrice());
                skuPriceSumDTO.setCycleMax(fullReduceVO.getCycleMax());
                return skuPriceSumDTO;
            }
        }else if(null != skuVO.getDiscountPrice()){
            salePrice = this.getDiscountPrice(skuVO, salePrice);
        }

        return this.getSkuSalePrice(skuVO, cycleFlag, salePrice, count, times);
    }

    /**
     * 商品封装券后价信息+优惠券信息  （若单品支持周期购 只展示周期购单提（券后）价）
     * @param productVO
     * @param couponRuleList
     */
    @Override
    public void buildProdctAllCouponList(ProductVO productVO, List<CouponRule> couponRuleList, Map<Long, List<CouponRange>> couponRangeMap) {
        this.buildProductSku(productVO);
        List<CouponVO> couponVOList = new ArrayList<>();
        for (SkuVO skuVO: productVO.getSkus()) {
            //判断当前skuVO是否与优惠券互斥
            //判断限时折扣互斥优惠券
            //判断满减满送互斥优惠券
            if (!isShareCoupon(skuVO)) {
                continue;
            }
            //计算sku的标价和售价之和
            SkuPriceSumDTO skuPriceSumDTO = this.calculatePrice(skuVO, productVO.getCycleFlag(), 1, null);
            BigDecimal salePriceSum = null == skuPriceSumDTO.getCycleSalePriceSum() ? skuPriceSumDTO.getSalePriceSum(): skuPriceSumDTO.getCycleSalePriceSum();

            List<CouponVO> couponPriceList = new ArrayList<>();
            for (CouponRule rule : couponRuleList) {
                List<CouponRange> couponRanges = couponRangeMap.get(rule.getId());
                //商品范围校验
                Boolean checkRangeFlag = CouponUtil.checkProductCouponRange(rule.getId(), rule.getCouponRange(),
                        BeanUtils.deepCopy(productVO, ProductBaseDTO.class), couponRanges);
                //购买类型校验
                Boolean checkBuyTypeFlag = this.checkBuyTypeWithCycle(rule, productVO);
                //校验领券时间是否已过优惠券领取结束时间
                Boolean checkCouponTimeFlag = this.checkCouponTime(rule);

                if(!checkRangeFlag || !checkBuyTypeFlag || !checkCouponTimeFlag){
                    continue;
                }
                //优惠券需要计算券后价
                if(CouponTypeEnum.PRODUCT_COUPON.getCode().equals(rule.getCouponType())){
                    //计算券后价
                    BigDecimal couponPrice = CouponUtil.computeCouponPrice(salePriceSum, rule);
                    //券后价-装载
                    if (null != couponPrice) {
                        CouponVO couponVO = new CouponVO();
                        BeanUtils.copy(rule, couponVO);
                        couponVO.setRuleId(rule.getId());
                        couponVO.setCouponPrice(couponPrice);
                        if(BasicFlagEnum.YES.getKey().equals(productVO.getCycleFlag())){
                            BigDecimal cycleSingleCouponPrice = couponPrice.divide(skuPriceSumDTO.getCycleMax(),2, BigDecimal.ROUND_UP);
                            couponVO.setCouponPrice(cycleSingleCouponPrice);
                        }
                        couponPriceList.add(couponVO);
                    }
                }
                //优惠券展示信息-装载
                CouponVO couponVO = new CouponVO();
                BeanUtils.copy(rule, couponVO);
                couponVOList.add(couponVO);
            }
            //券后价-排序
            if (CollectionUtils.isNotEmpty(couponPriceList)) {
                List<CouponVO> afterSortedCouponPrice = couponPriceList.stream().sorted(Comparator.comparing(CouponVO::getCouponPrice).thenComparing(CouponVO::getEndTime)).collect(Collectors.toList());
                if(CollectionUtils.isNotEmpty(afterSortedCouponPrice) && null != StreamUtils.getFirst(afterSortedCouponPrice)){
                    skuVO.setCouponPrice(StreamUtils.getFirst(afterSortedCouponPrice));
                }
            }
        }
        //优惠券展示信息-排序
        this.couponInfo(StreamUtils.distinct(couponVOList, Comparator.comparing(CouponVO::getId)), productVO);
    }

    @Override
    public void buildProdctAllCouponListWithAccess(ProductVO productVO, List<CouponRule> couponRuleList, Map<Long, List<CouponRange>> couponRangeMap) {
        if (!productVO.getShareDiscount().contains("2")) {
            return;
        }
        this.buildProdctAllCouponList(productVO, couponRuleList, couponRangeMap);
    }

    private Boolean checkBuyTypeWithCycle(CouponRule rule, ProductVO productVO) {
        boolean checkBuyTypeFlag = true;
        if(Objects.isNull(rule.getBuyType())){
            return checkBuyTypeFlag;
        }
        if(CouponBuyTypeEnum.NO_LIMIT.getCode().equals(rule.getBuyType())){
            //购买类型不限
        }else if(CouponBuyTypeEnum.SINGLE.getCode().equals(rule.getBuyType())){
            //单次购买券，单品支持周期购---若单品支持周期购 只展示周期购单提（券后）价，不用计算单品的单次价
            if(BasicFlagEnum.YES.getKey().equals(productVO.getCycleFlag())){
                checkBuyTypeFlag = false;
            }
        }else if(CouponBuyTypeEnum.CYCLE.getCode().equals(rule.getBuyType())){
            //周期购买券，单品不支持周期购
            if(!BasicFlagEnum.YES.getKey().equals(productVO.getCycleFlag())){
                checkBuyTypeFlag = false;
            }
        }
        return checkBuyTypeFlag;
    }

    private boolean isShareCoupon(SkuVO skuVO) {
        if(null != skuVO.getDiscountActivityId()
                && DiscountActivityStatusEnum.IN_PROGRESS.getCode().equals(skuVO.getDiscountActivityStatus())
                && DiscountActivityShareTypeEnum.NO_COUPON.getCode().equals(skuVO.getSharingDiscount())){
            return false;
        }

        if(null != skuVO.getFullReduce()
                && !FullReduceShareTypeEnum.isShareCoupon(skuVO.getFullReduce().getShareType())){
            return false;
        }
        if(Objects.nonNull(skuVO.getFreeTrialSubActivityId())){
            if(!skuVO.getFreeTrialSubActivityShareDiscount().contains("2")){
                return false;
            }
        }
        return true;
    }


    private CouponInfo computeTime(CouponRule couponRule, Date time) {
        CouponInfo couponInfo = new CouponInfo();
        String currentDate = DateUtil.dateToString(time, DateUtil.SIMPLE_YMD) + " 00:00:00";

        //生效时间 生效延时时间(天数)
        int delayTime = null == couponRule.getDelayTime() ? 0 : couponRule.getDelayTime().intValue();
        Date startTime = 0 == delayTime ? DateUtil.stringToDate(currentDate,DateUtil.SIMPLE_FMT) : DateUtil.getNextDate(currentDate, delayTime);
        couponInfo.setStartTime(startTime);
        //失效时间 有效天数
        int effectTime = null == couponRule.getEffectiveTime() ? 0 : couponRule.getEffectiveTime().intValue();
        int extend = 0 == delayTime ? effectTime + delayTime : effectTime + delayTime;
        Date endTime = new Date(DateUtil.getNextDate(currentDate, extend).getTime() - 1000);

        couponInfo.setEndTime(endTime);
        return couponInfo;
    }

    @Override
    public ProductVO detail(Long id) {
        ProductBaseDTO dto = productManager.basicDetail(id);
        if (null == dto) {
            return null;
        }
        ProductVO productVO = BeanUtils.deepCopy(dto, ProductVO.class);
        // 图片处理
        List<String> urlsByProductId = productMediaService.getUrlsByProductId(id);
        productVO.setImageUrls(urlsByProductId);
        productVO.setSkus(skuService.getAllSkuByProdcut(id));
        productVO.setSpecs(specService.getAllSpecByProdcut(id));

        //周期购
        if(BasicFlagEnum.YES.getKey().equals(productVO.getCycleFlag()) || ProductTypeEnum.NORMAL.getCode().equals(productVO.getProductType())){
            ProductCycleDTO productCycleDTO = productManager.buildProductCycle(productVO.getId());
            productVO.setCycleList(productCycleDTO.getCycleList());
            productVO.setCycleDefault(productCycleDTO.getCycleDefault());
        }
        //用户信息
        CustomerUserVO user = null;
        if(UserUtil.isLogged()){
            user = UserUtil.getDetails();
        }
        // 库存处理
        stockProcess(productVO);
        if (ProductTypeEnum.PACKAGE.getCode().equals(productVO.getProductType())
            || ProductTypeEnum.ENTITY_CARD.getCode().equals(productVO.getProductType())
            || ProductTypeEnum.SINGLE_PRODUCT.getCode().equals(productVO.getProductType())) {

            CouponRuleListDTO ruleDto = new CouponRuleListDTO();
            ruleDto.setChannel(CouponChannelTypeEnum.WECHAT_MINI.getCode());
            ruleDto.setIsPublic(CouponPublicTypeEnum.PUBLIC.getCode());
            ruleDto.setStatus(CouponStatusTypeEnum.ONLINE.getCode());
            ruleDto.setEndTimeStart(new Date());
            ruleDto.setCouponType(CouponTypeEnum.PRODUCT_COUPON.getCode());
            ruleDto.setNotReceiveCrowd(CouponReceiveCrowdTypeEnum.FIRST_JOIN.getCode());

            List<PointAmount> pointAmountList = pointAmountManager.getInProgressList();
            List<PointAmountItem> pointAmountItemList = pointAmountItemManager.findByPointAmountIds(StreamUtils.convert(pointAmountList, PointAmount::getId));
            Map<Long, List<PointAmountItem>> pointAmountItemMap = StreamUtils.group(pointAmountItemList, PointAmountItem::getPointAmountId);
            
            //查询可领+已领的优惠券
            ProductCouponDTO productCouponDTO = couponRuleManager.queryEnableReceiveAndReceivedCouponDTO(ruleDto, user);
            //促销活动信息
            buildProdctDiscountActivity(productVO, user,true, null);
            //满减满送
            buildProductFullReduce(productVO, user, true, null, 1, null, productVO.getCycleFlag());
            //优惠券信息
            buildProdctAllCouponList(productVO, productCouponDTO.getCouponRuleList(), productCouponDTO.getReceiveCouponRuleList(), user);
            //装载积分抵现活动
            buildProductPointAmount(productVO, user, pointAmountList, pointAmountItemMap);
            //装载商品标签
            PointCommonUtil.buildProductLabel(productVO, 2, null);
            //拼团活动信息
            buildProductPtActivity(productVO, user);
        }


        productVO.setSkuMap(skuMapProcess(productVO.getSkus()));

        if (PrivilegeFlagEnum.Privilege.getCode().equals(dto.getPrivilegeFlag())) {
            List<PrivilegeItemUserVO> privilegeItemUserVOS = privilegeActManager.findByItemId(productVO.getId());
            if (CollectionUtils.isNotEmpty(privilegeItemUserVOS)) {
                CustomerUserVO customerUserVO = UserUtil.getDetails();
                List<Long> labelIds = privilegeItemUserVOS.stream().map(PrivilegeItemUserVO::getLableId).collect(Collectors.toList());
                List<PrivilegeItemUserVO> privilegeLables = privilegeActManager.findPrivilegeLables(labelIds, PrivilegeRelationTypeEnum.USER_ID.getCode(), customerUserVO.getId());
                List<Long> labelContainIds = StreamUtils.toList(privilegeLables, PrivilegeItemUserVO::getLableId);
                List<PrivilegeLableVO> lableVOS = privilegeActManager.findPrivilegeLablesById(labelContainIds);
                if (CollectionUtils.isNotEmpty(lableVOS)) {
                    List<Date> collect = lableVOS.stream().map(PrivilegeLableVO::getEndTime).sorted(Comparator.reverseOrder()).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(collect)) {
                        String date = DateUtil.dateToString(collect.get(0), DateUtil.SIMPLE_FMT_ZH);
                        productVO.setEndTime(date);
                    }
                }
            }
        }
        //测评是否存在
        Integer trialCommentCount = freeTrialCommentManager.existBySpuId(id);
        productVO.setTrialCommentCount(trialCommentCount);

        productVO.setCurrentTime(System.currentTimeMillis());
        return productVO;
    }

    @Override
    public ProductVO detailBySkuId(Long id, Long skuId, String shareDiscount, BigDecimal resetSalePrice) {
        ProductBaseDTO dto = productManager.basicDetailNoSku(id);
        if (null == dto) {
            return null;
        }
        ProductVO productVO = BeanUtils.deepCopy(dto, ProductVO.class);
        // 图片处理
        List<String> urlsByProductId = productMediaService.getUrlsByProductId(id);
        productVO.setImageUrls(urlsByProductId);
        SkuVO specificSkuInfo = skuService.getSpecificSkuInfo(skuId);
        specificSkuInfo.setSalePrice(resetSalePrice);
        productVO.setSkus(Lists.newArrayList(specificSkuInfo));
        productVO.setSpecs(specService.getAllSpecByProdcut(id));

        //周期购
        if(BasicFlagEnum.YES.getKey().equals(productVO.getCycleFlag()) || ProductTypeEnum.NORMAL.getCode().equals(productVO.getProductType())){
            ProductCycleDTO productCycleDTO = productManager.buildProductCycle(productVO.getId());
            productVO.setCycleList(productCycleDTO.getCycleList());
            productVO.setCycleDefault(productCycleDTO.getCycleDefault());
        }
        //用户信息
        CustomerUserVO user = null;
        if(UserUtil.isLogged()){
            user = UserUtil.getDetails();
        }
        // 库存处理
        stockProcess(productVO);
        if (ProductTypeEnum.PACKAGE.getCode().equals(productVO.getProductType())
                || ProductTypeEnum.ENTITY_CARD.getCode().equals(productVO.getProductType())
                || ProductTypeEnum.SINGLE_PRODUCT.getCode().equals(productVO.getProductType())) {

            CouponRuleListDTO ruleDto = new CouponRuleListDTO();
            ruleDto.setChannel(CouponChannelTypeEnum.WECHAT_MINI.getCode());
            ruleDto.setIsPublic(CouponPublicTypeEnum.PUBLIC.getCode());
            ruleDto.setStatus(CouponStatusTypeEnum.ONLINE.getCode());
            ruleDto.setEndTimeStart(new Date());
            ruleDto.setCouponType(CouponTypeEnum.PRODUCT_COUPON.getCode());
            ruleDto.setNotReceiveCrowd(CouponReceiveCrowdTypeEnum.FIRST_JOIN.getCode());

            List<PointAmount> pointAmountList = pointAmountManager.getInProgressList();
            List<PointAmountItem> pointAmountItemList = pointAmountItemManager.findByPointAmountIds(StreamUtils.convert(pointAmountList, PointAmount::getId));
            Map<Long, List<PointAmountItem>> pointAmountItemMap = StreamUtils.group(pointAmountItemList, PointAmountItem::getPointAmountId);

            //查询可领+已领的优惠券
            ProductCouponDTO productCouponDTO = couponRuleManager.queryEnableReceiveAndReceivedCouponDTO(ruleDto, user);
            //促销活动信息
            buildProdctDiscountActivity(productVO, user,true, null);
            if (shareDiscount.contains("3")) {
                //满减满送
                buildProductFullReduce(productVO, user, true, null, 1, null, productVO.getCycleFlag());
            }
            if (shareDiscount.contains("2")) {
                //优惠券信息
                buildProdctAllCouponList(productVO, productCouponDTO.getCouponRuleList(), productCouponDTO.getReceiveCouponRuleList(), user);
            }
            if (shareDiscount.contains("1")) {
                //装载积分抵现活动
                buildProductPointAmount(productVO, user, pointAmountList, pointAmountItemMap);
            }
            //装载商品标签
            PointCommonUtil.buildProductLabel(productVO, 2, null);
            //拼团活动信息
            buildProductPtActivity(productVO, user);
        }


        productVO.setSkuMap(skuMapProcess(productVO.getSkus()));

        if (PrivilegeFlagEnum.Privilege.getCode().equals(dto.getPrivilegeFlag())) {
            List<PrivilegeItemUserVO> privilegeItemUserVOS = privilegeActManager.findByItemId(productVO.getId());
            if (CollectionUtils.isNotEmpty(privilegeItemUserVOS)) {
                CustomerUserVO customerUserVO = UserUtil.getDetails();
                List<Long> labelIds = privilegeItemUserVOS.stream().map(PrivilegeItemUserVO::getLableId).collect(Collectors.toList());
                List<PrivilegeItemUserVO> privilegeLables = privilegeActManager.findPrivilegeLables(labelIds, PrivilegeRelationTypeEnum.USER_ID.getCode(), customerUserVO.getId());
                List<Long> labelContainIds = StreamUtils.toList(privilegeLables, PrivilegeItemUserVO::getLableId);
                List<PrivilegeLableVO> lableVOS = privilegeActManager.findPrivilegeLablesById(labelContainIds);
                if (CollectionUtils.isNotEmpty(lableVOS)) {
                    List<Date> collect = lableVOS.stream().map(PrivilegeLableVO::getEndTime).sorted(Comparator.reverseOrder()).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(collect)) {
                        String date = DateUtil.dateToString(collect.get(0), DateUtil.SIMPLE_FMT_ZH);
                        productVO.setEndTime(date);
                    }
                }
            }
        }
        //测评是否存在
        Integer trialCommentCount = freeTrialCommentManager.existBySpuId(id);
        productVO.setTrialCommentCount(trialCommentCount);

        productVO.setCurrentTime(System.currentTimeMillis());
        return productVO;
    }

    @Override
    public PageVO<ProductVO> searchHot(PageParams params) {
        String hotProductKey = "hotProductListV3:pageNum:" + params.getPageNum() + ":pageSize:"  + params.getPageSize();
        Map<String, Object> cacheMap = redisOperation.get(hotProductKey);
        List<Long> productList = null;
        Pagination pagination =  null;
        try {
            productList = null == cacheMap ? null : (List<Long>) cacheMap.get("list");
            pagination =  null == cacheMap ? null : (Pagination) cacheMap.get("page");
        }catch (Exception e){
            log.error("热销爆款缓存数据转换异常. cacheMap:{}", JSON.toJSONString(cacheMap));
        }
        if(null == productList || null == pagination){
            productList = new ArrayList<>();
            if(params.getPageNum().equals(PageConstants.DEFAULT_PAGE_NUM)){
                List<HotProduct> hotProductList = hotProductDao.list();
                productList = StreamUtils.convertFilter(hotProductList, HotProduct::getProductId, Objects::nonNull);
            }
            PageHelper.startPage(params.getPageNum(), params.getPageSize());
            List<ProductHotDTO> productHotDTOList = productManager.hotProductList(null, 1, null);
            List<Long> productHotList = productHotDTOList.stream().map(ProductHotDTO::getProductId).collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(productHotList)){
                productList.addAll(productHotList);
            }
            productList = this.dedup(productList);
            pagination = PageUtils.extract(productHotDTOList);
            Map<String, Object> map = new HashMap<>();
            map.put("list", productList);
            map.put("page", pagination);
            redisOperation.setex(hotProductKey, map, 10, TimeUnit.SECONDS);
        }

        if(CollectionUtils.isEmpty(productList)){
            return PageUtils.emptyPage(params);
        }
        List<ProductBaseDTO> baseDTOList = productManager.onSaleProductDetailList(productList);
        List<ProductVO> productVoList = BeanUtils.deepListCopy(baseDTOList, ProductVO.class);

        CouponRuleListDTO ruleDto = new CouponRuleListDTO();
        ruleDto.setChannel(CouponChannelTypeEnum.WECHAT_MINI.getCode());
        ruleDto.setIsPublic(CouponPublicTypeEnum.PUBLIC.getCode());
        ruleDto.setStatus(CouponStatusTypeEnum.ONLINE.getCode());
        ruleDto.setEndTimeStart(new Date());
        ruleDto.setCouponType(CouponTypeEnum.PRODUCT_COUPON.getCode());

        CustomerUserVO user = null;
        if(UserUtil.isLogged()){
            user = UserUtil.getDetails();
        }

        //查询可领+已领的优惠券
        List<CouponRule> couponRuleList = couponRuleManager.queryEnableReceiveAndReceivedCoupon(ruleDto, user);
        List<Long> ruleIds = StreamUtils.convert(couponRuleList, CouponRule::getId);
        List<CouponRange> couponRangeList = couponRangeDao.queryByRuleIds(ruleIds);
        Map<Long,List<CouponRange>> couponRangeMap = StreamUtils.group(couponRangeList, CouponRange::getRuleId);

        List<PointAmount> pointAmountList = pointAmountManager.getInProgressList();
        List<PointAmountItem> pointAmountItemList = pointAmountItemManager.findByPointAmountIds(StreamUtils.convert(pointAmountList, PointAmount::getId));
        Map<Long, List<PointAmountItem>> pointAmountItemMap = StreamUtils.group(pointAmountItemList, PointAmountItem::getPointAmountId);
        for (ProductVO productVO : productVoList) {
            //促销活动信息
            buildProdctDiscountActivity(productVO, user, true, null);
            //满减满送
            buildProductFullReduce(productVO, user, false, null, 1, null, productVO.getCycleFlag());
            //优惠券信息
            buildProdctAllCouponList(productVO, couponRuleList, couponRangeMap);
            //装载积分抵现活动
            buildProductPointAmount(productVO, user, pointAmountList, pointAmountItemMap);
            //装载商品标签
            PointCommonUtil.buildProductLabel(productVO, 1, null);
            //拼团活动信息
            buildProductPtActivity(productVO, null);
        }

        return PageUtils.toPageVO(pagination, productVoList);
    }

    private List<Long> dedup(List<Long> list){
        List<Long> listNew = new ArrayList<>();
        Set set = new HashSet();
        for (Long spuId : list) {
            if(set.add(spuId)){
                listNew.add(spuId);
            }
        }
        return listNew;
    }

    @Override
    public SkuPopVO productRate(ProductOrderDTO productOrderDTO) {
        SkuPopVO skuPopVO = new SkuPopVO();
        CouponRuleListDTO ruleDto = new CouponRuleListDTO();
        ruleDto.setChannel(CouponChannelTypeEnum.WECHAT_MINI.getCode());
        ruleDto.setIsPublic(CouponPublicTypeEnum.PUBLIC.getCode());
        ruleDto.setStatus(CouponStatusTypeEnum.ONLINE.getCode());
        ruleDto.setEndTimeStart(new Date());
        ruleDto.setCouponType(CouponTypeEnum.PRODUCT_COUPON.getCode());

        CustomerUserVO user = null;
        if(UserUtil.isLogged()){
            user = UserUtil.getDetails();
        }
        //productVO 只留前端传的sku
        ProductBaseDTO productBaseDTO = productManager.basicDetail(productOrderDTO.getProductId());
        ProductVO productBaseVO = BeanUtils.deepCopy(productBaseDTO, ProductVO.class);
        //满额试用&0元尝鲜
        buildProdctFullTrialFreeTaste(productBaseVO, productOrderDTO);
        //促销活动信息
        buildProdctDiscountActivity(productBaseVO, user,false, productOrderDTO.getSkuId());
        SkuVO skuVO = productBaseVO.getSku(productOrderDTO.getSkuId());
        //满减满送
        buildProductFullReduce(productBaseVO, user, false, productOrderDTO.getSkuId(), productOrderDTO.getCount(), productOrderDTO.getTimes(), productOrderDTO.getCycleFlag());
        skuPopVO.setFullReduce(productBaseVO.getSku(productOrderDTO.getSkuId()).getFullReduce());
        //查询可领+已领的优惠券
        List<CouponRule> couponRuleList = couponRuleManager.queryEnableReceiveAndReceivedCoupon(ruleDto, user);
        //商品装载优惠券信息(券后价)
        List<CouponVO> couponVOList = buildProdctCouponByOrderList(productBaseVO, couponRuleList, productOrderDTO);
        skuPopVO.setCouponPriceList(couponVOList);
        if(BasicFlagEnum.YES.getKey().equals(productOrderDTO.getCycleFlag())){
            //周期购
            String cycleSinglePrice = skuVO.getCycleSinglePrice(String.valueOf(productOrderDTO.getTimes()));
            if(StringUtils.isNotBlank(cycleSinglePrice)){
                skuPopVO.setSalePrice(new BigDecimal(cycleSinglePrice));
            }
        }else{
            //非周期购
            skuPopVO.setSalePrice(Objects.isNull(skuVO.getDiscountPrice()) ? skuVO.getSalePrice() : skuVO.getDiscountPrice());
        }
        //sku拼团信息
        buildSkuPopPtActivity(productOrderDTO, skuPopVO, skuVO);
        return skuPopVO;
    }

    private void buildProdctFullTrialFreeTaste(ProductVO productBaseVO, ProductOrderDTO productOrderDTO) {
        if(null == productOrderDTO.getFreeTasteId() && null == productOrderDTO.getFullTrialId()){
            return;
        }
        if(null != productOrderDTO.getFreeTasteId() && null != productOrderDTO.getFullTrialId()){
            return;
        }
        Long activityId = null != productOrderDTO.getFreeTasteId() ? productOrderDTO.getFreeTasteId() : productOrderDTO.getFullTrialId();
        FreeTrialSubActivityProduct freeTrialSubActivityProduct = freeTrialSubActivityProductDao.findActivityProductBySkuId(activityId, productOrderDTO.getSkuId());
        if(null == freeTrialSubActivityProduct){
            return;
        }
        SkuVO skuVO = productBaseVO.getSku(productOrderDTO.getSkuId());
        skuVO.setFreeTrialSubActivityId(freeTrialSubActivityProduct.getFreeTrialSubActivityId());
        skuVO.setFreeTrialSubActivityShareDiscount(freeTrialSubActivityProduct.getShareDiscount());
        if(BasicFlagEnum.NO.getKey().equals(productOrderDTO.getCycleFlag())){
            skuVO.setSalePrice(Objects.nonNull(freeTrialSubActivityProduct.getPrice()) ? freeTrialSubActivityProduct.getPrice() : skuVO.getSalePrice());
        }
    }

    @Override
    public PageVO<ProductVO> searchCouponProduct(SearchCouponDTO searchCouponDTO) {
        //用户登陆信息
        CustomerUserVO user = null;
        if(UserUtil.isLogged()){
            user = UserUtil.getDetails();
        }
        if(Objects.isNull(searchCouponDTO.getRuleId())){
            throw new BusinessException("请重新选择优惠券！");
        }
        CouponRule rule = couponRuleManager.findById(searchCouponDTO.getRuleId());
        String key = CouponUtil.COUPON_PRODUCT + rule.getId();
        List<Long> productIds = redisOperation.get(key);
        if(CollectionUtils.isEmpty(productIds)){
            productIds = couponRuleManager.couponRangeProductIds(rule);
            redisOperation.setex(key, productIds ,10L, TimeUnit.MINUTES);
        }
        if(CollectionUtils.isEmpty(productIds)){
            return PageUtils.emptyPage(searchCouponDTO);
        }
        // 排除限时折扣 、满减
        List<Long> noShareCouponDiscountProductIds = discountActivityRangeDao.selectInProgressNoShareCouponProductIds();
        List<Long> noShareCouponFullReduceProductIds = fullReduceProductRangeDao.selectInProgressNoShareCouponProductIds();
        List<Long> noShareCouponPtProductIds = ptGoodsManager.selectInProgressNoShareCouponProductIds();
        if (CollectionUtils.isNotEmpty(noShareCouponDiscountProductIds)) {
            log.info("noShareCouponDiscountProductIds: {}", noShareCouponDiscountProductIds);
            productIds.removeAll(noShareCouponDiscountProductIds);
        }
        if (CollectionUtils.isNotEmpty(noShareCouponFullReduceProductIds)) {
            log.info("noShareCouponFullReduceProductIds: {}", noShareCouponFullReduceProductIds);
            productIds.removeAll(noShareCouponFullReduceProductIds);
        }
        if (CollectionUtils.isNotEmpty(noShareCouponPtProductIds)) {
            log.info("noShareCouponPtProductIds: {}", noShareCouponPtProductIds);
            productIds.removeAll(noShareCouponPtProductIds);
        }

        if(CollectionUtils.isEmpty(productIds)){
            return PageUtils.emptyPage(searchCouponDTO);
        }

        Integer cycleFlag = null;
        //优惠券购买类型筛选
        if(Objects.nonNull(rule.getBuyType()) && CouponBuyTypeEnum.CYCLE.getCode().equals(rule.getBuyType())){
            cycleFlag = ProductCycleEnum.CYCLE.getCode();
        }

        List<ProductHotDTO> productHotDTOList = new ArrayList<>();
        List<ProductVO> productVoList = new ArrayList<>();
        List<Long> productList = new ArrayList<>();
        Pagination pagination = new Pagination();
        if(SearchCouponSortTypeEnum.PRICE.getCode().equals(searchCouponDTO.getSortType())){
            String productListkey = ProductUtil.COUPON_PRODUCT_GAP_KEY + rule.getId() + ":productType:" + searchCouponDTO.getProductType();
            productList = redisOperation.get(productListkey);
            if(CollectionUtils.isEmpty(productList)){
                BigDecimal couponLimit = rule.getAmountFull();
                List<ProductBaseDTO> baseDTOList = productManager.detailCompleteList(productIds, searchCouponDTO.getProductType(), cycleFlag, ProductShowEnum.ENABLE.getCode());
                List<ProductCouponGapDTO> productCouponGapDTOList = new ArrayList<>();
                for (ProductBaseDTO product: baseDTOList) {
                    ProductVO productVO = BeanUtils.deepCopy(product, ProductVO.class);
                    buildProdctDiscountActivity(productVO, user, false, null);
                    SkuVO skuVO = productVO.getSkus().get(0);
                    BigDecimal salePrice = BigDecimal.ZERO;
                    if(ProductCycleEnum.CYCLE.getCode().equals(cycleFlag)){
                        salePrice = getCycleMaxSalePriceSum(skuVO);
                    }else{
                        salePrice = skuVO.getSalePrice();
                        if(DiscountActivityShareTypeEnum.COUPON.getCode().equals(skuVO.getSharingDiscount())){
                            salePrice = Objects.nonNull(skuVO.getDiscountPrice()) ? skuVO.getDiscountPrice() : skuVO.getSalePrice();
                        }
                    }
                    ProductCouponGapDTO productCouponGapDTO = new ProductCouponGapDTO();
                    productCouponGapDTO.setSpuId(product.getId());
                    productCouponGapDTO.setGap(couponLimit.subtract(salePrice));
                    productCouponGapDTOList.add(productCouponGapDTO);
                }
                //排序
                productCouponGapSort(productCouponGapDTOList);
                productList = productCouponGapDTOList.stream().map(ProductCouponGapDTO::getSpuId).collect(Collectors.toList());
                if(CollectionUtils.isEmpty(productList)){
                    return PageUtils.emptyPage(searchCouponDTO);
                }
                redisOperation.setnx(productListkey, productList, 10, TimeUnit.MINUTES);
            }

            //分页
            Integer pageNum = searchCouponDTO.getPageNum();
            Integer pageSize = searchCouponDTO.getPageSize();
            Integer total = productList.size();
            Integer pages = (int)Math.ceil(total / (pageSize * 1.0));
            productList = CollectionUtil.getPage(productList, searchCouponDTO.getPageNum(), searchCouponDTO.getPageSize());
            pagination = new Pagination(pageNum, pageSize, total, pages);
            if(CollectionUtils.isEmpty(productList)){
                return PageUtils.emptyPage(searchCouponDTO);
            }
        }else{
            PageHelper.startPage(searchCouponDTO.getPageNum(), searchCouponDTO.getPageSize());
            productHotDTOList = productManager.hotProductList(productIds, ProductShowEnum.ENABLE.getCode(), cycleFlag);

            productList = productHotDTOList.stream().map(ProductHotDTO::getProductId).collect(Collectors.toList());
            if(CollectionUtils.isEmpty(productList)){
                return PageUtils.emptyPage(searchCouponDTO);
            }
            pagination = PageUtils.extract(productHotDTOList);
        }

        List<ProductBaseDTO> baseDTOList = productManager.detailCompleteList(productList, false);
        productVoList = BeanUtils.deepListCopy(baseDTOList, ProductVO.class);

        CouponRuleListDTO ruleDto = new CouponRuleListDTO();
        ruleDto.setChannel(CouponChannelTypeEnum.WECHAT_MINI.getCode());
        ruleDto.setIsPublic(CouponPublicTypeEnum.PUBLIC.getCode());
        ruleDto.setStatus(CouponStatusTypeEnum.ONLINE.getCode());
        ruleDto.setEndTimeStart(new Date());
        ruleDto.setCouponType(CouponTypeEnum.PRODUCT_COUPON.getCode());

        //查询可领+已领的优惠券
        List<CouponRule> couponRuleList = couponRuleManager.queryEnableReceiveAndReceivedCoupon(ruleDto, user);
        List<Long> ruleIds = StreamUtils.convert(couponRuleList, CouponRule::getId);
        List<CouponRange> couponRangeList = couponRangeDao.queryByRuleIds(ruleIds);
        Map<Long,List<CouponRange>> couponRangeMap = StreamUtils.group(couponRangeList, CouponRange::getRuleId);

        List<PointAmount> pointAmountList = pointAmountManager.getInProgressList();
        List<PointAmountItem> pointAmountItemList = pointAmountItemManager.findByPointAmountIds(StreamUtils.convert(pointAmountList, PointAmount::getId));
        Map<Long, List<PointAmountItem>> pointAmountItemMap = StreamUtils.group(pointAmountItemList, PointAmountItem::getPointAmountId);
        for (ProductVO productVO : productVoList) {
            //促销活动
            buildProdctDiscountActivity(productVO, user, false, null);
            //满减满送
            buildProductFullReduce(productVO, user, false,null, 1, null, productVO.getCycleFlag());
            //优惠券
            buildProdctAllCouponList(productVO, couponRuleList, couponRangeMap);
            //装载积分抵现活动
            buildProductPointAmount(productVO, user, pointAmountList, pointAmountItemMap);
            //装载商品标签
            PointCommonUtil.buildProductLabel(productVO, 1, null);
        }

        return PageUtils.toPageVO(pagination, productVoList);
    }

    private void productCouponGapSort(List<ProductCouponGapDTO> productCouponGapDTOList) {
        Collections.sort(productCouponGapDTOList, new Comparator<ProductCouponGapDTO>() {
            /**
             * 排序规则如下：
             *
             * 如果一个数为0，则排在最前面。
             * 如果两个数符号相同时：
             * 如果两个数都是负数，则按绝对值大小正序排列。
             * 如果两个数都是正数，则按绝对值大小正序排列。
             * 如果两个数符号不同，则负数排在正数前面。
             * @param o1
             * @param o2
             * @return
             */
            @Override
            public int compare(ProductCouponGapDTO o1, ProductCouponGapDTO o2) {
                //根据A.[优惠券门槛-售价]值，负数在前，正数在后排列 B.负数中，按照|优惠券门槛-售价| 绝对值正序排列 B.正数中，按照|优惠券门槛-售价| 绝对值正序排列
                if (o1.getGap().compareTo(BigDecimal.ZERO) == 0 && o2.getGap().compareTo(BigDecimal.ZERO) == 0) {
                    return 0;
                } else if(o1.getGap().compareTo(BigDecimal.ZERO) == 0){
                    return -1;
                }else if(o2.getGap().compareTo(BigDecimal.ZERO) == 0){
                    return 1;
                }else if (o1.getGap().signum() == -1 && o2.getGap().signum() == -1) {
                    if (o1.getGap().abs().compareTo(o2.getGap().abs()) == 0) {
                        return o1.getGap().compareTo(o2.getGap());
                    } else {
                        return o1.getGap().abs().compareTo(o2.getGap().abs());
                    }
                } else if (o1.getGap().signum() == 1 && o2.getGap().signum() == 1) {
                    if (o1.getGap().abs().compareTo(o2.getGap().abs()) == 0) {
                        return o1.getGap().compareTo(o2.getGap());
                    } else {
                        return o1.getGap().abs().compareTo(o2.getGap().abs());
                    }
                } else {
                    return Integer.compare(o1.getGap().signum(), o2.getGap().signum());
                }
            }
        });
    }

    /**
     * Description: 库存处理
     *
     * @param productVO
     */
    private void stockProcess(ProductVO productVO) {
        Map<Long, Long> stockMap = StreamUtils.toMap(stockManager.findSaleStockByProductId(productVO.getId()),
                SkuSaleStockVO::getSkuId, SkuSaleStockVO::getStockNum);
        productVO.getSkus().forEach(sku -> sku.setStock(stockMap.get(sku.getId())));
    }

    private static BigDecimal getCycleMaxSalePriceSum(SkuVO skuVO){
        List<SkuAttrDTO> skuAttrList = skuVO.getSkuAttrs().stream().filter(skuAttr -> "times".equals(skuAttr.getAttrName())).collect(Collectors.toList());
        for (SkuAttrDTO skuAttrDTO: skuAttrList) {
            skuAttrDTO.setAttrValueInt(Integer.valueOf(skuAttrDTO.getAttrValue()));
        }
        BigDecimal salePriceSum = null;
        //取期数最多的周期购
        skuAttrList = skuAttrList.stream().sorted(Comparator.comparing(SkuAttrDTO::getAttrValueInt).reversed()).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(skuAttrList)){
            salePriceSum = BigDecimal.ZERO;
        }else{
            salePriceSum = new BigDecimal(StreamUtils.getFirst(skuAttrList).getAttrSpecValue()).multiply(new BigDecimal(StreamUtils.getFirst(skuAttrList).getAttrValue())).multiply(new BigDecimal(String.valueOf(1)));
        }
        return salePriceSum;
    }


    /**
     * Description: 拼装SKU映射MAP
     *
     * @param skus
     * @return
     */
    private Map<String, Long> skuMapProcess(List<SkuVO> skus) {
        Map<String, Long> skuMap = new HashMap<String, Long>();
        skus.forEach(sku -> {
            if (sku.getStock() != null && sku.getStock() > 0) {
                StringBuilder keySb = new StringBuilder();
                sku.getSkuSpecValues().forEach(skuSpecValue -> keySb.append(skuSpecValue.getSpecValueId()).append(","));
                keySb.deleteCharAt(keySb.length() - 1);
                skuMap.put(keySb.toString(), sku.getId());
            }
        });

        return skuMap;
    }

    private List<CouponVO> buildProdctCouponByOrderList(ProductVO productVO, List<CouponRule> couponRuleList, ProductOrderDTO productOrderDTO) {
        List<CouponVO> couponVOList = new ArrayList<>();
        SkuVO skuVO = productVO.getSku(productOrderDTO.getSkuId());
        if (null != skuVO) {
            //判断当前skuVO是否与优惠券互斥
            //判断限时折扣互斥优惠券
            //判断满减满送互斥优惠券
            if (!isShareCoupon(skuVO)) {
                return couponVOList;
            }
            SkuPriceSumDTO skuPriceSumDTO = this.calculatePrice(skuVO, productOrderDTO.getCycleFlag(), productOrderDTO.getCount(), productOrderDTO.getTimes());
            BigDecimal salePriceSum = null == skuPriceSumDTO.getCycleSalePriceSum() ? skuPriceSumDTO.getSalePriceSum(): skuPriceSumDTO.getCycleSalePriceSum();
            for (CouponRule rule : couponRuleList) {
                if(!CouponTypeEnum.PRODUCT_COUPON.getCode().equals(rule.getCouponType())){
                    continue;
                }
                //校验购买类型
                if(Objects.nonNull(rule.getBuyType())){
                    if(CouponBuyTypeEnum.NO_LIMIT.getCode().equals(rule.getBuyType())){
                        //购买类型不限
                    }else if(CouponBuyTypeEnum.SINGLE.getCode().equals(rule.getBuyType())){
                        if(BasicFlagEnum.YES.getKey().equals(productOrderDTO.getCycleFlag())){
                            continue;
                        }
                    }else if(CouponBuyTypeEnum.CYCLE.getCode().equals(rule.getBuyType())){
                        if(!BasicFlagEnum.YES.getKey().equals(productOrderDTO.getCycleFlag())){
                            continue;
                        }
                    }
                }
                boolean checkRangeFlag = couponRuleManager.checkCouponAllRange(rule.getId(), rule.getCouponRange(), BeanUtils.deepCopy(productVO, ProductBaseDTO.class));
                if (checkRangeFlag) {
                    //计算券后价
                    BigDecimal couponPrice = CouponUtil.computeCouponPrice(salePriceSum, rule);
                    if(null == couponPrice){
                        continue;
                    }
                    CouponVO couponVO = new CouponVO();
                    BeanUtils.copy(rule, couponVO);
                    couponVO.setRuleId(rule.getId());
                    couponVO.setCouponPrice(couponPrice);
                    couponVOList.add(couponVO);
                }
            }
        }

        if (CollectionUtils.isNotEmpty(couponVOList)) {
            return couponVOList.stream().sorted(Comparator.comparing(CouponVO::getCouponPrice).thenComparing(CouponVO::getEndTime)).collect(Collectors.toList());
        }
        return couponVOList;
    }

    private ProductVO.PtActivityInfo assemblePtActivityInfo(PtActivity ptActivity, PtGoods ptGoods) {
        ProductVO.PtActivityInfo ptActivityInfo = new ProductVO.PtActivityInfo();
        ptActivityInfo.setId(ptActivity.getId());
        ptActivityInfo.setStartTime(ptActivity.getStartTime());
        ptActivityInfo.setStartTimeUnix(ptActivity.getStartTime().getTime());
        ptActivityInfo.setEndTime(ptActivity.getEndTime());
        ptActivityInfo.setEndTimeUnix(ptActivity.getEndTime().getTime());
        ptActivityInfo.setRequiredNum(ptActivity.getRequiredNum());
        ptActivityInfo.setStock(ptActivity.getStock());
        ptActivityInfo.setSkuId(ptGoods.getSkuId());
        ptActivityInfo.setPtPrice(ptGoods.getPtPrice());
        Date now = new Date();
        ptActivityInfo.setSystemDate(now);
        ptActivityInfo.setSystemDateUnix(now.getTime());
        ptActivityInfo.setStatus(ptActivity.getStatus());
        ptActivityInfo.setPeopleLimit(ptActivity.getPeopleLimit());
        ptActivityInfo.setLimitNum(ptActivity.getLimitNum());
        ptActivityInfo.setUsed(ptActivity.getUsed());
        ptActivityInfo.setRemaining(ptActivity.getStock() - ptActivity.getUsed());
        return ptActivityInfo;
    }

    private void buildSkuPopPtActivity(ProductOrderDTO productOrderDTO, SkuPopVO skuPopVO, SkuVO skuVO) {
        //拼团与满额试用&0元尝鲜默认互斥
        if(Objects.nonNull(skuVO.getFreeTrialSubActivityId())){
            return;
        }
        Optional<PtActivity> optionalPtActivity = Optional.ofNullable(ptActivityManager.findBySkuId(productOrderDTO.getSkuId()));

        optionalPtActivity.ifPresent(activity -> {
            Optional<PtGoods> optionalPtGoods = Optional.ofNullable(ptGoodsManager.getByActivityId(activity.getId()));

            SkuPopVO.PtActivityInfo ptActivityInfo = new SkuPopVO.PtActivityInfo();
            ptActivityInfo.setId(activity.getId());

            optionalPtGoods.ifPresent(goods -> {
                ptActivityInfo.setPtPrice(goods.getPtPrice());
                BigDecimal amountReduce = skuVO.getSalePrice().subtract(goods.getPtPrice()).multiply(BigDecimal.valueOf(productOrderDTO.getCount()));
                ptActivityInfo.setAmountReduce(amountReduce);
                ptActivityInfo.setTotalPtPrice(goods.getPtPrice().multiply(BigDecimal.valueOf(productOrderDTO.getCount())));
                ptActivityInfo.setTotalDiscount(skuVO.getListPrice().multiply(BigDecimal.valueOf(productOrderDTO.getCount())).subtract(ptActivityInfo.getTotalPtPrice()));
            });

            skuPopVO.setPtActivity(ptActivityInfo);
        });
    }
}
