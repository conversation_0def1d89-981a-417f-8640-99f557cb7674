package com.hengtiansoft.mall.order.service.impl;

import cn.hutool.core.lang.Assert;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.hengtiansoft.address.entity.vo.AddressDetailVO;
import com.hengtiansoft.common.entity.exception.BusinessException;
import com.hengtiansoft.common.entity.vo.PageVO;
import com.hengtiansoft.common.enumeration.*;
import com.hengtiansoft.common.redis.RedisOperation;
import com.hengtiansoft.common.util.*;
import com.hengtiansoft.item.dao.DiscountActivityRangeDao;
import com.hengtiansoft.item.entity.dto.*;
import com.hengtiansoft.item.entity.po.DiscountActivityRange;
import com.hengtiansoft.item.entity.po.Product;
import com.hengtiansoft.item.enumeration.*;
import com.hengtiansoft.item.interfaces.DeliveryAddressTemplateManager;
import com.hengtiansoft.item.interfaces.ProductManager;
import com.hengtiansoft.item.interfaces.SkuManager;
import com.hengtiansoft.item.interfaces.StockManager;
import com.hengtiansoft.mall.address.service.ReceiveAddressService;
import com.hengtiansoft.mall.fullReduce.dto.FullProductOrderDTO;
import com.hengtiansoft.mall.item.vo.FullSummaryVO;
import com.hengtiansoft.mall.mq.service.MilkProducerService;
import com.hengtiansoft.mall.order.service.OrderService;
import com.hengtiansoft.mall.shop.cart.service.ShopCartService;
import com.hengtiansoft.order.adapter.OrderSyncAdapter;
import com.hengtiansoft.order.dao.*;
import com.hengtiansoft.order.entity.dto.*;
import com.hengtiansoft.order.entity.po.*;
import com.hengtiansoft.order.entity.vo.OrderInfoGiftVO;
import com.hengtiansoft.order.entity.vo.OrderInfoV2VO;
import com.hengtiansoft.order.entity.vo.OrderInfoVO;
import com.hengtiansoft.order.entity.vo.ShopCartCouponVO;
import com.hengtiansoft.order.enums.*;
import com.hengtiansoft.order.manager.*;
import com.hengtiansoft.order.service.OrderRemoteService;
import com.hengtiansoft.order.service.PeopleLimitVerifyManger;
import com.hengtiansoft.order.util.CouponUtil;
import com.hengtiansoft.order.util.OrderInfoUtil;
import com.hengtiansoft.order.util.ShopCartUtil;
import com.hengtiansoft.privilege.dao.FreeTrialSubActivityProductDao;
import com.hengtiansoft.privilege.dao.FreeTrialSubActivityRecordDao;
import com.hengtiansoft.privilege.dao.PtActivityDao;
import com.hengtiansoft.privilege.entity.dto.FullReduceMallDTO;
import com.hengtiansoft.privilege.entity.po.*;
import com.hengtiansoft.privilege.entity.vo.FullCouponRuleVO;
import com.hengtiansoft.privilege.entity.vo.FullSkuVO;
import com.hengtiansoft.privilege.enums.FullRuleRangeTypeEnum;
import com.hengtiansoft.privilege.enums.LuckyRecordPrizeStatusEnum;
import com.hengtiansoft.privilege.manager.*;
import com.hengtiansoft.privilege.util.PtActivityUtil;
import com.hengtiansoft.security.util.UserUtil;
import com.hengtiansoft.thirdpart.entity.vo.nascent.NascentUserVO;
import com.hengtiansoft.thirdpart.enumeration.NascentPointTypeEnum;
import com.hengtiansoft.thirdpart.interfaces.NascentCustomerManager;
import com.hengtiansoft.thirdpart.interfaces.WeChatMiniSmsManager;
import com.hengtiansoft.user.dao.CommunityPostDao;
import com.hengtiansoft.user.dao.CustomerUserDao;
import com.hengtiansoft.user.dao.WorkWxSalesDao;
import com.hengtiansoft.user.entity.po.CommunityPost;
import com.hengtiansoft.user.entity.po.CommunityUserOrderStats;
import com.hengtiansoft.user.entity.po.CustomerUser;
import com.hengtiansoft.user.entity.po.WorkWxSales;
import com.hengtiansoft.user.entity.vo.CustomerUserVO;
import com.hengtiansoft.user.enums.UserEventTypeEnum;
import com.hengtiansoft.user.manager.CommunityUserOrderStatsManager;
import com.hengtiansoft.user.manager.CustomerUserManager;
import com.nascent.ecrp.opensdk.domain.point.CustomerPointInfo;
import com.nascent.ecrp.opensdk.response.point.PointReduceResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
@Slf4j
public class OrderServiceImpl implements OrderService {
    @Resource
    private OrderManager orderManager;
    @Resource
    private OrderV2Manager orderV2Manager;
    @Resource
    private RedisOperation redisOperation;
    @Resource
    private OrderSkuManager orderSkuManager;
    @Resource
    private OrderAddressManager orderAddressManager;
    @Resource
    private ReceiveAddressService receiveAddressService;
    @Resource
    private CouponRuleManager couponRuleManager;
    @Autowired
    private ProductManager productManager;
    @Resource
    private DeliveryAddressTemplateManager deliveryAddressTemplateManager;
    @Resource
    private StockManager stockManager;
    @Resource
    private CustomerUserDao customerUserDao;
    @Resource
    private PrivilegeActManager privilegeActManager;
    @Resource
    private SkuManager skuManager;
    @Resource
    private OrderGiftManager orderGiftManager;
    @Resource
    private OrderCycleManager orderCycleManager;
    @Resource
    private MilkProducerService milkProducerService;
    @Resource
    private OrderSyncAdapter orderSyncAdapter;
    @Resource
    private ShopCartDao shopCartDao;
    @Resource
    private CouponRangeDao couponRangeDao;
    @Resource
    private CustomerUserManager customerUserManager;
    @Resource
    private OrderRemoteService orderRemoteService;
    @Resource
    private ShopCartCycleDao shopCartCycleDao;
    @Resource
    private ShopCartService shopCartService;
    @Resource
    private FullReduceRecordManager fullReduceRecordManager;
    @Resource
    private CouponInfoManager couponInfoManager;
    @Resource
    private WeChatMiniSmsManager weChatMiniSmsManager;
    @Resource
    private NascentCustomerManager nascentCustomerManager;
    @Resource
    private PointAmountManager pointAmountManager;
    @Resource
    private Executor pointTask;
    @Resource
    private OrderSkuSenceManager orderSkuSenceManager;
    @Resource
    private PtActivityDao ptActivityDao;
    @Resource
    private PtActivityManager ptActivityManager;
    @Resource
    private PtGoodsManager ptGoodsManager;
    @Resource
    private PtOrderDao ptOrderDao;
    @Resource
    private PtSubOrderDao ptSubOrderDao;
    @Resource
    private PtOrderManager ptOrderManager;
    @Resource
    private PeopleLimitVerifyManger peopleLimitVerifyManger;
    @Resource
    private MilkDispatchRuleManager milkDispatchRuleManager;
    @Resource
    private DiscountActivityRangeDao discountActivityRangeDao;
    @Resource
    private FreeTrialSubActivityProductDao freeTrialSubActivityProductDao;
    @Resource
    private CouponRuleDao couponRuleDao;
    @Resource
    private FreeTrialSubActivityRecordDao freeTrialSubActivityRecordDao;
    @Resource
    private LuckyRecordManager luckyRecordManager;
    @Resource
    private FullReduceRuleRangeManager fullReduceRuleRangeManager;
    @Resource
    private WorkWxSalesDao workWxSalesDao;
    @Resource
    private CommunityUserOrderStatsManager communityUserOrderStatsManager;
    @Resource
    private CommunityPostDao communityPostDao;


    @Override
    public String createOrderNo(Long productId, Long skuId) {
        String flag = checkProduct(productId, skuId);
        if ("error".equals(flag)) {
            return flag;
        }
        String currentTime = DateUtil.dateToString(new Date(), "yyyyMMddHHmmssSSS");
        String orderNo = "LPK" + currentTime + (int) (Math.random() * 9000 + 1000);
        return orderNo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createOrderV3(OrderCreateV2DTO orderCreateDTO) {
        CustomerUserVO user = UserUtil.getDetails();
        orderCreateDTO.setUserId(user.getId());
        orderCreateDTO.setBelongMark("");
        log.info("提交订单 . req --> orderCreateDTO:{}, user:{}", JSON.toJSONString(orderCreateDTO), JSON.toJSONString(user));

        CustomerUser customer = customerUserManager.findById(user.getId());
        orderCreateDTO.setOrderNickName(customer.getOrderNickName());
        orderCreateDTO.setUnionId(customer.getUnionId());
        //用户黑名单校验
        verifyBlacklist(customer);

        //校验订单号
        if(StringUtils.isBlank(orderCreateDTO.getOrderNo())){
            throw new BusinessException("订单号不能为空");
        }

        Long num = redisOperation.incr("Order::setOrderNo_" + orderCreateDTO.getOrderNo(), 1);
        redisOperation.expire("Order::setOrderNo_" + orderCreateDTO.getOrderNo(), 15000L);
        if (num >= 2) {
            throw new BusinessException("请勿重复提交");
        }

        OrderInfo orderInfo = orderManager.selectByOrderNo(orderCreateDTO.getOrderNo());
        if (Objects.nonNull(orderInfo)) {
            return;
        }

        if (Objects.isNull(orderCreateDTO.getAddressId())) {
            throw new BusinessException("请选择收货地址!");
        }
        AddressDetailVO userReceiveAddress = receiveAddressService.receiveDetail(orderCreateDTO.getAddressId());
        if (Objects.isNull(userReceiveAddress)) {
            throw new BusinessException("请选择收货地址!");
        }

        List<OrderSkuCreateV2DTO> orderSkuCreateV2DTOList = orderCreateDTO.getOrderSkuCreateV2DTOList();
        if(CollectionUtils.isEmpty(orderSkuCreateV2DTOList)){
            throw new BusinessException("请选择商品");
        }
        OrderSkuCreateV2DTO firstOrderSku = orderSkuCreateV2DTOList.get(0);
        if(Objects.nonNull(firstOrderSku.getPtOrderId())){
            Long ptOrdernum = redisOperation.incr("Order::ptOrderId_" + firstOrderSku.getPtOrderId(), 1);
            redisOperation.expire("Order::ptOrderId_" + firstOrderSku.getPtOrderId(), 5000L);
            if (ptOrdernum >= 2) {
                throw new BusinessException("网络繁忙，请稍后再试");
            }
        }

        // 赠品订单处理
        Map<Long, List<OrderSkuCreateV2DTO>> skuV2Map = StreamUtils.filterGroup(orderSkuCreateV2DTOList, x -> x.getFullReduceId() != null,
                OrderSkuCreateV2DTO::getFullReduceId);
        List<FullReduceMallDTO> fullReduceMallList = orderCreateDTO.getFullReduceMallList() == null ? Lists.newArrayList() : orderCreateDTO.getFullReduceMallList();
        for (FullReduceMallDTO fullReduceMallDTO : fullReduceMallList) {

            if(!skuV2Map.keySet().contains(fullReduceMallDTO.getId())){
                continue;
            }
            //校验增品和优惠券
            checkFullReduceProduct(fullReduceMallDTO);

            List<FullSkuVO> gifts = fullReduceMallDTO.getGifts();
            if(CollectionUtils.isEmpty(gifts)){
                continue;
            }
            FullSkuVO fullSkuVO = gifts.get(0);
            OrderSkuCreateV2DTO orderSkuCreateV2DTO = new OrderSkuCreateV2DTO();
            orderSkuCreateV2DTO.setShopCartId(-1L);
            orderSkuCreateV2DTO.setTotalAmount(BigDecimal.ZERO);
            orderSkuCreateV2DTO.setRealAmount(BigDecimal.ZERO);
            orderSkuCreateV2DTO.setProductAmount(BigDecimal.ZERO);
            orderSkuCreateV2DTO.setProductId(fullSkuVO.getProductId());
            orderSkuCreateV2DTO.setSkuId(fullSkuVO.getId());
            orderSkuCreateV2DTO.setCount(fullSkuVO.getCount());
            orderSkuCreateV2DTO.setOriginPrice(BigDecimal.ZERO);
            orderSkuCreateV2DTO.setProductType(ProductTypeEnum.DERIVATIVE.getCode());
            orderSkuCreateV2DTO.setCycleFlag(0);
            orderSkuCreateV2DTO.setFullReduceId(fullReduceMallDTO.getId());
            orderSkuCreateV2DTO.setIsGift(FlagEnum.YES.getCode());
            orderSkuCreateV2DTOList.add(orderSkuCreateV2DTO);
        }

        //优惠券
        int couponUpdateCount = 0;
        CouponInfo couponInfo = null;
        CouponRule couponRule = null;
        // todo 暂时去掉优惠券校验: 这里获得的是ruleId
        if (Objects.nonNull(orderCreateDTO.getCouponId())) {
            // couponInfo = couponRuleManager.findUserCouponById(orderCreateDTO.getUserId(), orderCreateDTO.getCouponId());
            couponInfo = couponRuleManager.findUserCouponByRuleId(orderCreateDTO.getUserId(), orderCreateDTO.getCouponId());
            if (Objects.isNull(couponInfo)) {
                throw new BusinessException("优惠券不存在!");
            }
            couponRule = couponRuleManager.findById(couponInfo.getRuleId());
            couponUpdateCount = couponRuleManager.updateCouponUsedById(orderCreateDTO.getUserId(), couponInfo.getId(), orderCreateDTO.getOrderNo());
        }

        List<Long> shopCartIds = StreamUtils.convertFilter(orderSkuCreateV2DTOList, OrderSkuCreateV2DTO::getShopCartId, x -> Objects.nonNull(x) && x != -1L);
        List<FullProductOrderDTO> skuList = Lists.newArrayList();
        if(org.apache.commons.collections4.CollectionUtils.isEmpty(shopCartIds)){
            // 直接下单
            orderCreateDTO.setIsShopCart(FlagEnum.NO.getCode());
            for (OrderSkuCreateV2DTO orderSkuCreateCouponDTO : orderSkuCreateV2DTOList) {
                // 赠品订单不参与计算
                if(Objects.equals(FlagEnum.YES.getCode(), orderSkuCreateCouponDTO.getIsGift())){
                    continue;
                }
                FullProductOrderDTO fullProductOrderDTO = new FullProductOrderDTO();
                fullProductOrderDTO.setProductId(orderSkuCreateCouponDTO.getProductId());
                fullProductOrderDTO.setSkuId(orderSkuCreateCouponDTO.getSkuId());
                fullProductOrderDTO.setCount(orderSkuCreateCouponDTO.getCount());
                fullProductOrderDTO.setCycleFlag(orderSkuCreateCouponDTO.getCycleFlag());
                fullProductOrderDTO.setPtActivityId(orderSkuCreateCouponDTO.getPtActivityId());
                fullProductOrderDTO.setFullTrialId(orderSkuCreateCouponDTO.getFullTrialId());
                fullProductOrderDTO.setFreeTasteId(orderSkuCreateCouponDTO.getFreeTasteId());
                OrderCycleCreateV2DTO orderCycleCreateDTO = orderSkuCreateCouponDTO.getOrderCycleCreateDTO();
                if(orderCycleCreateDTO != null){
                    fullProductOrderDTO.setTimes(orderCycleCreateDTO.getTimes());
                    fullProductOrderDTO.setMilkAmount(orderCycleCreateDTO.getMilkAmount());
                }
                skuList.add(fullProductOrderDTO);
            }
            if(StringUtils.isNotBlank(orderCreateDTO.getWxsId())){
                WorkWxSales wxSales = workWxSalesDao.getOne(Long.valueOf(orderCreateDTO.getWxsId()));
                if(null != wxSales){
                    orderCreateDTO.setBelongMark(wxSales.getWorkWxUserId());
                }
            }
        }else{
            // 购物车下单
            orderCreateDTO.setIsShopCart(FlagEnum.YES.getCode());
            List<ShopCart> shopCarts = shopCartDao.findByIds(shopCartIds, user.getId());
            for (ShopCart shopCart : shopCarts) {
                FullProductOrderDTO fullProductOrderDTO = BeanUtils.deepCopy(shopCart, FullProductOrderDTO.class);
                if(Objects.nonNull(shopCart.getCycleId()) && shopCart.getCycleId() > 0){
                    ShopCartCycle shopCartCycle = shopCartCycleDao.getOne(shopCart.getCycleId());
                    fullProductOrderDTO.setTimes(shopCartCycle.getTimes());
                    fullProductOrderDTO.setMilkAmount(shopCartCycle.getMilkAmount());
                    fullProductOrderDTO.setCycleFlag(FlagEnum.YES.getCode());
                }
                skuList.add(fullProductOrderDTO);
            }
            //订单归属
            ShopCart shopCart = StreamUtils.sortFindFirst(shopCarts, x -> StringUtils.isNotBlank(x.getBelongMark()) && Objects.nonNull(x.getBelongTime()),
                    Comparator.comparing(ShopCart::getBelongTime).reversed());
            if(null != shopCart){
                orderCreateDTO.setBelongMark(shopCart.getBelongMark());
            }
            shopCartDao.delete(shopCartIds);
        }
        List<Long> spuIds = StreamUtils.toList(orderSkuCreateV2DTOList, OrderSkuCreateV2DTO::getProductId);
        List<ProductBaseDTO> productBaseDTOS = productManager.detailCompleteList(spuIds, true);
        // 过滤下架的商品
        for (ProductBaseDTO productBaseDTO : productBaseDTOS) {
            if(!ProductSaleStatusEnum.isOnShelves(productBaseDTO)){
                throw new BusinessException(productBaseDTO.getProductName() + "商品已下架");
            }
        }
        List<CouponRule> couponRuleList = couponRule == null ? Lists.newArrayList() : Lists.newArrayList(couponRule);
        List<FullSummaryVO> fullSummaryVOList = shopCartService.getFullSummaryVOS(skuList, couponRuleList, orderCreateDTO.getUsePoint());
        FullSummaryVO fullSummaryVO = fullSummaryVOList.get(0);
        BigDecimalUtils.roundFields(fullSummaryVO);
        Map<Long, FullProductOrderDTO> fullProductOrderDTOMap = StreamUtils.toMap(StreamUtils.filter(skuList, x -> !Objects.equals(x.getCycleFlag(), FlagEnum.YES.getCode())), FullProductOrderDTO::getSkuId);

        Map<Long, ProductBaseDTO> baseDTOMap = StreamUtils.toMap(productBaseDTOS, ProductBaseDTO::getId);
        int j = 0;
        for (OrderSkuCreateV2DTO orderSkuCreateV2DTO : orderSkuCreateV2DTOList) {
            j++;
            orderSkuCreateV2DTO.setDiscountActivityId(null);
            FullProductOrderDTO fullProductOrderDTO = fullProductOrderDTOMap.get(orderSkuCreateV2DTO.getSkuId());
            if(Objects.nonNull(fullProductOrderDTO)){
                orderSkuCreateV2DTO.setDiscountActivityId(fullProductOrderDTO.getDiscountActivityId());
            }
            if(Objects.nonNull(orderSkuCreateV2DTO.getPtActivityId())){
                if(BasicFlagEnum.YES.getKey().equals(orderCreateDTO.getIsShopCart())){
                    throw new BusinessException("购物车下单无法参与拼团!");
                }
                PtActivity ptActivity = ptActivityManager.getById(orderSkuCreateV2DTO.getPtActivityId());
                if (Objects.isNull(ptActivity)) {
                    throw new BusinessException("拼团活动不存在!");
                }
                if(!CommonActivityStatusEnum.IN_PROGRESS.getCode().equals(ptActivity.getStatus())){
                    throw new BusinessException("拼团活动未在进行中!");
                }
                if(Objects.nonNull(ptActivity.getLimitNum())){
                    Integer sum = ptSubOrderDao.sumByCount(ptActivity.getId(), user.getId());
                    sum = sum == null ? 0 : sum;
                    if(sum + orderSkuCreateV2DTO.getCount() > ptActivity.getLimitNum()){
                        throw new BusinessException("已超出限购数量!");
                    }
                }
                CustomerUser customerUser = customerUserDao.findById(user.getId());
                PtGoods ptGoods = ptGoodsManager.getByActivityId(orderSkuCreateV2DTO.getPtActivityId());
                if (Objects.isNull(ptGoods)) {
                    throw new BusinessException("拼团商品不存在!");
                }
                if(!Objects.equals(ptGoods.getSkuId(), orderSkuCreateV2DTO.getSkuId())){
                    throw new BusinessException("拼团参数不匹配!");
                }
                String ptOrderNo = PtActivityUtil.ptNoGenerate();
                if(Objects.isNull(orderSkuCreateV2DTO.getPtOrderId())){
                    // 团长订单
                    Long occupyNum = Long.valueOf(ptActivity.getRequiredNum() - 1);
                    Long realNum = occupyNum + orderSkuCreateV2DTO.getCount();
                    if(!ptActivityDao.decrease(orderSkuCreateV2DTO.getPtActivityId(), realNum)){
                        throw new BusinessException("拼团活动库存不足!");
                    }
                    PtOrder ptOrder = new PtOrder();
                    ptOrder.setPtOrderNo(ptOrderNo);
                    ptOrder.setPtActivityId(ptActivity.getId());
                    ptOrder.setSkuId(orderSkuCreateV2DTO.getSkuId());
                    ptOrder.setProductId(orderSkuCreateV2DTO.getProductId());
                    ptOrder.setRequiredNum(ptActivity.getRequiredNum());
                    ptOrder.setOfferedNum(1);
                    ptOrder.setPtOrderStatus(PtOrderStatusEnum.NO_START.getCode());
                    long endTimeUnix = System.currentTimeMillis() + ptActivity.getValidityPeriod() * 60 * 1000L;
                    ptOrder.setEndTime(endTimeUnix);
                    ptOrder.setCreateTime(new Date());
                    ptOrder.setUpdateTime(new Date());
                    ptOrder.setOperator(Objects.isNull(customerUser.getOrderNickName()) ? Strings.EMPTY : customerUser.getOrderNickName());
                    ptOrder.setDelflag(0);
                    ptOrder.setOccupyNum(occupyNum);
                    ptOrderDao.insert(ptOrder);

                    PtSubOrder ptSubOrderInsert = new PtSubOrder();
                    ptSubOrderInsert.setOrderNo(orderCreateDTO.getOrderNo());
                    ptSubOrderInsert.setPtOrderNo(ptOrderNo);
                    ptSubOrderInsert.setPtActivityId(ptActivity.getId());
                    ptSubOrderInsert.setProductId(orderSkuCreateV2DTO.getProductId());
                    ptSubOrderInsert.setSkuId(orderSkuCreateV2DTO.getSkuId());
                    ptSubOrderInsert.setUserId(user.getId());
                    ptSubOrderInsert.setUserName(Objects.isNull(customerUser.getOrderNickName()) ? Strings.EMPTY : customerUser.getOrderNickName());
                    ptSubOrderInsert.setUserPic(customerUser.getUserPic());
                    ptSubOrderInsert.setHasCaptain(1);
                    ptSubOrderInsert.setPtPrice(ptGoods.getPtPrice());
                    ptSubOrderInsert.setBuyCount(orderSkuCreateV2DTO.getCount());
                    ptSubOrderInsert.setPtSubOrderStatus("unPay");
                    ptSubOrderInsert.setOperator(Objects.isNull(customerUser.getOrderNickName()) ? Strings.EMPTY : customerUser.getOrderNickName());
                    ptSubOrderDao.insert(ptSubOrderInsert);
                }else{
                    PtOrder ptOrder = ptOrderDao.findById(orderSkuCreateV2DTO.getPtOrderId());
                    if(Objects.isNull(ptOrder)){
                        throw new BusinessException("拼团订单不存在!");
                    }
                    if(!PtOrderStatusEnum.WAIT.getCode().equals(ptOrder.getPtOrderStatus())){
                        throw new BusinessException("非拼团中状态不允许拼团!");
                    }
                    if(ptOrder.getOfferedNum() + 1 > ptActivity.getRequiredNum()){
                        throw new BusinessException("拼团人数已满!");
                    }
                    if(CollectionUtils.isNotEmpty(ptSubOrderDao.findByOrderNoAndUser(ptOrder.getPtOrderNo(), user.getId()))){
                        throw new BusinessException("您正在参团中!");
                    }
                    if(!ptOrderManager.addOfferedNum(ptOrder.getId())){
                        throw new BusinessException("参团人数已满!");
                    }
                    if(!ptOrder.getPtActivityId().equals(ptActivity.getId())){
                        throw new BusinessException("拼团参数不匹配!");
                    }
                    //新用户
                    if(CommonPeopleLimitEnum.NEW_USER.getCode().equals(ptActivity.getPeopleLimit())){
                        PeopleVerifyDTO verifyDTO = new PeopleVerifyDTO();
                        verifyDTO.setPeopleLimit(ptActivity.getPeopleLimit());
                        boolean verifyFlag = peopleLimitVerifyManger.peopleLimitVerify(verifyDTO, customerUser);
                        if(!verifyFlag){
                            throw new BusinessException("非新用户无法参与拼团，您可选择发起拼团!");
                        }
                    }
                    if(!ptOrderDao.decrease(ptOrder.getId())){
                        throw new BusinessException("该团人数已满!");
                    }
                    if(orderSkuCreateV2DTO.getCount() - 1 > 0){
                        if(!ptActivityDao.decrease(orderSkuCreateV2DTO.getPtActivityId(), Long.valueOf(orderSkuCreateV2DTO.getCount() - 1))){
                            throw new BusinessException("拼团活动库存不足!");
                        }
                    }

                    PtSubOrder ptSubOrderInsert = new PtSubOrder();
                    ptSubOrderInsert.setOrderNo(orderCreateDTO.getOrderNo());
                    ptSubOrderInsert.setPtOrderNo(ptOrder.getPtOrderNo());
                    ptSubOrderInsert.setPtActivityId(ptActivity.getId());
                    ptSubOrderInsert.setProductId(orderSkuCreateV2DTO.getProductId());
                    ptSubOrderInsert.setSkuId(orderSkuCreateV2DTO.getSkuId());
                    ptSubOrderInsert.setUserId(user.getId());
                    ptSubOrderInsert.setUserName(Objects.isNull(customerUser.getOrderNickName()) ? Strings.EMPTY : customerUser.getOrderNickName());
                    ptSubOrderInsert.setUserPic(customerUser.getUserPic());
                    ptSubOrderInsert.setHasCaptain(0);
                    ptSubOrderInsert.setPtPrice(ptGoods.getPtPrice());
                    ptSubOrderInsert.setBuyCount(orderSkuCreateV2DTO.getCount());
                    ptSubOrderInsert.setPtSubOrderStatus("unPay");
                    ptSubOrderInsert.setOperator(Objects.isNull(customerUser.getOrderNickName()) ? Strings.EMPTY : customerUser.getOrderNickName());
                    ptSubOrderDao.insert(ptSubOrderInsert);
                }
            }else{
                // 起售和限购校验
                if(Objects.equals(FlagEnum.YES.getCode(), orderSkuCreateV2DTO.getIsGift())){
                    continue;
                }
                ProductBaseDTO baseDTO = baseDTOMap.get(orderSkuCreateV2DTO.getProductId());
                if(Objects.nonNull(orderSkuCreateV2DTO.getDiscountActivityId())){
                    // 周期购不校验
                    if(BasicFlagEnum.YES.getKey().equals(orderSkuCreateV2DTO.getCycleFlag())){
                        continue;
                    }
                    // 限时折扣限购校验
                    DiscountActivityRange discountActivityRange = discountActivityRangeDao.findByDiscountActivityIdAndSkuId(orderSkuCreateV2DTO.getDiscountActivityId(), orderSkuCreateV2DTO.getSkuId());
                    if(Objects.isNull(discountActivityRange)){
                        throw new BusinessException("商品不在活动范围内!");
                    }
                    //活动限购
                    if(Objects.nonNull(discountActivityRange.getLimitNum())){
                        Long discountNum = redisOperation.incr("Order::discountActivityId_" + orderSkuCreateV2DTO.getDiscountActivityId() + "_userId:" + user.getId() + "_num:" + j, 1);
                        redisOperation.expire("Order::discountActivityId_" + orderSkuCreateV2DTO.getDiscountActivityId() + "_userId:" + user.getId() + "_num:" + j, 3000L);
                        if (discountNum >= 2) {
                            throw new BusinessException("网络繁忙，请稍后再试");
                        }
                        Integer count = orderManager.countDiscountSkuCnt(user.getId(), orderSkuCreateV2DTO.getDiscountActivityId(), orderSkuCreateV2DTO.getSkuId());
                        count = Objects.isNull(count) ? 0 : count;
                        if(count + orderSkuCreateV2DTO.getCount() > discountActivityRange.getLimitNum()){
                            throw new BusinessException("活动限购:" + discountActivityRange.getLimitNum() + "件!");
                        }
                    }
                    if(Objects.nonNull(discountActivityRange.getMinimumCount())){
                        if(orderSkuCreateV2DTO.getCount() < discountActivityRange.getMinimumCount()){
                            throw new BusinessException("活动起售数量为" + discountActivityRange.getMinimumCount() + " " + baseDTO.getProductName());
                        }
                    }
                }else if(Objects.nonNull(orderSkuCreateV2DTO.getFullTrialId()) || Objects.nonNull(orderSkuCreateV2DTO.getFreeTasteId())){
                    // 满额试用&0元尝鲜校验
                    if(Objects.nonNull(orderSkuCreateV2DTO.getFullTrialId()) && Objects.nonNull(orderSkuCreateV2DTO.getFreeTasteId())){
                        throw new BusinessException("不能同时参与满额试用和0元尝鲜活动!");
                    }
                    Long activityId = null != orderSkuCreateV2DTO.getFullTrialId() ? orderSkuCreateV2DTO.getFullTrialId() : orderSkuCreateV2DTO.getFreeTasteId();
                    FreeTrialSubActivityProduct freeTrialSubActivityProduct = freeTrialSubActivityProductDao.findActivityProductBySkuId(activityId, orderSkuCreateV2DTO.getSkuId());
                    if(null == freeTrialSubActivityProduct){
                        throw new BusinessException("活动不在进行中!");
                    }
                    if(Objects.nonNull(freeTrialSubActivityProduct.getSingleLimitNum())){
                        if(orderSkuCreateV2DTO.getCount() > freeTrialSubActivityProduct.getSingleLimitNum()){
                            throw new BusinessException("活动单次限购为" + freeTrialSubActivityProduct.getSingleLimitNum() + " " + baseDTO.getProductName());
                        }
                    }
                    if(Objects.nonNull(freeTrialSubActivityProduct.getLimitNum())){
                        Integer count = orderManager.countFullTrialFreeTasteCnt(user.getId(), orderSkuCreateV2DTO.getFullTrialId(), orderSkuCreateV2DTO.getFreeTasteId());
                        count = Objects.isNull(count) ? 0 : count;
                        if(count +  orderSkuCreateV2DTO.getCount() > freeTrialSubActivityProduct.getLimitNum()){
                            throw new BusinessException("活动累计限购为" + freeTrialSubActivityProduct.getLimitNum());
                        }
                    }
                    if(null != orderSkuCreateV2DTO.getFullTrialId()){
                        // 满额试用奖品订单
                        List<FreeTrialSubActivityProduct> freeTrialSubActivityProducts = freeTrialSubActivityProductDao.selectByActivityId(orderSkuCreateV2DTO.getFullTrialId());
                        if (CollectionUtils.isEmpty(freeTrialSubActivityProducts)) {
                            throw new BusinessException("满额试用活动信息无商品信息");
                        }
                        Optional<FreeTrialSubActivityProduct> giftFirst = freeTrialSubActivityProducts.stream().filter(e -> e.getType() == 2 && e.getTargetType() == 1).findFirst();
                        if (!giftFirst.isPresent()) {
                            throw new BusinessException("满额试用活动信息无赠品信息");
                        }
                        FreeTrialSubActivityProduct giftProduct = giftFirst.get();
                        Long skuId = giftProduct.getSkuId();
                        Long productId = giftProduct.getTargetId();
                        Product product = productManager.findById(productId);
                        if(Objects.isNull(product)){
                            throw new BusinessException("满额试用赠品不存在");
                        }
                        if(!Objects.equals(product.getSaleStatus(), ProductSaleStatusEnum.ON_SHELVES.getCode())){
                            throw new BusinessException("满额试用赠品已下架");
                        }
                        //更新库存
                        StockCalculateDTO stockCalculateDTO = new StockCalculateDTO();
                        stockCalculateDTO.setSkuId(skuId);
                        stockCalculateDTO.setStockNum(1L);
                        if (!stockManager.decrease(Collections.singletonList(stockCalculateDTO))) {
                            throw new BusinessException("满额试用赠品库存不足!");
                        }
                    }
                    if(null != orderSkuCreateV2DTO.getFreeTasteId()){
                        // 0元尝鲜发放优惠券
                        List<FreeTrialSubActivityProduct> freeTrialSubActivityProducts = freeTrialSubActivityProductDao.selectByActivityId(orderSkuCreateV2DTO.getFreeTasteId());
                        if (CollectionUtils.isEmpty(freeTrialSubActivityProducts)) {
                            throw new BusinessException("0元尝鲜活动信息无商品信息");
                        }
                        Optional<FreeTrialSubActivityProduct> giftFirst = freeTrialSubActivityProducts.stream().filter(e -> e.getType() == 2 && e.getTargetType() == 2).findFirst();
                        if (!giftFirst.isPresent()) {
                            throw new BusinessException("0元尝鲜活动信息无赠品信息");
                        }
                        FreeTrialSubActivityProduct giftProduct = giftFirst.get();
                        Long ruleId = giftProduct.getTargetId();
                        CustomerUser customerUser = new CustomerUser();
                        customerUser.setId(user.getId());
                        CouponRule rule = couponRuleManager.findById(ruleId);
                        if(Objects.isNull(rule)){
                            throw new BusinessException("0元尝鲜活动优惠券不存在");
                        }
                        if(!Objects.equals(rule.getStatus(), CouponStatusTypeEnum.ONLINE.getCode())
                                || !Objects.equals(rule.getDelflag(), DeleteFlagEnum.IS_NOT_DELETE.getCode())){
                            throw new BusinessException("0元尝鲜活动优惠券已下线");
                        }
                        int row = couponRuleDao.updateReceiveCount(ruleId, 1);
                        if(row == 0){
                            throw new BusinessException("0元尝鲜活动优惠券库存不足");
                        }
                        CouponInfo freeTasteCoupon = CouponUtil.buildLuckyCouponInfo(CouponInfoStatusTypeEnum.OCCUPY, customerUser, rule);
                        couponRuleManager.insert(freeTasteCoupon);
                        FreeTrialSubActivityRecord insertRecord = new FreeTrialSubActivityRecord();
                        insertRecord.setFreeTrialSubActivityId(orderSkuCreateV2DTO.getFreeTasteId());
                        String currentTime = DateUtil.dateToString(new Date(), "yyyyMMddHHmmssSSS");
                        String orderNo = "LPKFT" + currentTime + (int) (Math.random() * 9000 + 1000);
                        insertRecord.setOrderNo(orderNo);
                        if(orderSkuCreateV2DTOList.size() == 1){
                            insertRecord.setSourceNo(orderCreateDTO.getOrderNo());
                        }else{
                            insertRecord.setSourceNo(orderCreateDTO.getOrderNo() + "-" + j);
                        }
                        insertRecord.setType(2);
                        insertRecord.setTargetId(ruleId);
                        insertRecord.setCount(1);
                        insertRecord.setSendFlag(0);
                        insertRecord.setOperator(customer.getNickName());
                        insertRecord.setCreateTime(new Date());
                        insertRecord.setUpdateTime(new Date());
                        insertRecord.setDelflag(0);
                        freeTrialSubActivityRecordDao.insert(insertRecord);
                    }
                }else{
                    // 周期购不校验
                    if(BasicFlagEnum.YES.getKey().equals(orderSkuCreateV2DTO.getCycleFlag())){
                        continue;
                    }
                    SkuBaseDTO skuBaseDTO = skuManager.detail(orderSkuCreateV2DTO.getSkuId());
                    // spu起售校验
                    if(Objects.nonNull(baseDTO.getMinimumCount())){
                        if(orderSkuCreateV2DTO.getCount() < baseDTO.getMinimumCount()){
                            throw new BusinessException("商品起售数量为" + baseDTO.getMinimumCount() + " " + baseDTO.getProductName());
                        }
                    }
                    // spu限购校验:1.统计所有相同spu的数量 2.查询此spu，该用户的历史下单数量 3.比较是否超过spu限购数量
                    if(Objects.nonNull(baseDTO.getPurchase())){
                        int sumSkuCount = orderSkuCreateV2DTOList.stream().filter(x -> x.getProductId().equals(orderSkuCreateV2DTO.getProductId()))
                                .mapToInt(OrderSkuCreateV2DTO::getCount).sum();
                        Integer orderCount = orderManager.getOrderCount(user.getId(), OrderCountTypeEnum.SPU, orderSkuCreateV2DTO.getProductId());
                        if (sumSkuCount + orderCount  > baseDTO.getPurchase()) {
                            throw new BusinessException("超出限购数量:" + baseDTO.getProductName());
                        }
                    }
                    // sku限购校验
                    Integer skuPurchase = NumberOptUtil.toInt(skuBaseDTO.skuAttrMap().get("purchase"), null);
                    if(Objects.nonNull(skuPurchase)){
                        int sumSkuCount = orderSkuCreateV2DTO.getCount();
                        Integer orderCount = orderManager.getOrderCount(user.getId(), OrderCountTypeEnum.SKU, orderSkuCreateV2DTO.getSkuId());
                        if (sumSkuCount + orderCount  > skuPurchase) {
                            throw new BusinessException("超出限购数量:" + baseDTO.getProductName());
                        }
                    }
                }
            }
        }

        orderCreateDTO.setReduceDiscount(fullSummaryVO.getReduceDiscount());
        orderCreateDTO.setCouponDiscount(fullSummaryVO.getCouponDiscount());
        // 积分活动校验
        Long pointAmountId = null;
        if(Objects.nonNull(orderCreateDTO.getUsePoint()) && orderCreateDTO.getUsePoint() > 0){
            pointAmountId = orderSkuCreateV2DTOList.stream().filter(x -> Objects.nonNull(x.getPointAmountId())).map(OrderSkuCreateV2DTO::getPointAmountId).findFirst().orElse(null);
            if(Objects.nonNull(pointAmountId)){
                PointAmount po = pointAmountManager.findById(pointAmountId);
                if(Objects.isNull(po)){
                    throw new BusinessException("积分抵现活动不存在，请重新刷新后下单");
                }
                if(Objects.equals(po.getStatus(), CommonActivityStatusEnum.NOT_STARTED.getCode())){
                    throw new BusinessException("积分抵现活动未开始，请重新刷新后下单");
                }
                if(Objects.equals(po.getStatus(), CommonActivityStatusEnum.END.getCode())){
                    throw new BusinessException("积分抵现活动已结束，请重新刷新后下单");
                }
            }
        }

        // 金额校验
        validAmount(orderCreateDTO, fullSummaryVO);

        //赠品券
        int couponGiftUpdateCount = 0;
        CouponInfo couponGiftInfo = new CouponInfo();
        // 这里获得的是ruleId
        if(Objects.nonNull(orderCreateDTO.getCouponGiftId())){
            // couponGiftInfo = couponRuleManager.findUserCouponById(orderCreateDTO.getUserId(), orderCreateDTO.getCouponGiftId());
            couponGiftInfo = couponRuleManager.findUserCouponByRuleId(orderCreateDTO.getUserId(), orderCreateDTO.getCouponGiftId());
            if (Objects.isNull(couponGiftInfo)) {
                throw new BusinessException("赠品券不存在!");
            }
            //赠品券校验 todo 先注释
            // orderManager.checkCouponGift(orderCreateDTO, couponGiftInfo, orderCreateDTO.getCouponId());
            List<SkuProductBaseDTO> skuProductBaseDTOList = skuManager.skuProductNoDelList(Arrays.asList(orderCreateDTO.getGiftSkuId()));
            if(CollectionUtils.isEmpty(skuProductBaseDTOList)){
                throw new BusinessException("赠品不存在!");
            }
            couponGiftUpdateCount = couponRuleManager.updateCouponUsedById(orderCreateDTO.getUserId(), couponGiftInfo.getId(), orderCreateDTO.getOrderNo());
            if(couponGiftUpdateCount > 0){
                //更新赠品库存
                StockCalculateDTO stockCalculateDTO = new StockCalculateDTO();
                stockCalculateDTO.setSkuId(orderCreateDTO.getGiftSkuId());
                stockCalculateDTO.setStockNum(Long.valueOf(orderCreateDTO.getGiftCount()));
                if (!stockManager.decrease(Collections.singletonList(stockCalculateDTO))) {
                    throw new BusinessException("赠品库存信息更新失败!");
                }
            }
            OrderGift orderGift = new OrderGift();
            orderGift.setCount(orderCreateDTO.getGiftCount());
            orderGift.setStatus(0);
            orderGift.setOrderNo(orderCreateDTO.getOrderNo());
            orderGift.setSkuId(orderCreateDTO.getGiftSkuId());
            orderGift.setSkuCode(skuProductBaseDTOList.get(0).getSkuCode());
            orderGift.setSkuDesc(skuProductBaseDTOList.get(0).getSpecValueList());
            orderGift.setProductName(skuProductBaseDTOList.get(0).getProductName());
            orderGift.setSkuPic(skuProductBaseDTOList.get(0).getPicUrl());
            orderGiftManager.save(orderGift);
        }
        // 判断父子订单类型
        OrderParentTypeEnum parentTypeEnum;
        if(orderSkuCreateV2DTOList.size() == 1){
            parentTypeEnum = OrderParentTypeEnum.SIGNAL;
            OrderSkuCreateV2DTO orderSkuCreateV2DTO = orderSkuCreateV2DTOList.get(0);
            orderSkuCreateV2DTO.setOrderNo(orderCreateDTO.getOrderNo());
            orderSkuCreateV2DTO.setOrderParentTypeEnum(parentTypeEnum);
        }else{
            parentTypeEnum = OrderParentTypeEnum.PARENT;
            for (int i = 0; i < orderSkuCreateV2DTOList.size(); i++) {
                OrderSkuCreateV2DTO orderSkuCreateV2DTO = orderSkuCreateV2DTOList.get(i);
                orderSkuCreateV2DTO.setOrderNo(orderCreateDTO.getOrderNo() + "-" + (i+1));
                orderSkuCreateV2DTO.setOrderParentTypeEnum(OrderParentTypeEnum.CHILD);
            }
            // todo 金额校验
            OrderInfo parenetOderInfo = OrderInfoUtil.buildParentOrder(orderCreateDTO, user, orderSyncAdapter);
            parenetOderInfo.setPointAmountId(pointAmountId);
            parenetOderInfo.setParentType(parentTypeEnum.getCode());
            if (couponUpdateCount > 0) {
                parenetOderInfo.setUseCouponStatus(1);
            }
            if (couponGiftUpdateCount > 0) {
                parenetOderInfo.setUseCouponGiftStatus(1);
            }
            orderManager.save(parenetOderInfo);

            OrderAddress orderAddress = new OrderAddress();
            orderAddress.setOrderNo(orderCreateDTO.getOrderNo());
            orderAddress.setReceiverName(userReceiveAddress.getReceiveName());
            orderAddress.setReceiverPhone(userReceiveAddress.getPhone());
            orderAddress.setProvince(userReceiveAddress.getProvinceName());
            orderAddress.setCity(userReceiveAddress.getCityName());
            orderAddress.setDistrict(userReceiveAddress.getDistrictName());
            orderAddress.setAddress(userReceiveAddress.getDetailAddress());
            orderAddressManager.saveList(Lists.newArrayList(orderAddress));
        }

        // 子订单处理开始
        List<OrderInfo> orderInfoList = Lists.newArrayList();
        for (OrderSkuCreateV2DTO orderSkuCreateV2DTO : orderSkuCreateV2DTOList) {
            SkuBaseDTO skuBaseDTO = skuManager.detail(orderSkuCreateV2DTO.getSkuId());
            ProductBaseDTO baseDTO = baseDTOMap.get(orderSkuCreateV2DTO.getProductId());
            ProductTypeEnum productTypeEnum = ProductTypeEnum.getEnum(baseDTO.getProductType());
            orderSkuCreateV2DTO.setProductType(productTypeEnum.getCode());

            //地区可配送判断
            if(!deliveryAddressTemplateManager.checkCanDelivery(orderSkuCreateV2DTO.getProductId(),String.valueOf(userReceiveAddress.getProvinceId()),String.valueOf(userReceiveAddress.getCityId()),String.valueOf(userReceiveAddress.getDistrictId()))){
                throw new BusinessException("当前地区不可配送，请重新选择");
            }

            if(ProductTypeEnum.ENTITY_CARD.getCode().equals(orderSkuCreateV2DTO.getProductType())
                    || ProductTypeEnum.SINGLE_PRODUCT.getCode().equals(orderSkuCreateV2DTO.getProductType())){

                if (null == skuBaseDTO) {
                    throw new BusinessException("该商品不存在");
                }
            }
            OrderCycleCreateV2DTO orderCycleCreateV2DTO = orderSkuCreateV2DTO.getOrderCycleCreateDTO();
            //周期购售价
            BigDecimal cycleSalePrice = getCycleSalePrice(orderSkuCreateV2DTO, skuBaseDTO);

            List<OrderSku> orderSkus = new ArrayList<>();
            List<OrderAddress> orderAddresses = new ArrayList<>();
            if(ProductTypeEnum.PACKAGE == productTypeEnum){
                //电子卡
                orderInfo = OrderInfoUtil.convertOrderInfoCreate(orderSkuCreateV2DTO, orderCreateDTO, parentTypeEnum);
                orderInfo.setProductType(OrderProductTypeEnum.VIRTUAL_CARD.getCode());
                orderInfo.setOrderType(OrderTypeEnum.CARD_ORDER.getCode());
                orderInfo.setOrderStatus(OrderStatusEnum.PAYMENT.getCode());
                orderInfo.setChannel(OrderChannelEnum.LPK.getCode());
                orderInfo.setBuyerNick(user.getUserName());

                orderInfo.setShopNo(orderSyncAdapter.getMallShopCode());
                orderInfo.setShopName("商城电子奶卡");
                orderInfo.setPlatform(OrderPlatformEnum.ZIYAN.getCode());

                OrderAddress orderAddress = new OrderAddress();
                orderAddress.setOrderNo(orderInfo.getOrderNo());
                orderAddress.setReceiverName(userReceiveAddress.getReceiveName());
                orderAddress.setReceiverPhone(userReceiveAddress.getPhone());
                orderAddress.setProvince(userReceiveAddress.getProvinceName());
                orderAddress.setCity(userReceiveAddress.getCityName());
                orderAddress.setDistrict(userReceiveAddress.getDistrictName());
                orderAddress.setAddress(userReceiveAddress.getDetailAddress());
                orderAddresses.add(orderAddress);

                OrderSku orderSku = OrderInfoUtil.convertOrderSkuCreate(orderSkuCreateV2DTO, baseDTO, skuBaseDTO);
                orderSkus.add(orderSku);

            }else if(ProductTypeEnum.SINGLE_PRODUCT == productTypeEnum || ProductTypeEnum.DERIVATIVE == productTypeEnum){
                //单品, 衍生品当单品处理
                orderInfo = OrderInfoUtil.convertOrderInfoCreate(orderSkuCreateV2DTO, orderCreateDTO, parentTypeEnum);
                if(ProductTypeEnum.SINGLE_PRODUCT == productTypeEnum){
                    if(ProductTemperatureEnum.LOW.getCode().equals(baseDTO.getTemperature())){
                        orderInfo.setProductType(OrderProductTypeEnum.LOW_PRODUCT.getCode());
                    }else{
                        orderInfo.setProductType(OrderProductTypeEnum.SINGLE_PRODUCT.getCode());
                    }
                }else{
                    // 衍生品
                    orderInfo.setProductType(OrderProductTypeEnum.getEnum(productTypeEnum).getCode());
                }
                if(BasicFlagEnum.YES.getKey().equals(orderSkuCreateV2DTO.getCycleFlag())){
                    // 发货时间校验
                    // 周期购 首次发货日期校验
                    milkDispatchRuleManager.checkLowMilkDispatchDa(orderCycleCreateV2DTO.getDispatchType(), orderCycleCreateV2DTO.getTimes(),
                            orderCycleCreateV2DTO.getDispatchDate(), orderSkuCreateV2DTO.getProductId(),false);

                    orderInfo.setTotalAmount(cycleSalePrice);
                    orderInfo.setOrderType(OrderTypeEnum.CYLE_ORDER.getCode());
                    OrderCycle orderCycle = new OrderCycle();
                    orderCycle.setOrderNo(orderInfo.getOrderNo());
                    orderCycle.setUnit(ProductUnitEnum.getDescByCode(orderCycleCreateV2DTO.getUnit()));
                    orderCycle.setSkuId(orderSkuCreateV2DTO.getSkuId());
                    orderCycle.setSkuCode(skuBaseDTO.getSkuCode());
                    orderCycle.setSkuDesc(skuBaseDTO.getSpecValueList());
                    orderCycle.setProductId(orderSkuCreateV2DTO.getProductId());
                    orderCycle.setProductName(baseDTO.getProductName());
                    orderCycle.setTimes(orderCycleCreateV2DTO.getTimes());
                    orderCycle.setMilkAmount(orderCycleCreateV2DTO.getMilkAmount());
                    orderCycle.setDispatchType(orderCycleCreateV2DTO.getDispatchType());
                    orderCycle.setIntervalTime(orderCycleCreateV2DTO.getIntervalTime());
                    orderCycle.setDispatchDate(orderCycleCreateV2DTO.getDispatchDate());
                    orderCycle.setDispatchMode(orderCycleCreateV2DTO.getDispatchMode());
                    orderCycle.setReceiverName(userReceiveAddress.getReceiveName());
                    orderCycle.setReceiverPhone(userReceiveAddress.getPhone());
                    orderCycle.setProvince(userReceiveAddress.getProvinceName());
                    orderCycle.setProvinceCode(String.valueOf(userReceiveAddress.getProvinceId()));
                    orderCycle.setCity(userReceiveAddress.getCityName());
                    orderCycle.setCityCode(String.valueOf(userReceiveAddress.getCityId()));
                    orderCycle.setDistrict(userReceiveAddress.getDistrictName());
                    orderCycle.setDistrictCode(String.valueOf(userReceiveAddress.getDistrictId()));
                    orderCycle.setAddressDetail(userReceiveAddress.getDetailAddress());
                    orderCycle.setCycleCode(baseDTO.getCycleCode());
                    orderCycleManager.save(orderCycle);
                }else{
                    orderInfo.setOrderType(OrderTypeEnum.PRODUCT_ORDER.getCode());
                }

                orderInfo.setOrderStatus(OrderStatusEnum.PAYMENT.getCode());
                orderInfo.setChannel(OrderChannelEnum.LPK.getCode());
                orderInfo.setBuyerNick(user.getUserName());
                orderInfo.setShopNo(orderSyncAdapter.getMallShopCode());
                orderInfo.setShopName("商城单品");
                orderInfo.setPlatform(OrderPlatformEnum.ZIYAN.getCode());

                OrderAddress orderAddress = new OrderAddress();
                orderAddress.setOrderNo(orderInfo.getOrderNo());
                orderAddress.setReceiverName(userReceiveAddress.getReceiveName());
                orderAddress.setReceiverPhone(userReceiveAddress.getPhone());
                orderAddress.setProvince(userReceiveAddress.getProvinceName());
                orderAddress.setCity(userReceiveAddress.getCityName());
                orderAddress.setDistrict(userReceiveAddress.getDistrictName());
                orderAddress.setAddress(userReceiveAddress.getDetailAddress());
                orderAddresses.add(orderAddress);

                OrderSku orderSku = OrderInfoUtil.convertOrderSkuCreate(orderSkuCreateV2DTO, baseDTO, skuBaseDTO);
                if(BasicFlagEnum.YES.getKey().equals(orderSkuCreateV2DTO.getCycleFlag())) {
                    orderSku.setTotalAmount(cycleSalePrice);
                    orderSku.setOriginPrice(orderSkuCreateV2DTO.getProductAmount());
                }else{
                    // 可有可无 todo
                    // orderSku.setTotalAmount(orderSkuCreateV2DTO.getOriginPrice().multiply(BigDecimal.valueOf(orderSkuCreateV2DTO.getCount())));
                }
                orderSkus.add(orderSku);
            }else if(ProductTypeEnum.ENTITY_CARD == productTypeEnum){
                //实体卡
                orderInfo = OrderInfoUtil.convertOrderInfoCreate(orderSkuCreateV2DTO, orderCreateDTO, parentTypeEnum);
                orderInfo.setProductType(OrderProductTypeEnum.PHYSICAL_CARD.getCode());
                orderInfo.setOrderType(OrderTypeEnum.CARD_ORDER.getCode());
                orderInfo.setOrderStatus(OrderStatusEnum.PAYMENT.getCode());
                orderInfo.setChannel(OrderChannelEnum.LPK.getCode());
                orderInfo.setBuyerNick(user.getUserName());
                orderInfo.setShopNo(orderSyncAdapter.getMallShopCode());
                orderInfo.setShopName("商城实体卡");
                orderInfo.setPlatform(OrderPlatformEnum.ZIYAN.getCode());

                OrderAddress orderAddress = new OrderAddress();
                orderAddress.setOrderNo(orderInfo.getOrderNo());
                orderAddress.setReceiverName(userReceiveAddress.getReceiveName());
                orderAddress.setReceiverPhone(userReceiveAddress.getPhone());
                orderAddress.setProvince(userReceiveAddress.getProvinceName());
                orderAddress.setCity(userReceiveAddress.getCityName());
                orderAddress.setDistrict(userReceiveAddress.getDistrictName());
                orderAddress.setAddress(userReceiveAddress.getDetailAddress());
                orderAddresses.add(orderAddress);

                OrderSku orderSku = OrderInfoUtil.convertOrderSkuCreate(orderSkuCreateV2DTO, baseDTO, skuBaseDTO);
                orderSkus.add(orderSku);
            }

            if(ProductTypeEnum.ENTITY_CARD == productTypeEnum
                    ||ProductTypeEnum.SINGLE_PRODUCT == productTypeEnum
                    ||ProductTypeEnum.DERIVATIVE == productTypeEnum
                    ||ProductTypeEnum.PACKAGE == productTypeEnum){
                if(Objects.nonNull(orderSkuCreateV2DTO.getPtActivityId())){
                    //拼团库存前面已扣
                }else if(Objects.nonNull(orderSkuCreateV2DTO.getDiscountActivityId())){
                    for (OrderSku sku: orderSkus) {
                        //更新库存
                        if(BasicFlagEnum.YES.getKey().equals(orderSkuCreateV2DTO.getCycleFlag())){
                            continue;
                        }
                        if (!discountActivityRangeDao.decreaseStock(orderSkuCreateV2DTO.getDiscountActivityId(), sku.getSkuId(), Long.valueOf(sku.getCount()))) {
                            throw new BusinessException("活动库存不足!");
                        }
                    }
                }else{
                    for (OrderSku sku: orderSkus) {
                        //更新库存
                        StockCalculateDTO stockCalculateDTO = new StockCalculateDTO();
                        stockCalculateDTO.setSkuId(sku.getSkuId());
                        if(BasicFlagEnum.YES.getKey().equals(orderSkuCreateV2DTO.getCycleFlag())){
                            continue;
                        }else{
                            stockCalculateDTO.setStockNum(Long.valueOf(sku.getCount()));
                        }
                        if (!stockManager.decrease(Collections.singletonList(stockCalculateDTO))) {
                            throw new BusinessException("商品库存信息更新失败!");
                        }
                    }
                }
            }
            if (couponUpdateCount > 0) {
                orderInfo.setUseCouponStatus(1);
            }
            if (couponGiftUpdateCount > 0) {
                orderInfo.setUseCouponGiftStatus(1);
            }
            orderInfo.setNascentFlag(OrderNascentFlagEnum.NO_SYNCHRONIZED.getCode());
            orderManager.save(orderInfo);
            orderSkuManager.saveMore(orderSkus);
            orderAddressManager.saveList(orderAddresses);
            orderInfo.setOrderSkus(orderSkus);
            orderInfoList.add(orderInfo);
        }
        // 积分扣减
        if(orderCreateDTO.getPoint() != null && !Objects.equals(orderCreateDTO.getPoint(), 0)){
            NascentUserVO nascentUserVO = nascentCustomerManager.getNascentUserVO(customer.getId(), customer.getPhone());
            PointReduceResponse response = nascentCustomerManager.reducePointV2(nascentUserVO.getNasOuid(), nascentUserVO.getPlatform(),
                    NascentPointTypeEnum.ORDER_POINT_AMOUNT_REDUCE.getCode(),
                    BigDecimal.valueOf(orderCreateDTO.getPoint()), NascentPointTypeEnum.ORDER_POINT_AMOUNT_REDUCE.getRemark());
            if(response == null){
                throw new BusinessException("积分抵现活动暂不可用，请重新刷新后下单");
            }
            if(!response.getSuccess()){
                if(Objects.equals(604, response.getCode())){
                    // 积分不够扣减, 刷新积分
                    CustomerPointInfo customerPointInfo = nascentCustomerManager.customerPointInfoGet(nascentUserVO.getNasOuid(), nascentUserVO.getPlatform());
                    if(customerPointInfo != null && !Objects.equals(customer.getPoint(), customerPointInfo.getAvailPoint())){
                        // 更新用户总积分
                        customer.setPoint(customerPointInfo.getAvailPoint().intValue());
                        customerUserManager.updateOne(customer);
                    }
                    throw new BusinessException("扣减积分不足，请重新刷新后下单");
                }else{
                    // 其他错误
                    throw new BusinessException("积分抵现活动暂不可用，请重新刷新后下单");
                }
            }else{
                // 成功，更新用户总积分
                customer.setPoint(response.getResult().getAvailPoint().intValue());
                customerUserManager.updateOne(customer);
            }
        }
        // 满减满送
        insertFullReduceRecord(orderCreateDTO, user, orderInfoList);

        if (couponUpdateCount > 0) {
            //优惠券核销处理
            // todo 暂时去掉优惠券校验
            couponRuleManager.miniUseCoupon(couponInfo);
        }
        if (couponGiftUpdateCount > 0) {
            //赠品券核销处理
            couponRuleManager.miniUseCoupon(couponGiftInfo);
        }

        Integer timeOut = Objects.nonNull(firstOrderSku.getPtActivityId()) ? 5 : 30;

        redisOperation.setnx("LPK:" + orderCreateDTO.getOrderNo(), "LPK", timeOut, TimeUnit.MINUTES);
        redisOperation.del("Order::setOrderNo_" + orderCreateDTO.getOrderNo());

        if(Objects.nonNull(firstOrderSku.getPtOrderId())){
            redisOperation.del("Order::ptOrderId_" + firstOrderSku.getPtOrderId());
        }

        TransactionUtils.afterCommitAsyncExecute(pointTask, () -> {
            log.info("事务提交后执行订单来源信息保存");
            milkProducerService.orderCancel(orderCreateDTO.getOrderNo(), timeOut);
            // 订单上报有数
            milkProducerService.syncOrderToYoushu(orderCreateDTO.getOrderNo());
            // 通过延时信息查询南讯更新用户信息
            milkProducerService.queryNascent(user.getId(), UserEventTypeEnum.ORDER);
            // 来源信息保存
            orderSkuSenceManager.saveSence(orderInfoList);

            // 如果带有postId，则统计到community_user_order_stats表中
            if (Objects.nonNull(orderCreateDTO.getPostId())) {
                saveStats(orderCreateDTO.getOrderNo(), orderCreateDTO.getPostId(), user.getId());
            }
        });
    }

    private void saveStats(String orderNo, Long postId, Long userId) {
        try {
            log.info("帖子引导订单统计，订单号：{}，帖子ID：{}", orderNo, postId);
            // 获取订单信息
            OrderInfo orderInfo = orderManager.selectByOrderNo(orderNo);
            if (Objects.nonNull(orderInfo)) {
                // 查询帖子信息，获取发帖人ID
                Long postUserId = 0L;
                CommunityPost post = communityPostDao.findById(postId);
                if(post == null){
                    return;
                }
                // 创建统计记录
                Date now = new Date();
                CommunityUserOrderStats stats = new CommunityUserOrderStats();
                stats.setPostId(postId);
                stats.setPostUserId(post.getUserId()); // 设置发帖人ID
                stats.setOrderNo(orderNo);
                stats.setRealAmount(orderInfo.getRealAmount());
                stats.setUserId(userId);
                stats.setPayTime(orderInfo.getPayTime());
                stats.setPayStatus(0);
                stats.setCreateTime(now);
                stats.setUpdateTime(now);
                stats.setDelflag(0);

                // 保存统计记录
                communityUserOrderStatsManager.insert(stats);
                log.info("帖子引导订单统计保存成功，订单号：{}，帖子ID：{}", orderNo, postId);
            }
        } catch (Exception e) {
            log.error("帖子引导订单统计异常，订单号：{}，帖子ID：{}", orderNo, postId, e);
        }
    }

    private void checkFullReduceProduct(FullReduceMallDTO fullReduceMallDTO) {
        if(null == fullReduceMallDTO){
            return;
        }
        List<FullReduceRuleRange> fullReduceRuleRangeList = fullReduceRuleRangeManager.findByFullReduceId(fullReduceMallDTO.getId());
        List<FullSkuVO> gifts = fullReduceMallDTO.getGifts();

        if(CollectionUtils.isNotEmpty(gifts)){
            List<Long> productRange = StreamUtils.filterConvert(fullReduceRuleRangeList, x-> Objects.equals(x.getType(), FullRuleRangeTypeEnum.GIFT.getCode()), FullReduceRuleRange::getTargetId);
            if(!productRange.contains(gifts.get(0).getId())){
                throw new BusinessException("赠品有误！");
            }
        }
        List<FullCouponRuleVO> coupons = fullReduceMallDTO.getCoupons();
        if(CollectionUtils.isNotEmpty(coupons)){
            List<Long> couponRange = StreamUtils.filterConvert(fullReduceRuleRangeList, x-> Objects.equals(x.getType(), FullRuleRangeTypeEnum.COUNPON.getCode()), FullReduceRuleRange::getTargetId);
            List<Long> fullCouponRuleIds = StreamUtils.toList(coupons, FullCouponRuleVO::getId);
            for (Long fullCouponRuleId: fullCouponRuleIds) {
                if(!couponRange.contains(fullCouponRuleId)){
                    throw new BusinessException("优惠券有误！");
                }
            }
        }
    }

    private static void validAmount(OrderCreateV2DTO orderCreateDTO, FullSummaryVO fullSummaryVO) {
        //总实付金额校验
        BigDecimal realAmount = StreamUtils.mapReduce(orderCreateDTO.getOrderSkuCreateV2DTOList(), OrderSkuCreateV2DTO::getRealAmount, BigDecimal::add);
        if(realAmount.compareTo(orderCreateDTO.getRealAmount()) != 0){
            throw new BusinessException("实付金额校验不通过");
        }
        if(realAmount.compareTo(fullSummaryVO.getRealAmount()) != 0){
            throw new BusinessException("实付金额校验不通过");
        }
        // 应付金额校验
        BigDecimal totalAmount = StreamUtils.mapReduce(orderCreateDTO.getOrderSkuCreateV2DTOList(), OrderSkuCreateV2DTO::getTotalAmount, BigDecimal::add);
        if(totalAmount.compareTo(orderCreateDTO.getTotalAmount()) != 0){
            throw new BusinessException("应付金额校验不通过");
        }
        if(totalAmount.compareTo(fullSummaryVO.getTotalAmount()) != 0){
            throw new BusinessException("应付金额校验不通过");
        }
        // 总金额校验
        BigDecimal productAmount = StreamUtils.mapReduce(orderCreateDTO.getOrderSkuCreateV2DTOList(), OrderSkuCreateV2DTO::getProductAmount, BigDecimal::add);
        if(productAmount.compareTo(orderCreateDTO.getProductAmount()) != 0){
            throw new BusinessException("总金额校验不通过");
        }
        if(productAmount.compareTo(fullSummaryVO.getProductAmount()) != 0){
            throw new BusinessException("总金额校验不通过");
        }
        // 满减金额校验
        BigDecimal reduceDiscount = orderCreateDTO.getOrderSkuCreateV2DTOList().stream().map(OrderSkuCreateV2DTO::getReduceDiscount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
        if(reduceDiscount.compareTo(orderCreateDTO.getReduceDiscount()) != 0){
            throw new BusinessException("满减金额校验不通过");
        }
        // 优惠券金额校验
        BigDecimal couponDiscount = orderCreateDTO.getOrderSkuCreateV2DTOList().stream().map(OrderSkuCreateV2DTO::getCouponDiscount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
        if(couponDiscount.compareTo(orderCreateDTO.getCouponDiscount()) != 0){
            throw new BusinessException("优惠券金额校验不通过");
        }
        // 积分抵扣金额校验
        BigDecimal pointDiscount = orderCreateDTO.getOrderSkuCreateV2DTOList().stream().map(OrderSkuCreateV2DTO::getPointDiscount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
        if(pointDiscount.compareTo(orderCreateDTO.getPointDiscount()) != 0){
            throw new BusinessException("积分抵扣金额校验不通过");
        }
        if(pointDiscount.compareTo(fullSummaryVO.getPointDiscount()) != 0){
            throw new BusinessException("积分抵扣金额校验不通过");
        }
        // 积分校验
        Integer point = orderCreateDTO.getOrderSkuCreateV2DTOList().stream().map(OrderSkuCreateV2DTO::getPoint).filter(Objects::nonNull).reduce(0, Integer::sum);
        if(!Objects.equals(point, orderCreateDTO.getPoint())){
            throw new BusinessException("积分校验不通过");
        }
        if(!Objects.equals(point, fullSummaryVO.getPoint())){
            throw new BusinessException("积分校验不通过");
        }
    }

    private void insertFullReduceRecord(OrderCreateV2DTO orderCreateDTO, CustomerUserVO user, List<OrderInfo> orderInfoList) {
        Map<Long, List<OrderInfo>> fullReduceOrderMap = StreamUtils.filterGroup(orderInfoList, x -> x.getFullReduceId() != null, OrderInfo::getFullReduceId);
        // 根据ruleId生成优惠券信息：可能多个
        List<FullReduceMallDTO> fullReduceMallList = orderCreateDTO.getFullReduceMallList() == null ? Lists.newArrayList() : orderCreateDTO.getFullReduceMallList();
        for (FullReduceMallDTO fullReduceMallDTO : fullReduceMallList) {
            if(!fullReduceOrderMap.keySet().contains(fullReduceMallDTO.getId())){
                continue;
            }
            List<FullCouponRuleVO> giftCoupons = fullReduceMallDTO.getCoupons();
            if(CollectionUtils.isEmpty(giftCoupons)){
                continue;
            }
            for (FullCouponRuleVO fullCouponRuleVO : giftCoupons){
                List<OrderInfo> fullReduceOrderInfos = fullReduceOrderMap.get(fullReduceMallDTO.getId());
                //记录满减记录
                FullReduceRecord fullReduceRecord = new FullReduceRecord();
                fullReduceRecord.setFullRecordId(fullReduceMallDTO.getId());
                String orderNos = StreamUtils.joinStringFilter(fullReduceOrderInfos, ",", OrderInfo::getOrderNo, StringUtils::isNotBlank);
                fullReduceRecord.setOrderNo(fullReduceOrderInfos.get(0).getSrcNo());
                fullReduceRecord.setSrcNo(fullReduceOrderInfos.get(0).getSrcNo());
                fullReduceRecord.setSourceNo(orderNos);
                fullReduceRecord.setType(2);//优惠券
                fullReduceRecord.setTargetId(fullCouponRuleVO.getId());
                fullReduceRecord.setAfterSaleStatus(0);//未售后
                fullReduceRecord.setCount(fullCouponRuleVO.getCount());
                fullReduceRecord.setOperator(StringUtils.isBlank(user.getNickName()) ? "" : user.getNickName());
                Date now = new Date();
                fullReduceRecord.setCreateTime(now);
                fullReduceRecord.setUpdateTime(now);
                fullReduceRecord.setDelflag(DeleteFlagEnum.IS_NOT_DELETE.getCode());
                fullReduceRecordManager.insert(fullReduceRecord);
                // 发放优惠券
                CustomerUser customerUser = new CustomerUser();
                customerUser.setId(user.getId());
                CouponRule rule = couponRuleManager.findById(fullCouponRuleVO.getId());
                List<CouponInfo> couponInfoList = Lists.newArrayList();
                for (Integer i = 0; i < fullReduceRecord.getCount(); i++) {
                    CouponInfo couponInfo = CouponUtil.buildLuckyCouponInfo(CouponInfoStatusTypeEnum.OCCUPY, customerUser, rule);
                    couponInfoList.add(couponInfo);
                }
                couponInfoManager.insertList(couponInfoList);
            }
        }

        for (FullReduceMallDTO fullReduceMallDTO : fullReduceMallList) {
            if(!fullReduceOrderMap.keySet().contains(fullReduceMallDTO.getId())){
                continue;
            }
            List<OrderInfo> fullReduceOrderInfos = fullReduceOrderMap.get(fullReduceMallDTO.getId());
            List<OrderInfo> giftOrderInfos = StreamUtils.filter(fullReduceOrderInfos, x -> Objects.equals(FlagEnum.YES.getCode(), x.getIsGift()));
            List<OrderInfo> normalOrderInfos = StreamUtils.filter(fullReduceOrderInfos, x -> Objects.equals(FlagEnum.NO.getCode(), x.getIsGift()));
            if(CollectionUtils.isEmpty(giftOrderInfos)){
                continue;
            }
            if(giftOrderInfos.size() > 1){
                throw new BusinessException("赠品订单数量异常");
            }
            OrderInfo giftOrderInfo = giftOrderInfos.get(0);
            //记录满减记录
            FullReduceRecord fullReduceRecord = new FullReduceRecord();
            fullReduceRecord.setFullRecordId(fullReduceMallDTO.getId());
            String orderNos = StreamUtils.joinStringFilter(normalOrderInfos, ",", OrderInfo::getOrderNo, StringUtils::isNotBlank);
            fullReduceRecord.setOrderNo(giftOrderInfo.getOrderNo());
            fullReduceRecord.setSrcNo(giftOrderInfo.getSrcNo());
            fullReduceRecord.setSourceNo(orderNos);
            fullReduceRecord.setType(1);//赠品
            fullReduceRecord.setTargetId(giftOrderInfo.getOrderSkus().get(0).getSkuId());
            fullReduceRecord.setAfterSaleStatus(0);//未售后
            fullReduceRecord.setCount(giftOrderInfo.getOrderSkus().get(0).getCount());
            fullReduceRecord.setOperator(StringUtils.isBlank(user.getNickName()) ? "" : user.getNickName());
            Date now = new Date();
            fullReduceRecord.setCreateTime(now);
            fullReduceRecord.setUpdateTime(now);
            fullReduceRecord.setDelflag(DeleteFlagEnum.IS_NOT_DELETE.getCode());
            fullReduceRecordManager.insert(fullReduceRecord);
        }
    }

    @Override
    public OrderInfoGiftVO orderFullGift(String orderNo) {
        return orderV2Manager.orderFullGift(orderNo);
    }

    /**
     * 生成0元订单
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public OrderInfo createLuckyOrderV2Zero(CustomerUser customerUser, SkuBaseDTO skuBaseDTO, AddressDetailVO userReceiveAddress, LuckyRecord luckyRecord) {
        Date now = new Date();
        ProductBaseDTO productBaseDTO = productManager.singleGetOne(skuBaseDTO.getProductId());
        OrderCreateZeroDTO dto = new OrderCreateZeroDTO();
        dto.setCreateTime(now);
        dto.setPayTime(now);
        dto.setSkuBaseDTO(skuBaseDTO);
        dto.setProductBaseDTO(productBaseDTO);
        OrderInfo orderInfo = createOrderInfo(OrderTypeEnum.LUCKY_ORDER, null,customerUser, userReceiveAddress, dto);
        // 更新中奖记录领取状态,中奖记录：领取状态、订单号回填
        LuckyRecord updateRecord = new LuckyRecord();
        updateRecord.setId(luckyRecord.getId());
        updateRecord.setGetTime(now);
        updateRecord.setPrizeStatus(LuckyRecordPrizeStatusEnum.WINNING.getCode());
        updateRecord.setUpdateTime(now);
        updateRecord.setOrderNo(orderInfo.getOrderNo());
        luckyRecordManager.update(updateRecord);
        return orderInfo;
    }

    @Override
    public OrderInfo createOrderInfo(OrderTypeEnum orderTypeEnum, OrderSourceTypeEnum sourceTypeEnum, CustomerUser customerUser, AddressDetailVO userReceiveAddress, OrderCreateZeroDTO dto) {
        // 生成订单主表
        OrderInfo orderInfo = new OrderInfo();
        Date createTime;
        Date payTime;
        String orderNo;
        String currentTime = DateUtil.dateToString(new Date(), "yyyyMMddHHmmssSSS");
        Integer count;
        Integer partPoint = null;
        SkuBaseDTO skuBaseDTO = dto.getSkuBaseDTO();
        ProductBaseDTO productBaseDTO = dto.getProductBaseDTO();
        BigDecimal originPrice;
        BigDecimal productAmount;
        if(orderTypeEnum == OrderTypeEnum.LUCKY_ORDER){
            createTime = dto.getCreateTime();
            payTime = dto.getPayTime();
            orderNo = "LPKZP" + currentTime + (int) (Math.random() * 9000 + 1000);
            count = 1;
            originPrice = BigDecimal.ZERO;
            productAmount = BigDecimal.ZERO;
            orderInfo.setAmountType(OrderAmountTypeEnum.FREE.getCode());
        }else if(orderTypeEnum == OrderTypeEnum.POINT_ORDER
                || sourceTypeEnum == OrderSourceTypeEnum.FREE_TASTE){
            Date now = new Date();
            createTime = now;
            payTime = now;
            orderNo = "LPKJF" + currentTime + (int) (Math.random() * 9000 + 1000);
            count = dto.getCount();
            orderInfo.setPoint(dto.getTotalPoint());
            partPoint = dto.getPartPoint();
            originPrice = dto.getPrice();
            productAmount = new BigDecimal(count).multiply(originPrice);
            orderInfo.setAmountType(OrderAmountTypeEnum.POINT.getCode());
        }else if(orderTypeEnum == OrderTypeEnum.FREE_TRIAL){
            Date now = new Date();
            createTime = now;
            payTime = now;
            orderNo = "LPKFT" + currentTime + (int) (Math.random() * 9000 + 1000);
            count = 1;
            originPrice = dto.getPrice();
            productAmount = new BigDecimal(count).multiply(originPrice);
            orderInfo.setAmountType(OrderAmountTypeEnum.FREE.getCode());
        }else{
            throw new BusinessException("不支持的订单类型");
        }
        orderInfo.setParentType(OrderParentTypeEnum.SIGNAL.getCode());
        orderInfo.setOrderNo(orderNo);
        orderInfo.setSrcNo(orderNo);
        orderInfo.setOrderParentNo(orderNo);
        orderInfo.setUserId(customerUser.getId());
        orderInfo.setProductAmount(productAmount);
        orderInfo.setTotalAmount(BigDecimal.ZERO);
        orderInfo.setRealAmount(BigDecimal.ZERO);
        orderInfo.setIsComment(FlagEnum.YES.getCode());
        ProductTypeEnum productTypeEnum = ProductTypeEnum.getEnum(productBaseDTO.getProductType());
        orderInfo.setProductType(OrderProductTypeEnum.getEnum(productTypeEnum).getCode());
        if(null != orderTypeEnum){
            orderInfo.setOrderType(orderTypeEnum.getCode());
        }
        orderInfo.setIsGift(FlagEnum.YES.getCode());
        if(sourceTypeEnum == OrderSourceTypeEnum.FREE_TASTE){
            orderInfo.setOrderType(OrderTypeEnum.getEnum(productTypeEnum).getCode());
            orderInfo.setFreeTasteId(dto.getFreeTasteId());
            orderInfo.setIsGift(FlagEnum.NO.getCode());
            orderInfo.setIsComment(FlagEnum.NO.getCode());
        }
        orderInfo.setOrderStatus(OrderStatusEnum.DELIVERY.getCode());
        orderInfo.setPushFlag(PushFlagEnum.NOT_PUSH.getCode());
        orderInfo.setChannel(OrderChannelEnum.LPK.getCode());
        // todo
        orderInfo.setBuyerNick(customerUser.getNickName());
        orderInfo.setShopNo(orderSyncAdapter.getMallShopCode());
        orderInfo.setShopName("商城电子奶卡");
        orderInfo.setPlatform(OrderPlatformEnum.ZIYAN.getCode());
        orderInfo.setCreateTime(createTime);
        orderInfo.setPayTime(payTime);

        if(null != sourceTypeEnum){
            orderInfo.setSourceType(sourceTypeEnum.getCode());
        }
        // 生成订单明细
        OrderSku orderSku = new OrderSku();
        orderSku.setPoint(partPoint);
        orderSku.setProductId(productBaseDTO.getId());
        orderSku.setProductCode(productBaseDTO.getProductName());
        orderSku.setSkuId(skuBaseDTO.getId());
        orderSku.setSkuCode(skuBaseDTO.getSkuCode());
        orderSku.setOrderNo(orderInfo.getOrderNo());
        orderSku.setCategoryId(productBaseDTO.getCardCategoryId());
        orderSku.setCategoryName(productBaseDTO.getCardCategoryName());
        orderSku.setProductName(productBaseDTO.getProductName());
        orderSku.setSkuDesc(skuBaseDTO.getSpecValueList());
        orderSku.setSkuPicUrl(StringUtils.isNotBlank(skuBaseDTO.getPicUrl()) ? skuBaseDTO.getPicUrl() : productBaseDTO.getPicUrl());
        orderSku.setCount(count);
        orderSku.setOriginPrice(originPrice);
        orderSku.setTotalAmount(BigDecimal.ZERO);
        orderSku.setRealAmount(BigDecimal.ZERO);
        orderSku.setUnlockFlag(OrderUnlockFlagEnum.UNLOCKED.getCode());
        orderSku.setSnSyncFlag(SnSyncFlagEnum.SYNC.getCode());
        orderSku.setOrderSkuNo(orderInfo.getOrderNo() + "-" + orderSku.getSkuCode());
        // 生成订单地址
        OrderAddress orderAddress = new OrderAddress();
        orderAddress.setOrderNo(orderInfo.getOrderNo());
        orderAddress.setReceiverName(userReceiveAddress.getReceiveName());
        orderAddress.setReceiverPhone(userReceiveAddress.getPhone());
        orderAddress.setProvince(userReceiveAddress.getProvinceName());
        orderAddress.setCity(userReceiveAddress.getCityName());
        orderAddress.setDistrict(userReceiveAddress.getDistrictName());
        orderAddress.setAddress(userReceiveAddress.getDetailAddress());

        orderManager.save(orderInfo);
        orderSkuManager.saveMore(Lists.newArrayList(orderSku));
        orderAddressManager.saveList(Lists.newArrayList(orderAddress));

        if(orderTypeEnum == OrderTypeEnum.POINT_ORDER
                || sourceTypeEnum == OrderSourceTypeEnum.FREE_TASTE){
            StockCalculateDTO stockCalculateDTO = new StockCalculateDTO();
            stockCalculateDTO.setSkuId(orderSku.getSkuId());
            stockCalculateDTO.setStockNum(Long.valueOf(orderSku.getCount()));
            if (!stockManager.decrease(Collections.singletonList(stockCalculateDTO))) {
                throw new BusinessException("商品库存信息更新失败!");
            }
        }

        OrderConfirmDTO confirmDTO = new OrderConfirmDTO();
        confirmDTO.setOrderNo(orderInfo.getOrderNo());
        confirmDTO.setPayTime(String.valueOf(payTime.getTime()));
        if (OrderProductTypeEnum.VIRTUAL_CARD.getCode().equals(orderInfo.getProductType())) {
            orderRemoteService.confirmOrder(confirmDTO, orderInfo);
        } else if (OrderProductTypeEnum.PHYSICAL_CARD.getCode().equals(orderInfo.getProductType())
                || OrderProductTypeEnum.SINGLE_PRODUCT.getCode().equals(orderInfo.getProductType())
                || OrderProductTypeEnum.LOW_PRODUCT.getCode().equals(orderInfo.getProductType())) {
            orderRemoteService.confirmEntityOrder(confirmDTO, orderInfo);
        }
        return orderInfo;
    }

    @Override
    public void pushAll(CustomerUser customerUser, OrderInfo orderInfo) {
        try {
            // 推送父订单和单独订单
            orderSyncAdapter.orderPushAll(orderInfo, Lists.newArrayList(orderInfo));

            try {
                if(org.apache.commons.collections4.CollectionUtils.isNotEmpty(orderInfo.getCardStrs())){
                    weChatMiniSmsManager.startUsingCard(orderInfo.getCardStrs(), customerUser.getWechartOpenId());
                }
            } catch (Exception e) {
                log.error("推送微信奶卡信息失败", e);
            }
            //订单上报有数
            milkProducerService.syncOrderToYoushu(orderInfo.getOrderNo());
        } catch (Exception e) {
            log.error("推送订单信息失败", e);
        }
    }

    private void verifyBlacklist(CustomerUser customerUser) {
        if(CustomerUserStatusEnum.DISABLE.getCode().equals(customerUser.getStatus())){
            throw new BusinessException("账户异常");
        }
    }

    /**
     * 获取周期购售价
     * @param orderSkuCreateV2DTO
     * @param skuBaseDTO
     * @return
     */
    private static BigDecimal getCycleSalePrice(OrderSkuCreateV2DTO orderSkuCreateV2DTO, SkuBaseDTO skuBaseDTO) {
        BigDecimal cycleSalePrice = BigDecimal.ZERO;
        if(BasicFlagEnum.YES.getKey().equals(orderSkuCreateV2DTO.getCycleFlag())){
            List<SkuAttrDTO> skuAttrList = skuBaseDTO.getSkuAttrs().stream().filter(skuAttr -> "times".equals(skuAttr.getAttrName())
                    && orderSkuCreateV2DTO.getOrderCycleCreateDTO().getTimes().equals(Integer.valueOf(skuAttr.getAttrValue()))).collect(Collectors.toList());
            if(CollectionUtils.isEmpty(skuAttrList)){
                throw new BusinessException("参数有误!");
            }else{
                cycleSalePrice = new BigDecimal(StreamUtils.getFirst(skuAttrList).getAttrSpecValue())
                        .multiply(new BigDecimal(StreamUtils.getFirst(skuAttrList).getAttrValue()))
                        .multiply(new BigDecimal(String.valueOf(orderSkuCreateV2DTO.getOrderCycleCreateDTO().getMilkAmount())));
            }
        }
        return cycleSalePrice;
    }

    @Override
    public  ShopCartCouponVO getShopCartCouponVO(CustomerUserVO user, CouponInfo couponInfo, List<Long> shopCartIds,
                                                 Map<Long, OrderSkuCreateV2DTO> orderSkuCreateV2DTOMap, Map<Long, ProductBaseDTO> baseDTOMap) {
        List<ShopCart> shopCarts = shopCartDao.findByIds(shopCartIds, user.getId());
        List<ShopCartItemInfoDTO> itemInfoDTOList = StreamUtils.toList(shopCarts, shopCart -> {
            return ShopCartUtil.convert2DTO(shopCart, orderSkuCreateV2DTOMap.get(shopCart.getId()), baseDTOMap.get(shopCart.getProductId()));
        });
        CouponRule couponRule = couponRuleManager.findById(couponInfo.getRuleId());
        CouponRuleSimpleDTO couponRuleSimpleDTO = CouponUtil.convert2DTO(couponRule);
        List<CouponRange> couponRanges = couponRangeDao.queryByRuleId(couponInfo.getRuleId());
        ShopCartCouponVO shopCartCouponVO = couponRuleManager.getCouponItemDetailPrice(itemInfoDTOList, couponRuleSimpleDTO, couponRanges);
        return shopCartCouponVO;
    }


    @Override
    public void confirmOrder(OrderConfirmDTO dto) {
        for (int i = 0; i < 3; i++) {
            OrderInfo orderInfo = orderManager.selectByOrderNo(dto.getOrderNo());
            OrderStatusEnum orderStatusEnum = OrderStatusEnum.getEnum(orderInfo.getOrderStatus());
            if(OrderStatusEnum.PAYMENT == orderStatusEnum){
                try {
                    Thread.sleep(500L);
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                }
            }
        }
    }

    @Override
    public void checkStock(StockCalculateDTO stockCalculateDTO) {
        List<Long> skuIds = stockManager.checkStock(Collections.singletonList(stockCalculateDTO));
        if (CollectionUtils.isNotEmpty(skuIds)) {
            throw new BusinessException("商品库存不足！");
        }
    }

    @Override
    public void checkStockStatus(StockCalculateDTO stockCalculateDTO) {
        CustomerUserVO user = Objects.requireNonNull(UserUtil.getDetails(), "请登录后重试！");
        if(Objects.isNull(stockCalculateDTO.getSkuId())){
            throw new BusinessException("skuId不能为空");
        }
        SkuBaseDTO skuBaseDTO = skuManager.detail(stockCalculateDTO.getSkuId(), false);
        if(skuBaseDTO == null){
            throw new BusinessException("商品不存在！");
        }
        Product product = productManager.getOne(skuBaseDTO.getProductId());
        if(product == null){
            throw new BusinessException("商品不存在！");
        }
        if(ProductSaleStatusEnum.NEW.getCode().equals(product.getSaleStatus())){
            throw new BusinessException("商品未上架！");
        }
        // 不需要库存校验
        if(!ProductTypeEnum.needStockByFlag(product.getProductType(), stockCalculateDTO.getCycleFlag())){
            return;
        }
        if(Objects.isNull((stockCalculateDTO.getStockNum()))){
            throw new BusinessException("库存数量不能为空");
        }
        SkuDiscountActivityDTO skuDiscountActivityDTO = shopCartService.getSkuDiscountPrice(stockCalculateDTO.getSkuId(), user);
        if(Objects.nonNull(stockCalculateDTO.getPtActivityId())){
            if (!ptActivityDao.checkStock(stockCalculateDTO.getPtActivityId(), stockCalculateDTO.getStockNum())) {
                throw new BusinessException("拼团活动库存不足！");
            }
        }else if(Objects.nonNull(skuDiscountActivityDTO)){
            if(!discountActivityRangeDao.checkStock(skuDiscountActivityDTO.getId(), stockCalculateDTO.getSkuId(), stockCalculateDTO.getStockNum())){
                throw new BusinessException("活动库存不足！");
            }
        }else {
            List<Long> skuIds = stockManager.checkStock(Collections.singletonList(stockCalculateDTO));
            if (CollectionUtils.isNotEmpty(skuIds)) {
                throw new BusinessException("商品库存不足！");
            }
        }
    }

    @Override
    public void sign(String orderNo) {
        CustomerUserVO customerUserVO = Objects.requireNonNull(UserUtil.getDetails(), "登录后重试");
        OrderInfo orderInfo = orderManager.selectByOrderNo(orderNo);
        if (Objects.isNull(orderInfo)) {
            throw new BusinessException("订单不存在!");
        }
        if(!Objects.equals(orderInfo.getUserId(), customerUserVO.getId())){
            throw new BusinessException("这不是你的订单！！！");
        }
        orderManager.sign(orderInfo, false);
        // 订单同步南讯
        milkProducerService.syncOrderToNascent(orderInfo.getId());
        // 订单上报腾讯有数
        milkProducerService.syncOrderToYoushu(orderInfo.getOrderNo());
    }

    @Override
    public PageVO<OrderInfoVO> list(OrderSearchDTO dto) {
        dto.setProductType(CardTypeEnum.ELECTRONIC_CARD.getCode());
        dto.setOrderType(OrderTypeEnum.CARD_ORDER.getCode());
        dto.setDelflag(DeleteFlagEnum.IS_NOT_DELETE.getCode());
        dto.setChannel(OrderChannelEnum.LPK.getCode());
        return orderManager.eCardList(dto);
    }

    @Override
    public PageVO<OrderInfoVO> listAll(OrderSearchDTO dto) {
//        dto.setProductTypeList(Arrays.asList(OrderProductTypeEnum.VIRTUAL_CARD.getCode(),OrderProductTypeEnum.PHYSICAL_CARD.getCode(),OrderProductTypeEnum.SINGLE_PRODUCT.getCode()));
        dto.setOrderTypeList(dto.getOrderTypeList());
        dto.setDelflag(DeleteFlagEnum.IS_NOT_DELETE.getCode());
        dto.setChannel(OrderChannelEnum.LPK.getCode());
        return orderManager.eCardList(dto);
    }

    @Override
    public PageVO<OrderInfoV2VO> listAllV2(OrderSearchDTO dto) {
        dto.setOrderTypeList(dto.getOrderTypeList());
        dto.setDelflag(DeleteFlagEnum.IS_NOT_DELETE.getCode());
        dto.setChannel(OrderChannelEnum.LPK.getCode());
        return orderV2Manager.eCardList(dto);
    }

    @Override
    public OrderInfoVO detailExpress(String orderNo) {
        CustomerUserVO customerUserVO = Objects.requireNonNull(UserUtil.getDetails(), "登录后重试");
        OrderInfoVO orderInfoVO = new OrderInfoVO();
        OrderInfo orderInfo = orderManager.selectByOrderNo(orderNo);
        if (Objects.isNull(orderInfo)) {
            throw new BusinessException("订单不存在!");
        }
        if(!Objects.equals(orderInfo.getUserId(), customerUserVO.getId())){
            throw new BusinessException("这不是你的订单！！！");
        }
        BeanUtils.copy(orderInfo, orderInfoVO);

        List<OrderSku> orderSku = orderSkuManager.selectByOrderNo(orderNo);
        OrderAddress orderAddress = orderAddressManager.selectByOrderNo(orderNo);
        if(Objects.nonNull(orderAddress)){
            orderInfoVO.setAddressDetail(orderAddress.getProvince() + orderAddress.getCity() + orderAddress.getDistrict() + orderAddress.getAddress());
            orderInfoVO.setCity(orderAddress.getCity());
            orderInfoVO.setDistrict(orderAddress.getDistrict());
            orderInfoVO.setProvince(orderAddress.getProvince());
            orderInfoVO.setReceiverName(orderAddress.getReceiverName());
            orderInfoVO.setReceiverPhone(orderAddress.getReceiverPhone());
        }
        orderInfoVO.setProductName(orderSku.get(0).getProductName());
        orderInfoVO.setSkuDesc(orderSku.get(0).getSkuDesc());
        orderInfoVO.setSkuPicUrl(orderSku.get(0).getSkuPicUrl());
        orderInfoVO.setCount(orderSku.stream().mapToInt(OrderSku::getCount).sum());
        if(Objects.nonNull(orderInfo.getDeliveryTime())){
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(orderInfo.getDeliveryTime());
            orderInfoVO.setDeliveryTime(DateUtil.dateToString(orderInfo.getDeliveryTime(),"yyyy年MM-dd")
                    + "星期" + OrderInfoUtil.switchWeekDesc(calendar.get(Calendar.DAY_OF_WEEK), true));
        }

        List<OrderPackage> packageList = orderManager.selectOrderPackageByOrderNo(orderNo);
        orderInfoVO.setTrackingList(packageList);
        return orderInfoVO;
    }

    @Override
    public OrderInfoV2VO detailExpressV2(String orderNo) {
        CustomerUserVO customerUserVO = Objects.requireNonNull(UserUtil.getDetails(), "登录后重试");
        OrderInfo orderInfo = orderManager.selectByOrderNo(orderNo);
        Assert.notNull(orderInfo, "订单不存在");
        if(!Objects.equals(orderInfo.getUserId(), customerUserVO.getId())){
            throw new BusinessException("这不是你的订单！！！");
        }
        return orderV2Manager.detailExpress(orderNo);
    }

    @Override
    public OrderInfoV2VO oneV2(String orderNo) {
        CustomerUserVO customerUserVO = Objects.requireNonNull(UserUtil.getDetails(), "登录后重试");
        OrderInfo orderInfo = orderManager.selectByOrderNo(orderNo);
        Assert.notNull(orderInfo, "订单不存在");
        if(!Objects.equals(orderInfo.getUserId(), customerUserVO.getId())){
            throw new BusinessException("这不是你的订单！！！");
        }
        return orderV2Manager.one(orderNo);
    }

    @Override
    public OrderInfoV2VO oneV2ById(Long id) {
        CustomerUserVO customerUserVO = Objects.requireNonNull(UserUtil.getDetails(), "登录后重试");
        OrderInfo orderInfo = orderManager.findById(id);
        Assert.notNull(orderInfo, "订单不存在");
        if(!Objects.equals(orderInfo.getUserId(), customerUserVO.getId())){
            throw new BusinessException("这不是你的订单！！！");
        }
        return orderV2Manager.one(orderInfo.getOrderNo());
    }


    private String checkProduct(Long productId, Long skuId) {
        // Product product = productDao.getOne(productId);
        // Sku sku = skuDao.getOne(skuId);
        // if (Objects.isNull(product) || DeleteFlagEnum.IS_DELETE.getCode().equals(product.getDelflag()) ||
        //         ProductSaleStatusEnum.NEW.getCode().equals(product.getSaleStatus()) ||
        //         Objects.isNull(sku) || DeleteFlagEnum.IS_DELETE.getCode().equals(sku.getDelflag())) {
        //     return "error";
        // } else {
        return "success";
        // }
    }

    public static void main(String[] args) {
        List<ShopCart> shopCarts = new ArrayList<>();
        ShopCart sp1 = new ShopCart();
        sp1.setId(1L);
        sp1.setBelongMark("mmm");
//        sp1.setBelongTime(DateUtil.stringToDate("2025-02-03", DateUtil.SIMPLE_YMD));
        sp1.setBelongTime(null);

        ShopCart sp2 = new ShopCart();
        sp2.setId(2L);
        sp2.setBelongMark("");
        sp2.setBelongTime(null);

        ShopCart sp3 = new ShopCart();
        sp3.setId(3L);
        sp3.setBelongMark("hhh");
        sp3.setBelongTime(DateUtil.stringToDate("2024-12-03", DateUtil.SIMPLE_YMD));


        ShopCart sp4 = new ShopCart();
        sp4.setId(4L);
        sp4.setBelongMark("lll");
        sp4.setBelongTime(DateUtil.stringToDate("2021-01-01", DateUtil.SIMPLE_YMD));
        shopCarts.add(sp1);
        shopCarts.add(sp2);
        shopCarts.add(sp3);
        shopCarts.add(sp4);

        ShopCart shopCart = StreamUtils.sortFindFirst(shopCarts, x -> StringUtils.isNotBlank(x.getBelongMark()) && Objects.nonNull(x.getBelongTime()), Comparator.comparing(ShopCart::getBelongTime).reversed());
        System.out.println(JSON.toJSONString(shopCart));
    }
}
