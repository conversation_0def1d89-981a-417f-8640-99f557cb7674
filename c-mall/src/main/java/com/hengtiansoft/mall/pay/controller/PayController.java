package com.hengtiansoft.mall.pay.controller;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.hengtiansoft.common.entity.vo.Response;
import com.hengtiansoft.common.factory.ResponseFactory;
import com.hengtiansoft.mall.pay.service.PayService;
import com.hengtiansoft.pay.entity.dto.WeChatPayDTO;
import com.hengtiansoft.pay.entity.vo.RequestPaymentVO;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 微信支付回调controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("wechat")
@Api(tags = "支付相关接口 陈晓思")
public class PayController {

    @Resource
    private PayService payService;


    @ApiOperation(value = "小程序微信支付下单")
    @PostMapping(value = "/miniProgram")
    public Response<RequestPaymentVO> weChatMiniProgram(@RequestBody @Validated WeChatPayDTO weChatPayDTO, HttpServletRequest request) {
        return ResponseFactory.success(payService.weChatMiniProgram(weChatPayDTO, request));
    }


}
