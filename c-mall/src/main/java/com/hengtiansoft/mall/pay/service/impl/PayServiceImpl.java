package com.hengtiansoft.mall.pay.service.impl;

import com.google.common.collect.Lists;
import com.hengtiansoft.common.entity.exception.BusinessException;
import com.hengtiansoft.common.enumeration.FlagEnum;
import com.hengtiansoft.common.util.NumberOptUtil;
import com.hengtiansoft.common.util.StreamUtils;
import com.hengtiansoft.item.entity.dto.SkuBaseDTO;
import com.hengtiansoft.item.entity.dto.SkuDiscountActivityDTO;
import com.hengtiansoft.item.entity.po.Product;
import com.hengtiansoft.item.interfaces.ProductManager;
import com.hengtiansoft.item.interfaces.SkuManager;
import com.hengtiansoft.mall.pay.service.PayService;
import com.hengtiansoft.mall.shop.cart.service.ShopCartService;
import com.hengtiansoft.order.dao.OrderInfoDao;
import com.hengtiansoft.order.dao.OrderSkuDao;
import com.hengtiansoft.order.entity.po.OrderInfo;
import com.hengtiansoft.order.entity.po.OrderSku;
import com.hengtiansoft.order.enums.OrderCountTypeEnum;
import com.hengtiansoft.order.enums.OrderParentTypeEnum;
import com.hengtiansoft.order.enums.OrderSourceTypeEnum;
import com.hengtiansoft.order.enums.OrderTypeV2Enum;
import com.hengtiansoft.order.manager.OrderManager;
import com.hengtiansoft.pay.entity.dto.WeChatPayDTO;
import com.hengtiansoft.pay.entity.vo.RequestPaymentVO;
import com.hengtiansoft.pay.interfaces.PayManager;
import com.hengtiansoft.security.util.UserUtil;
import com.hengtiansoft.user.entity.vo.CustomerUserVO;
import com.hengtiansoft.user.manager.CustomerUserManager;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 微信支付service
 *
 * <AUTHOR>
 * @since 17.04.2020
 */
@Service
public class PayServiceImpl implements PayService {

    @Resource
    private PayManager payManager;
    @Resource
    private CustomerUserManager customerUserManager;
    @Resource
    private OrderInfoDao orderInfoDao;
    @Resource
    private OrderSkuDao orderSkuDao;
    @Resource
    private ProductManager productManager;
    @Resource
    private SkuManager skuManager;
    @Resource
    private OrderManager orderManager;
    @Resource
    private ShopCartService shopCartService;

    @Override
    public RequestPaymentVO weChatMiniProgram(WeChatPayDTO weChatPayDTO, HttpServletRequest request) {
        CustomerUserVO customerUser = UserUtil.getDetails();
        if(Objects.isNull(customerUser) || StringUtils.isBlank(customerUser.getOpenId())){
            throw new BusinessException("用户信息获取有误，请退出重新登录！");
        }
        OrderInfo orderInfo = orderInfoDao.findByOrderNo(weChatPayDTO.getOutTradeNo());
        long totalFee = orderInfo.getRealAmount().multiply(BigDecimal.valueOf(100)).longValue();
        weChatPayDTO.setTotalFee(totalFee);
        weChatPayDTO.setOpenId(customerUser.getOpenId());

        //用户黑名单校验
        customerUserManager.verifyBlacklist(customerUser.getId());

        OrderParentTypeEnum parentTypeEnum = OrderParentTypeEnum.getEnum(orderInfo.getParentType());
        List<OrderInfo> childOrderInfos;
        if(OrderParentTypeEnum.SIGNAL.equals(parentTypeEnum)){
            childOrderInfos = Lists.newArrayList(orderInfo);
        }else if(OrderParentTypeEnum.PARENT.equals(parentTypeEnum)){
            childOrderInfos = orderInfoDao.findByOrderParentNo(orderInfo.getOrderNo());
        }else{
            throw new BusinessException("订单类型有误");
        }
        //拼团订单不做限购校验
        if(!OrderSourceTypeEnum.PIN_TUAN.getCode().equals(orderInfo.getSourceType())){
            List<OrderSku> orderSkuList = orderSkuDao.findByOrderNoList(StreamUtils.toList(childOrderInfos, OrderInfo::getOrderNo));
            Map<String, List<OrderSku>> orderSkuMap = StreamUtils.group(orderSkuList, OrderSku::getOrderNo);
            List<Product> productList = productManager.findByIds(StreamUtils.toList(orderSkuList, OrderSku::getProductId));
            Map<Long, Product> baseDTOMap = StreamUtils.toMap(productList, Product::getId);

            for (OrderInfo childOrderInfo : childOrderInfos) {
                if(Objects.equals(FlagEnum.YES.getCode(), childOrderInfo.getIsGift())){
                    continue;
                }
                OrderTypeV2Enum typeV2Enum = OrderTypeV2Enum.getEnum(childOrderInfo);
                if(typeV2Enum == OrderTypeV2Enum.CYLE_ORDER){
                    continue;
                }
                if(Objects.equals(childOrderInfo.getSourceType(), OrderSourceTypeEnum.FULL_TRIAL.getCode())
                        || Objects.equals(childOrderInfo.getSourceType(), OrderSourceTypeEnum.FREE_TASTE.getCode())) {
                    continue;
                }
                List<OrderSku> orderSkus = orderSkuMap.get(childOrderInfo.getOrderNo());
                for (OrderSku orderSku : orderSkus) {
                    SkuDiscountActivityDTO skuDiscountActivityDTO = shopCartService.getSkuDiscountPrice(orderSku.getSkuId(), customerUser);
                    if(Objects.nonNull(skuDiscountActivityDTO)){
                        continue;
                    }
                    SkuBaseDTO skuBaseDTO = skuManager.detail(orderSku.getSkuId());
                    Product baseDTO = baseDTOMap.get(orderSku.getProductId());
                    // spu限购校验:1.统计所有相同spu的数量 2.查询此spu，该用户的历史下单数量 3.比较是否超过spu限购数量
                    if(Objects.nonNull(baseDTO.getPurchase())){
                        int sumSkuCount = orderSkuList.stream().filter(x -> x.getProductId().equals(orderSku.getProductId()))
                                .mapToInt(OrderSku::getCount).sum();
                        Integer orderCount = orderManager.getOrderCount(childOrderInfo.getUserId(), OrderCountTypeEnum.SPU, orderSku.getProductId());
                        if (sumSkuCount + orderCount  > baseDTO.getPurchase()) {
                            throw new BusinessException("超出限购数量:" + baseDTO.getProductName());
                        }
                    }
                    // sku限购校验
                    Integer skuPurchase = NumberOptUtil.toInt(skuBaseDTO.skuAttrMap().get("purchase"), null);
                    if(Objects.nonNull(skuPurchase)){
                        int sumSkuCount = orderSku.getCount();
                        Integer orderCount = orderManager.getOrderCount(childOrderInfo.getUserId(), OrderCountTypeEnum.SKU, orderSku.getSkuId());
                        if (sumSkuCount + orderCount  > skuPurchase) {
                            throw new BusinessException("超出限购数量:" + baseDTO.getProductName());
                        }
                    }
                }
            }
        }
        return payManager.weChatPayMiniProgram(weChatPayDTO, request);
    }

}
