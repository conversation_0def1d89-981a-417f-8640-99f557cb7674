package com.hengtiansoft.common.json;

import com.alibaba.fastjson.support.spring.FastJsonHttpMessageConverter;
import org.springframework.http.MediaType;

/**
 * 自定义FastJsonHttpMessageConverter，跳过xml处理
 */
public class CustomFastJsonHttpMessageConverter extends FastJsonHttpMessageConverter {

    @Override
    protected boolean canRead(MediaType mediaType) {
        // 不处理XML
        if (MediaType.APPLICATION_XML.includes(mediaType)) {
            return false;
        }
        return super.canRead(mediaType);
    }

    @Override
    protected boolean canWrite(MediaType mediaType) {
        if(!MediaType.APPLICATION_JSON.includes(mediaType)){
            return false;
        }
        return super.canWrite(mediaType);
    }
}
