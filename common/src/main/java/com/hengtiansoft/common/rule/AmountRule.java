package com.hengtiansoft.common.rule;

import com.hengtiansoft.common.enumeration.LaConditionEnum;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class AmountRule implements IRule<AmountRule>{
    private LaConditionEnum laConditionEnum = LaConditionEnum.AMOUNT;
    private Date timeStart;
    private Date timeEnd;
    private BigDecimal amountMin;
    private BigDecimal amountMax;

    @Override
    public AmountRule getParm() {
        AmountRule dealRule = new AmountRule();
        return null;
    }
    @Override
    public LaConditionEnum getCondition(){
        return this.laConditionEnum;
    }
}
