package com.hengtiansoft.common.rule;

import com.hengtiansoft.common.enumeration.LaConditionEnum;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class AverageRule implements IRule<AverageRule>{
    private LaConditionEnum laConditionEnum = LaConditionEnum.AVERAGE;
    private Date timeStart;
    private Date timeEnd;
    private BigDecimal averageAmountMin;
    private BigDecimal averageAmountMax;

    @Override
    public AverageRule getParm() {
        AverageRule dealRule = new AverageRule();
        return null;
    }
    @Override
    public LaConditionEnum getCondition(){
        return this.laConditionEnum;
    }
}
