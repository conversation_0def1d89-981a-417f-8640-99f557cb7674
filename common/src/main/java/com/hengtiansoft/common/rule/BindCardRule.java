package com.hengtiansoft.common.rule;

import com.hengtiansoft.common.enumeration.PackageConditionEnum;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @program: milk-card-server
 * @description:
 * @author: haiyang
 * @create: 2024-08-30 13:46
 **/
@Data
public class BindCardRule implements IRule<BindBuyRule>{
    private Date timeStart;
    private Date timeEnd;

    private List<Long> cardCategoryIds;
    @Override
    public BindBuyRule getParm() {
        return null;
    }

    @Override
    public PackageConditionEnum getPackageConditionEnum() {
        return PackageConditionEnum.BIND_CARD;
    }
}
