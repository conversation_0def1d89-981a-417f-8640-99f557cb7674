package com.hengtiansoft.common.rule;

import com.hengtiansoft.common.enumeration.PackageConditionEnum;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class BindCateRule implements IRule<BindCateRule>{
    private PackageConditionEnum packageConditionEnum = PackageConditionEnum.BIND_CATE;
    private Integer bindCate;

    @Override
    public BindCateRule getParm() {
        BindCateRule dealRule = new BindCateRule();
        return null;
    }
    @Override
    public PackageConditionEnum getPackageConditionEnum(){
        return this.packageConditionEnum;
    }
}
