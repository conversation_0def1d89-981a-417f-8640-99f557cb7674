package com.hengtiansoft.common.rule;

import com.hengtiansoft.common.enumeration.PackageConditionEnum;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 购买奶卡
 * <AUTHOR>
 */
@Data
public class BuyCardRule implements IRule<BuyCardRule>{
    private PackageConditionEnum packageConditionEnum = PackageConditionEnum.BUY_CARD;
    private Integer buyCard;

    private Date timeStart;
    private Date timeEnd;

    private List<Long> cardCategoryIds;

    @Override
    public BuyCardRule getParm() {
        BuyCardRule dealRule = new BuyCardRule();
        return null;
    }
    @Override
    public PackageConditionEnum getPackageConditionEnum(){
        return this.packageConditionEnum;
    }
}
