package com.hengtiansoft.common.rule;

import com.hengtiansoft.common.enumeration.PackageConditionEnum;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class BuyCateRule implements IRule<BuyCateRule>{
    private PackageConditionEnum packageConditionEnum = PackageConditionEnum.BUY_CATE;
    private Integer buyCate;

    @Override
    public BuyCateRule getParm() {
        BuyCateRule dealRule = new BuyCateRule();
        return null;
    }
    @Override
    public PackageConditionEnum getPackageConditionEnum(){
        return this.packageConditionEnum;
    }
}
