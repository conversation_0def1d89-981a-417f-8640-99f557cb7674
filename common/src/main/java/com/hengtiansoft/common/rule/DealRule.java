package com.hengtiansoft.common.rule;

import com.hengtiansoft.common.enumeration.PackageConditionEnum;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class DealRule implements IRule<DealRule>{
    private PackageConditionEnum packageConditionEnum = PackageConditionEnum.DEAL;
    private Date timeStart;
    private Date timeEnd;
    private Integer dealMin;
    private Integer dealMax;
    private BigDecimal amountMin;
    private BigDecimal amountMax;

    @Override
    public DealRule getParm() {
        DealRule dealRule = new DealRule();
        return null;
    }
    @Override
    public PackageConditionEnum getPackageConditionEnum(){
        return this.packageConditionEnum;
    }
}
