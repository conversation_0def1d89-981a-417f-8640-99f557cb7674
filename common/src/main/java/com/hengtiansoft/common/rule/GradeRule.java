package com.hengtiansoft.common.rule;

import com.hengtiansoft.common.enumeration.PackageConditionEnum;
import lombok.Data;

import java.util.List;

/**
 * 会员等级
 * <AUTHOR>
 */
@Data
public class GradeRule implements IRule<GradeRule>{
    private PackageConditionEnum packageConditionEnum = PackageConditionEnum.GRADE;
    private List<Integer> grades;

    @Override
    public GradeRule getParm() {
        GradeRule dealRule = new GradeRule();
        return null;
    }
    @Override
    public PackageConditionEnum getPackageConditionEnum(){
        return this.packageConditionEnum;
    }
}
