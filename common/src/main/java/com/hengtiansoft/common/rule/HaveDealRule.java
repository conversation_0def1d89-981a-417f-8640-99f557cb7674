package com.hengtiansoft.common.rule;

import com.hengtiansoft.common.enumeration.LaConditionEnum;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class HaveDealRule implements IRule<HaveDealRule>{
    private LaConditionEnum laConditionEnum = LaConditionEnum.HAVE_DEAL;
    private Date timeStart;
    private Date timeEnd;

    @Override
    public HaveDealRule getParm() {
        HaveDealRule dealRule = new HaveDealRule();
        return null;
    }
    @Override
    public LaConditionEnum getCondition(){
        return this.laConditionEnum;
    }
}
