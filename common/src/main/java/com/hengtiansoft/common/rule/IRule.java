package com.hengtiansoft.common.rule;


import com.hengtiansoft.common.enumeration.LaActivityTypeEnum;
import com.hengtiansoft.common.enumeration.LaConditionEnum;
import com.hengtiansoft.common.enumeration.PackageConditionEnum;

/**
 * <AUTHOR>
 */
public interface IRule<T> {
    T getParm();

    default LaConditionEnum getCondition(){return null;}

    default PackageConditionEnum getPackageConditionEnum(){
        return null;
    }

    default LaActivityTypeEnum getLaActivityTypeEnum(){
        return null;
    }
}
