package com.hengtiansoft.common.rule;

import com.hengtiansoft.common.enumeration.PackageConditionEnum;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class LabelRule implements IRule<LabelRule>{
    private PackageConditionEnum packageConditionEnum = PackageConditionEnum.LABEL;
    private List<Long> labelIds;

    @Override
    public LabelRule getParm() {
        LabelRule dealRule = new LabelRule();
        return null;
    }
    @Override
    public PackageConditionEnum getPackageConditionEnum(){
        return this.packageConditionEnum;
    }
}
