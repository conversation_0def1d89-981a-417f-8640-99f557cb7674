package com.hengtiansoft.common.rule;

import com.hengtiansoft.common.enumeration.LaConditionEnum;
import com.hengtiansoft.common.enumeration.PackageConditionEnum;
import lombok.Data;

import java.util.Date;

/**
 * 积分
 * <AUTHOR>
 */
@Data
public class PointRule implements IRule<PointRule>{
    private LaConditionEnum laConditionEnum = LaConditionEnum.POINT;
    private PackageConditionEnum packageConditionEnum = PackageConditionEnum.POINT;
    private Date timeStart;
    private Date timeEnd;
    private Integer pointMin;
    private Integer pointMax;

    @Override
    public PointRule getParm() {
        PointRule dealRule = new PointRule();
        return null;
    }
    @Override
    public LaConditionEnum getCondition(){
        return this.laConditionEnum;
    }
}
