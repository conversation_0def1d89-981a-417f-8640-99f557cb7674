package com.hengtiansoft.common.rule;

import com.hengtiansoft.common.enumeration.PackageConditionEnum;
import lombok.Data;

import java.util.Date;

/**
 * 店铺有购买
 * <AUTHOR>
 */
@Data
public class ShopBuyRule implements IRule<ShopBuyRule>{
    private PackageConditionEnum packageConditionEnum = PackageConditionEnum.SHOP_BUY;
    private Date timeStart;
    private Date timeEnd;

    @Override
    public ShopBuyRule getParm() {
        ShopBuyRule dealRule = new ShopBuyRule();
        return null;
    }
    @Override
    public PackageConditionEnum getPackageConditionEnum(){
        return this.packageConditionEnum;
    }
}
