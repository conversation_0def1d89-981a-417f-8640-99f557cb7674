/*
 * Project Name: yum-sample
 * File Name: DateUtil.java
 * Class Name: DateUtil
 *
 * Copyright 2018 Hengtian Software Inc
 *
 * Licensed under the Hengtiansoft
 *
 * http://www.hengtiansoft.com
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or
 * implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.hengtiansoft.common.util;

import com.hengtiansoft.common.entity.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.commons.lang3.time.FastDateFormat;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * Description: 时间工具类
 *
 * <AUTHOR> weiyangzhu
 */
@Slf4j
public final class DateUtil {

    public static final String SIMPLE_FMT = "yyyy-MM-dd HH:mm:ss";
    public static final String SIMPLE_FMT_MINUTE = "yyyy-MM-dd HH:mm";
    public static final String SIMPLE_YMD = "yyyy-MM-dd";
    public static final String SIMPLE_FMT_ZH = "yyyy年MM月dd日";
    public static final String SIMPLE_FMT_M = "yyyy年MM月";
    public static final String SIMPLE_DATE_YMD = "yyyyMMdd";
    public static final String SIMPLE_MDY = "MM/dd/yyyy";
    public static final String DATE_PATTERN_FULL = "yyyyMMddHHmmss";
    public static final String DATE_PATTERN_YEAR = "yyMMddHHmmss";
    public static final String SIMPLE_FMT_NOSPACE = "yyyyMMddHHmm";
    public static final String SIMPLE_FMT_SSS = "yyyy-MM-dd HH:mm:ss.SSS";
    public static final String SIMPLE_FMT_SSSZ = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'";
    public static final String SIMPLE_FMT_TIMEZONE = "yyyy-MM-dd'T'HH:mm:ss'+08:00'";
    public static final String SIMPLE_FMT_MONTH = "yyyyMM";
    public static final String SIMPLE_YM = "yyyy-MM";
    public static final String SIMPLE_YMD_SPLIT_VIRGULE = "yyyy/MM/dd";
    public static final String SIMPLE_FMT_SPLIT_VIRGULE = "yyyy/MM/dd HH:mm";
    public static final String SIMPLE_FMT_FULL_ZH = "yyyy年MM月dd日 HH:mm:ss";
    public static final String SIMPLE_FMT_SPLIT_VIRGULE_SIMPLE = "M/d/yyyy h:m";
    public static final String SIMPLE_FMT_SPLIT_SIMPLE = "M/d/yyyy";

    public static final ZoneId ZONE_ID = ZoneId.systemDefault();

    public static final int DEFAULT_END_HOUR = 23;
    public static final int DEFAULT_END_MINUTE = 59;
    public static final int DEFAULT_END_SENCEND = 59;
    public static final int DEFAULT_END_MIN_SENCEND = 000;
    public static final int DEFAULT_MONTH_OF_YEAR = 12;

    private static final Logger LOGGER = LoggerFactory.getLogger(DateUtil.class);

    private DateUtil() {

    }

    /**
     * 时间转换Date to String
     *
     * @param date    日期
     * @param sFormat 对应日期格式
     * @return 字符串形式的日期时间
     */
    public static String dateToString(Date date, String sFormat) {
        return date == null || StringUtils.isEmpty(sFormat) ? null : FastDateFormat.getInstance(sFormat).format(date);
    }

    /**
     * 时间转换String to Date
     *
     * @param sDate   源字符串
     * @param sFormat 对应日期格式
     * @return Date类型
     */
    public static Date stringToDate(String sDate, String sFormat) {
        try {
            FastDateFormat fastDateFormat = FastDateFormat.getInstance(sFormat);
            return fastDateFormat.parse(sDate);
        } catch (ParseException e) {
            LOGGER.error("Parse date exception: {}", e);
            throw new RuntimeException(e); // NOSONAR, convert exception to runtimeexception
        }
    }

    /**
     * 传入TimeStamp类型日期转换为String类型
     *
     * @param timestamp 源timestamp
     * @param sFormat   对应日期格式
     * @return 格式化后的时间戳
     */
    public static String timeStampToString(Timestamp timestamp, String sFormat) {
        return new SimpleDateFormat(sFormat).format(timestamp);
    }

    /**
     * 传入String类型日期转换为Timestamp类型
     *
     * @param sTimestamp sTimestamp
     * @param sFormat    对应日期格式
     * @return 时间戳类型
     */
    public static Timestamp timeStampParse(String sTimestamp, String sFormat) {
        return Timestamp.valueOf(dateToString(stringToDate(sTimestamp, sFormat), sFormat));
    }

    /**
     * 获取指定日期几天后的日期
     */
    public static String getDateAfterDays(String dateStr, Integer days, String strFormat) {
        if (StringUtils.isEmpty(dateStr)) {
            return null;
        }
        Calendar calendar = Calendar.getInstance();// 日历对象
        calendar.setTime(stringToDate(dateStr, strFormat));
        calendar.add(Calendar.DATE, days);
        return dateToString(calendar.getTime(), strFormat);// 输出格式化的日期
    }

    /**
     * 获取指定日期几年后的日期
     */

    public static Date getDateAfterYears(Date date, Integer years) {
        if (date == null) {
            return null;
        }
        Calendar calendar = Calendar.getInstance();// 日历对象
        calendar.setTime(date);
        calendar.add(Calendar.YEAR, years);
        return calendar.getTime();
    }

    /**
     * 获取指定日期几天后的日期
     */

    public static Date getDateAfterDays(Date date, Integer days) {
        if (date == null) {
            return null;
        }
        Calendar calendar = Calendar.getInstance();// 日历对象
        calendar.setTime(date);
        calendar.add(Calendar.DATE, days);
        return calendar.getTime();
    }

    /**
     * 获取指定日期几小时后的日期
     */

    public static Date getDateAfterHours(Date date, Integer hours) {
        if (date == null) {
            return null;
        }
        Calendar calendar = Calendar.getInstance();// 日历对象
        calendar.setTime(date);
        calendar.add(Calendar.HOUR, hours);
        return calendar.getTime();
    }

    /**
     * 获取指定日期几小时前的日期
     */

    public static Date getDateBeforeHours(Date date, Integer hours) {
        if (date == null) {
            return null;
        }
        Calendar calendar = Calendar.getInstance();// 日历对象
        calendar.setTime(date);
        calendar.add(Calendar.HOUR, -hours);
        return calendar.getTime();
    }

    /**
     * 将日期转换为中文字符串
     *
     * @param date 日期
     * @return 中文日期字符串
     */
    public static String parseDateToMString(Date date) {
        return dateToString(date, SIMPLE_FMT_M);
    }

    /**
     * 将日期转换为中文字符串
     *
     * @param date 日期
     * @return 中文日期字符串
     */
    public static String parseDateToZHString(Date date) {
        return dateToString(date, SIMPLE_FMT_ZH);
    }

    /**
     * 将yyyy-MM-dd转化为Date
     *
     * @param s 日期字符串
     * @return Date类型
     */
    public static Date timeStampParseYear(String s) {
        return stringToDate(s, SIMPLE_YMD);
    }

    /**
     * 将时间新增或减少对应月份
     *
     * @param dt     时间
     * @param amount 减少月份数
     * @return Date类型
     */
    public static Date subMonth(Date dt, int amount) {
        if (dt == null) {
            return null;
        }
        Calendar rightNow = Calendar.getInstance();
        rightNow.setTime(dt);
        rightNow.add(Calendar.MONTH, amount);
        return rightNow.getTime();
    }

    /**
     * 将时间新增或减少对应日期
     *
     * @param dt     时间
     * @param amount 减少天数
     * @return Date类型
     */
    public static Date subDay(Date dt, int amount) {
        if (dt == null) {
            return null;
        }
        Calendar rightNow = Calendar.getInstance();
        rightNow.setTime(dt);
        rightNow.add(Calendar.DATE, amount);
        return rightNow.getTime();
    }

    /**
     * @return 当前时间的 yyyyMMddHHmmss str
     */
    public static String getCurrentTimeStr() {
        return dateToString(new Date(), DATE_PATTERN_FULL);
    }

    /**
     * 日期转毫秒数
     *
     * @param date date
     * @return 毫秒数
     */
    public static Long getTimeInMillis(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return calendar.getTimeInMillis();
    }

    /**
     * 判断两个日期是否是同一个月
     *
     * @param date1
     * @param date2
     * @return
     */
    public static boolean checkTheSameMonth(Date date1, Date date2) {
        if (date1 == null || date2 == null) {
            return false;
        }
        Calendar c1 = Calendar.getInstance();
        c1.setTime(date1);
        Calendar c2 = Calendar.getInstance();
        c2.setTime(date2);
        return c1.get(Calendar.YEAR) == c2.get(Calendar.YEAR) && c1.get(Calendar.MONTH) == c2.get(Calendar.MONTH);
    }

    /**
     * 返回某月第一天
     *
     * @param date
     * @return
     */
    public static Date getFirstDayOfMonth(Date date) {
        if (date == null) {
            return null;
        }
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        c.set(Calendar.DAY_OF_MONTH, 1);
        c.set(Calendar.HOUR_OF_DAY, 0);
        c.set(Calendar.MINUTE, 0);
        c.set(Calendar.SECOND, 0);
        c.set(Calendar.MILLISECOND, 0);
        return c.getTime();
    }

    /**
     * 返回某月最后一天
     *
     * @param date
     * @return
     */
    public static Date getLastDayOfMonth(Date date) {
        if (date == null) {
            return null;
        }
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        int lastDay = c.getActualMaximum(Calendar.DATE);
        c.set(Calendar.DAY_OF_MONTH, lastDay);
        c.set(Calendar.HOUR_OF_DAY, 23);
        c.set(Calendar.MINUTE, 59);
        c.set(Calendar.SECOND, 59);
        c.set(Calendar.MILLISECOND, 999);
        return c.getTime();
    }

    /**
     * 按照给定格式返回某月第一天
     *
     * @param date
     * @param fm
     * @return
     */
    public static String getFirstDayOfMonth(Date date, String fm) {
        return new SimpleDateFormat(fm).format(getFirstDayOfMonth(date));
    }

    /**
     * 按照给定格式返回某月最后一天
     *
     * @param date
     * @param fm
     * @return
     */
    public static String getLastDayOfMonth(Date date, String fm) {
        return new SimpleDateFormat(fm).format(getLastDayOfMonth(date));
    }

    /**
     * Description: 智能格式化数据，如果时分秒为空，去除十分秒
     *
     * @param date
     * @return
     */
    public static String smartDateToString(Date date, String sFormat) {
        return dateToString(date, sFormat).replace(" 00:00:00", "");
    }

    /**
     * Description: 转换LocalDateTime 到Date
     *
     * @param ldt LocalDateTime
     * @return Date
     */
    public static Date localDateTimeToDate(LocalDateTime ldt) {
        if (ldt == null) {
            return null;
        }
        return Date.from(ldt.atZone(ZONE_ID).toInstant());
    }

    /**
     * Description: 转换Date 到LocalDateTime
     *
     * @param date
     * @return
     */
    public static LocalDateTime dateToLocalDateTime(Date date) {
        if (date == null) {
            return null;
        }
        return date.toInstant().atZone(ZONE_ID).toLocalDateTime();
    }

    /**
     * Description: 转换Date 到LocalDate
     *
     * @param date
     * @return
     */
    public static LocalDate dateToLocalDate(Date date) {
        if (date == null) {
            return null;
        }
        return date.toInstant().atZone(ZONE_ID).toLocalDate();
    }

    /**
     * Description: 转换LocalDate 到Date
     *
     * @param ld LocalDate
     * @return Date
     */
    public static Date localDateToDate(LocalDate ld) {
        if (ld == null) {
            return null;
        }
        return Date.from(ld.atStartOfDay().atZone(ZONE_ID).toInstant());
    }

    /**
     * Description: 取日期为一个月的第几天
     *
     * @param date
     * @return
     */
    public static int getDateOfMonth(Date date) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        return c.get(Calendar.DAY_OF_MONTH);
    }

    /**
     * 获取@date 的下一个工作日
     *
     * @param date     时间
     * @param holidays 节假日集合
     * @return 下个工作日
     */
    public static LocalDate getNextWorkDay(LocalDate date, List<LocalDate> holidays) {
        if (holidays == null) {
            holidays = new ArrayList<>();
        }
        if (date == null) {
            return null;
        }
        date = date.plusDays(1);
        while (date.getDayOfWeek() == DayOfWeek.SATURDAY || date.getDayOfWeek() == DayOfWeek.SUNDAY
                || holidays.indexOf(date) > -1) {
            date = date.plusDays(1);
        }
        return date;
    }

    /**
     * 如果当前时间是节假日，返回下一个工作日
     *
     * @param date     时间
     * @param holidays 节假日集合
     * @return 下个工作日
     */
    public static LocalDate getOpWorkDay(LocalDate date, List<LocalDate> holidays) {
        if (holidays == null) {
            holidays = new ArrayList<>();
        }
        if (date == null) {
            return null;
        }
        while (date.getDayOfWeek() == DayOfWeek.SATURDAY || date.getDayOfWeek() == DayOfWeek.SUNDAY
                || holidays.indexOf(date) > -1) {
            date = date.plusDays(1);
        }
        return date;
    }

    public static List<LocalDate> getPeriodDates(LocalDate preDate, LocalDate finalDate, List<LocalDate> dateList) {
        if (dateList == null) {
            dateList = new ArrayList<>();
        }
        if (preDate.compareTo(finalDate) > 0) {
            throw new IllegalArgumentException("参数异常");
        }
        while (preDate.compareTo(finalDate) <= 0) {
            dateList.add(preDate);
            preDate = preDate.plusDays(1);
        }
        return dateList;
    }

    public static LocalDate getCurrentSunDay(LocalDate date) {
        while (date.getDayOfWeek() != DayOfWeek.SUNDAY) {
            date = date.plusDays(1);
        }
        return date;
    }

    public static Long getDelay(Date preDate, Date finalDate) {
        if (preDate == null || finalDate == null) {
            return null;
        }
        LocalDate preLocalDate = preDate.toInstant().atZone(ZONE_ID).toLocalDate();
        LocalDate finalLocalDate = finalDate.toInstant().atZone(ZONE_ID).toLocalDate();
        return finalLocalDate.toEpochDay() - preLocalDate.toEpochDay();
    }

    public static Date getNextDate(String dateTime, int days) {
        Date date = DateUtil.stringToDate(dateTime, DateUtil.SIMPLE_YMD);
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.add(Calendar.DATE, days);
        return cal.getTime();
    }

    public static Date getNextDate(String dateTime, int days, String simple) {
        Date date = DateUtil.stringToDate(dateTime, simple);
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.add(Calendar.DATE, days);
        return cal.getTime();
    }

    public static Date getMonthStart(int monthNumber) {
        // 创建指定月份的第一天
        LocalDate startDate = LocalDate.of(LocalDate.now().getYear(), Month.of(monthNumber), 1);
        // 设置时间为当天的 00:00:00
        LocalTime startTime = LocalTime.MIN;
        ZoneId zoneId = ZoneId.systemDefault();
        LocalDateTime localDateTime = LocalDateTime.of(startDate, startTime);
        return Date.from(localDateTime.atZone(zoneId).toInstant());
    }

    public static Date getMonthEnd(int monthNumber) {
        // 创建指定月份的第一天
        LocalDate startDate = LocalDate.of(LocalDate.now().getYear(), Month.of(monthNumber), 1);
        // 获取下个月的第一天
        LocalDate nextMonthFirstDay = startDate.plusMonths(1).withDayOfMonth(1);
        // 减去一天得到当前月的最后一天
        LocalDate lastDayOfMonth = nextMonthFirstDay.minusDays(1);
        // 设置时间为当天的 23:59:59
        LocalTime endTime = LocalTime.MAX;
        ZoneId zoneId = ZoneId.systemDefault();
        LocalDateTime localDateTime = LocalDateTime.of(lastDayOfMonth, endTime);
        return Date.from(localDateTime.atZone(zoneId).toInstant());
    }

    public static Date plusYears(Date date, int amount) {
        return plus(date, Calendar.YEAR, amount);
    }

    public static Date plusMonths(Date date, int amount) {
        return plus(date, Calendar.MONTH, amount);
    }

    public static Date plusDays(Date date, int amount) {
        return plus(date, Calendar.DATE, amount);
    }

    public static Date plusHours(Date date, int amount) {
        return plus(date, Calendar.HOUR, amount);
    }

    public static Date plusMinutes(Date date, int amount) {
        return plus(date, Calendar.MINUTE, amount);
    }

    public static Date plusSeconds(Date date, int amount) {
        return plus(date, Calendar.SECOND, amount);
    }

    public static Date plusMilliseconds(Date date, int amount) {
        return plus(date, Calendar.MILLISECOND, amount);
    }

    private static Date plus(Date date, int field, int amount) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(field, amount);
        return calendar.getTime();
    }

    /**
     * 获取当前时间戳
     *
     * @return 当前时间戳
     */
    public static Long timestampNow() {
        return System.currentTimeMillis();
    }

    /**
     * 获取年份
     *
     * @param date 日期
     * @return 年份
     */
    public static int getYear(Date date) {
        return getPart(date, Calendar.YEAR);
    }

    /**
     * 获取月份
     *
     * @param date 日期
     * @return 年份
     */
    public static int getMonth(Date date) {
        return getPart(date, Calendar.MONTH) + 1;
    }

    /**
     * 获取天数
     *
     * @param date 日期
     * @return 年份
     */
    public static int getDay(Date date) {
        return getPart(date, Calendar.DATE);
    }

    /**
     * 获取星期
     * 0-星期天，1-星期一...6-星期六
     *
     * @param date 日期
     * @return 年份
     */
    public static int getDayOfWeek(Date date) {
        return getPart(date, Calendar.DAY_OF_WEEK) - 1;
    }

    /**
     * 获取月份中的天数
     *
     * @param date 日期
     * @return 年份
     */
    public static int getDayOfMonth(Date date) {
        return getPart(date, Calendar.DAY_OF_MONTH);
    }

    /**
     * 获取月份中的总天数
     *
     * @param date
     * @return
     */
    public static int getDaysByYearMonth(Date date) {
        int year = getYear(date);
        int month = getMonth(date);
        Calendar a = Calendar.getInstance();
        a.set(Calendar.YEAR, year);
        a.set(Calendar.MONTH, month - 1);
        a.set(Calendar.DATE, 1);
        a.roll(Calendar.DATE, -1);
        return a.get(Calendar.DATE);
    }

    /**
     * 获取年份中的天数
     *
     * @param date 日期
     * @return 年份
     */
    public static int getDayOfYear(Date date) {
        return getPart(date, Calendar.DAY_OF_YEAR);
    }

    private static int getPart(Date date, int type) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        return cal.get(type);
    }

    /**
     * 获取两个时间之间的间隔
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param seekType  便宜类型
     * @return 间隔量
     **/
    public static Long rangeTime(Date startTime, Date endTime, Integer seekType) {

        if (startTime.after(endTime)) {
            throw new BusinessException("开始时间大于结束时间 !");

        }

        Calendar startCalendar = Calendar.getInstance();
        startCalendar.setTime(startTime);
        Calendar endCalendar = Calendar.getInstance();
        endCalendar.setTime(endTime);

        Calendar temp = Calendar.getInstance();
        temp.setTime(endTime);
        temp.add(Calendar.DATE, 1);

        int year = endCalendar.get(Calendar.YEAR) - startCalendar.get(Calendar.YEAR);
        int month = endCalendar.get(Calendar.MONTH) - startCalendar.get(Calendar.MONTH);

        // 获取结束时间戳
        switch (seekType) {
            case Calendar.YEAR:
                return (long) year;
            case Calendar.MONTH:

                if ((startCalendar.get(Calendar.DATE) == 1) && (temp.get(Calendar.DATE) == 1)) {
                    return (long) (year * DEFAULT_MONTH_OF_YEAR + month + 1);
                } else if ((startCalendar.get(Calendar.DATE) != 1) && (temp.get(Calendar.DATE) == 1)) {
                    return (long) (year * DEFAULT_MONTH_OF_YEAR + month);
                } else if ((startCalendar.get(Calendar.DATE) == 1) && (temp.get(Calendar.DATE) != 1)) {
                    return (long) (year * DEFAULT_MONTH_OF_YEAR + month);
                } else {
                    return (long) ((year * DEFAULT_MONTH_OF_YEAR + month - 1) < 0 ? 0
                            : (year * DEFAULT_MONTH_OF_YEAR + month));
                }

            case Calendar.DATE:
                // 60 * 60 * 24 * 1000 = 86400000‬
                return (endTime.getTime() - startTime.getTime()) / (86400000);

            default:
                throw new BusinessException("不支持此 SeekType 【" + seekType + "】 !");
        }
    }

    /**
     * 描述: 设置当天结束时间
     *
     * @param date
     * @return
     */
    public static Date setDefaultEndDate(Date date) {
        date = DateUtils.setHours(date, DEFAULT_END_HOUR);
        date = DateUtils.setMinutes(date, DEFAULT_END_MINUTE);
        date = DateUtils.setSeconds(date, DEFAULT_END_SENCEND);
        date = DateUtils.setMilliseconds(date, DEFAULT_END_MIN_SENCEND);
        return date;
    }

    /**
     * Description: 计算时间的天数差
     *
     * @param begin
     * @param end
     * @return
     */
    public static long caculateDayDiff(Date begin, Date end) {
        return (end.getTime() - begin.getTime()) / (1000 * 60 * 60 * 24);
    }

    /**
     * 获取指定时间之前或之后几小时
     *
     * @param date
     * @param hours
     * @param isBefore
     * @return
     */
    public static Date getTimeByHours(Date date, int hours, boolean isBefore) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        if (isBefore) {
            calendar.add(Calendar.HOUR_OF_DAY, -1 * hours);
        } else {
            calendar.add(Calendar.HOUR_OF_DAY, hours);
        }
        return new Date(calendar.getTimeInMillis());
    }


    /**
     * 获取某年第一天日期
     *
     * @param year 年份
     * @return Date
     */
    public static Date getFirstOfYear(int year) {
        Calendar calendar = Calendar.getInstance();
        calendar.clear();
        calendar.set(Calendar.YEAR, year);
        Date currYearFirst = calendar.getTime();
        return currYearFirst;
    }

    /**
     * 获取当年的第一天
     * @return
     */
    public static Date getCurrentFirstOfYear() {
        Calendar currCal = Calendar.getInstance();
        int currentYear = currCal.get(Calendar.YEAR);
        return getFirstOfYear(currentYear);
    }

    /**
     * 获取某年最后一天日期
     * @param year 年份
     * @return Date
     */
    public static Date getLastOfYear(int year){
        Calendar calendar = Calendar.getInstance();
        calendar.clear();
        calendar.set(Calendar.YEAR, year);
        calendar.roll(Calendar.DAY_OF_YEAR, -1);
        return setDefaultEndDate(calendar.getTime());
    }

    /**
     * 获取当年的最后一天
     */
    public static Date getCurrentLastOfYear(){
        Calendar currCal=Calendar.getInstance();
        int currentYear = currCal.get(Calendar.YEAR);
        return getLastOfYear(currentYear);
    }
    public static Date setSecondZero(Date date){
        Calendar c1 = Calendar.getInstance();
        c1.setTime(date);
        c1.set(Calendar.SECOND, 0);
        c1.set(Calendar.MILLISECOND, 0);
        return c1.getTime();
    }

    public static Date getTimeBySecond(Date date, int second, boolean isBefore) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        if(isBefore){
            calendar.add(Calendar.SECOND, -1 * second);
        }else{
            calendar.add(Calendar.SECOND, second);
        }
        return new Date(calendar.getTimeInMillis());
    }

    public static Date getStartTime(int pastDays) {
        LocalDate localDate = LocalDate.now().minus(pastDays, ChronoUnit.DAYS);
        LocalDateTime localDateTime = LocalDateTime.of(localDate, LocalTime.MIN);
        return Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
    }

    public static Date getEndTime(int pastDays) {
        LocalDate localDate = LocalDate.now().minus(pastDays, ChronoUnit.DAYS);
        LocalDateTime localDateTime = LocalDateTime.of(localDate, LocalTime.MAX);
        return Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
    }

    public static Long intervalDays(Date date1, Date date2){
        // 将Date转换为LocalDate
        LocalDate localDate1 = date1.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate localDate2 = date2.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();

        // 计算相差的天数
        long days = ChronoUnit.DAYS.between(localDate1, localDate2);

        return days;
    }

    public static long minus(Date endDate, Date startDate, TimeUnit unit){
        long timeDiff = endDate.getTime() - startDate.getTime();
        return unit.convert(timeDiff, TimeUnit.MILLISECONDS);
        // long diffInSeconds = TimeUnit.MILLISECONDS.toSeconds(timeDiff);
        // long diffInMinutes = TimeUnit.MILLISECONDS.toMinutes(timeDiff);
        // long diffInHours = TimeUnit.MILLISECONDS.toHours(timeDiff);
        // long diffInDays = TimeUnit.MILLISECONDS.toDays(timeDiff);
    }

    public static boolean isOverlap(Date start1, Date end1, Date start2, Date end2) {
        // 判断两个时间段是否有重叠
        if (end1.before(start2) || start1.after(end2)) {
            // 如果时间段1的结束时间早于时间段2的开始时间，或时间段1的开始时间晚于时间段2的结束时间，说明两个时间段没有重叠
            return false;
        } else {
            // 否则，说明两个时间段有重叠
            return true;
        }
    }
    public static Date getDayOfStart(LocalDate localDate) {
        if(localDate == null){
            localDate = LocalDate.now();
        }
        LocalDateTime localDateTime = LocalDateTime.of(localDate, LocalTime.MIN);
        return Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
    }
    public static Date getDayOfEnd(LocalDate localDate) {
        if(localDate == null){
            localDate = LocalDate.now();
        }
        LocalDateTime localDateTime = LocalDateTime.of(localDate, LocalTime.MAX);
        return Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
    }

    public static Integer getRemainSeconds(Date currentDate) {
        LocalDateTime midnight = LocalDateTime.ofInstant(currentDate.toInstant(),
                        ZoneId.systemDefault()).plusDays(1).withHour(0).withMinute(0)
                .withSecond(0).withNano(0);
        LocalDateTime currentDateTime = LocalDateTime.ofInstant(currentDate.toInstant(),
                ZoneId.systemDefault());
        long seconds = ChronoUnit.SECONDS.between(currentDateTime, midnight);
        return (int) seconds;
    }

    public static Integer getRemainSeconds(Date currentDate, Date targetDate) {
        LocalDateTime midnight = LocalDateTime.ofInstant(targetDate.toInstant(),
                        ZoneId.systemDefault());
        LocalDateTime currentDateTime = LocalDateTime.ofInstant(currentDate.toInstant(),
                ZoneId.systemDefault());
        long seconds = ChronoUnit.SECONDS.between(currentDateTime, midnight);
        return (int) seconds;
    }
    /**
     * 获取两个日期之间相差的时间
     * date1 < date2, 返回正数
     * @param date1
     * @param date2
     * @param unit 时间单位，如ChronoUnit.HOURS
     * @return
     */
    public static Long interval(Date date1, Date date2, ChronoUnit unit){
        if (unit == ChronoUnit.DAYS) {
            // Convert to LocalDate objects and calculate interval in days
            LocalDate localDate1 = date1.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
            LocalDate localDate2 = date2.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
            return ChronoUnit.DAYS.between(localDate1, localDate2);
        } else {
            // Convert to LocalDateTime objects and calculate interval in the specified unit
            LocalDateTime localDateTime1 = LocalDateTime.ofInstant(date1.toInstant(), ZoneId.systemDefault());
            LocalDateTime localDateTime2 = LocalDateTime.ofInstant(date2.toInstant(), ZoneId.systemDefault());
            return unit.between(localDateTime1, localDateTime2);
        }
    }
    /**
     * 判断给定时间是否在给定时间范围内
     * 比起始时间小，返回-1
     * 在时间范围内，返回0
     * 比结束时间大，返回1
     * @param startDate
     * @param endDate
     * @param targetDate
     * @return
     */
    public static int compare(Date startDate, Date endDate, Date targetDate){
        Assert.isTrue(!startDate.after(endDate), "开始时间不能大于结束时间");
        if (targetDate.before(startDate)) {
            return -1;
        } else if (targetDate.after(endDate)) {
            return 1;
        } else {
            return 0;
        }
    }

    public static boolean isDateInCurrentMonth(String dateStr) {
        if(org.apache.commons.lang3.StringUtils.isBlank(dateStr)){
            return false;
        }
        try {
            // 解析日期
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            LocalDate givenDate = LocalDate.parse(dateStr, formatter);

            // 获取当前日期
            LocalDate currentDate = LocalDate.now();

            // 检查年份和月份是否相同
            return givenDate.getMonth() == currentDate.getMonth();
        } catch (Exception e) {
            log.error("日期解析失败", e);
            return false;
        }
    }

}
