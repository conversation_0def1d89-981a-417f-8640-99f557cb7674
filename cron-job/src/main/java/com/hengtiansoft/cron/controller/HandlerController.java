package com.hengtiansoft.cron.controller;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.hengtiansoft.common.entity.exception.BusinessException;
import com.hengtiansoft.common.entity.vo.Response;
import com.hengtiansoft.common.factory.ResponseFactory;
import com.hengtiansoft.cron.entity.dto.LabelDTO;
import com.hengtiansoft.cron.enums.PullTypeEnum;
import com.hengtiansoft.cron.handler.*;
import com.hengtiansoft.cron.service.CouponStatsService;
import com.hengtiansoft.cron.service.PeopleLabelService;
import com.hengtiansoft.order.entity.po.CouponRule;
import com.hengtiansoft.order.manager.CouponRuleManager;
import com.xxl.job.core.biz.model.ReturnT;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Objects;

@RestController
@RequestMapping("/handler")
@Slf4j
public class HandlerController {
    @Autowired
    private ProductSaleStatusUpdateHandler productSaleStatusUpdateHandler;

    @Autowired
    private BackupsTablesHandler backupsTablesHandler;
    @Autowired
    private CouponAutoOfflineHandler couponAutoOfflineHandler;
    @Autowired
    private CouponAutoOnlineHandler couponAutoOnlineHandler;
    @Autowired
    private CouponAutoExpireHandler couponAutoExpireHandler;
    @Autowired
    private OrderCancelHandler orderCancelHandler;
    @Autowired
    private DispatchPlanAutoConfirmHandler dispatchPlanAutoConfirmHandler;
    @Autowired
    private ShareActivityAutoPointHandler shareActivityAutoPointHandler;
    @Resource
    private UserPointHandler userPointHandler;
    @Resource
    private PeopleLableHandler peopleLableHandler;
    @Resource
    private PeopleLabelService peopleLabelService;
    @Resource
    private PeoplePackageLableHandler peoplePackageLableHandler;
    @Resource
    private UserOrderNickNameHandler userOrderNickNameHandler;
    @Resource
    private WechatActivityMsgHandler wechatActivityMsgHandler;
    @Resource
    private LowTemDispatchPlanNoticeHandler lowTemDispatchPlanNoticeHandler;
    @Resource
    private LoginContentAutoPushHandler loginContentAutoPushHandler;
    @Resource
    private OrderDefaultCommentHandler orderDefaultCommentHandler;
    @Resource
    private PeopleLableManuaHandler peopleLableManuaHandler;
    @Resource
    private PointMallAutoHandler pointMallAutoHandler;
    @Resource
    private IndexModuleContentAutoHandler indexModuleContentAutoHandler;
    @Resource
    private DataFinderModuleHandler dataFinderHandler;
    @Resource
    private DataFinderModuleContentHandler dataFinderModuleContentHandler;
    @Resource
    private WechatUserSyncHandler wechatUserSyncHandler;
    @Resource
    private WechatUserOpenIdSyncHandler wechatUserOpenIdSyncHandler;
    @Resource
    private UserMinBuyDateHandler userMinBuyDateHandler;
    @Resource
    private CouponInfoCountHandler couponInfoCountHandler;
    @Resource
    private CouponRuleManager couponRuleManager;
    @Resource
    private CouponStatsHandler couponStatsHandler;
    @Resource
    private CouponStatsService couponStatsService;
    @Resource
    private OrderReportVolcengineHandler orderReportVolcengineHandler;
    @Resource
    private UserOrderInfoHandler userOrderInfoHandler;
    @Resource
    private FreeTrialDrawHandler freeTrialDrawHandler;
    @Resource
    private FreeTrialUserCntHandler freeTrialUserCntHandler;
    @Resource
    private FreeTrialNewHandler freeTrialNewHandler;
    @Resource
    private FreeTrialManualDrawHandler freeTrialManualDrawHandler;
    @Resource
    private FreeTrialBackHandler freeTrialBackHandler;
    @Resource
    private FreeTrialStatsHandler freeTrialStatsHandler;
    @Resource
    private AutomaticCloseHandler automaticCloseHandler;
    @Resource
    private AutomaticSuccessHandler automaticSuccessHandler;
    @Resource
    private AutomaticSuccessMonitorHandler automaticSuccessMonitorHandler;
    @Resource
    private NaiKaShopHandler naiKaShopHandler;
    @Resource
    private CardSummaryHandler cardSummaryHandler;
    @Resource
    private FreeTrialCommentPrizeHandler freeTrialCommentPrizeHandler;
    @Resource
    private FreeTrialCommentWeightHandler freeTrialCommentWeightHandler;
    @Resource
    private FreeTrialVoteStatsHandler freeTrialVoteStatsHandler;
    @Resource
    private DiscountNewNoticeHandler discountNewNoticeHandler;
    @Resource
    private DiscountStartNoticeHandler discountStartNoticeHandler;
    @Resource
    private OmsTestHandler omsTestHandler;
    @Resource
    private UserLifeCycleStatsHandler userLifeCycleStatsHandler;
    @Resource
    private KnowledgeFilesHandler knowledgeFilesHandler;
    @Resource
    private ProductHotListHandler productHotListHandler;
    @Resource
    private WorkWxUserSyncHandler workWxUserSyncHandler;
    @Resource
    private WorkWxDepartmentSyncHandler workWxDepartmentSyncHandler;
    @Resource
    private WorkWxExternalContactSyncHandler workWxExternalContactSyncHandler;
    @Resource
    private DispatchPlanRemindHandler dispatchPlanRemindHandler;
    @Resource
    private CommunityOrderStatsHandler communityOrderStatsHandler;


    @GetMapping("/productSaleStatus")
    public void productSaleStatus() throws Exception {
        productSaleStatusUpdateHandler.execute("");
    }

    @GetMapping("/backupsTables")
    public void backupsTables() throws Exception {
        backupsTablesHandler.execute("");
    }

    @GetMapping("/couponAutoOnline")
    public void couponAutoOnline() throws Exception {
        couponAutoOnlineHandler.execute("");
    }

    @GetMapping("/couponAutoOffline")
    public void couponAutoOffline() throws Exception {
        couponAutoOfflineHandler.execute("");
    }

    @GetMapping("/couponAutoExpire")
    public void couponAutoExpire() throws Exception {
        couponAutoExpireHandler.execute("");
    }

    @GetMapping("/orderCancelHandler")
    public void orderCancelHandler() throws Exception {
        orderCancelHandler.execute("");
    }

    @GetMapping("/dispatchPlanAutoConfirmHandler")
    public void dispatchPlanAutoConfirmHandler() throws Exception {
        dispatchPlanAutoConfirmHandler.execute("");
    }

    @GetMapping("/shareActivityAutoPointHandler")
    public void shareActivityAutoPointHandler() throws Exception {
        shareActivityAutoPointHandler.execute("");
    }

    @GetMapping("/userPointHandler")
    public void userPointHandler() throws Exception {
        // 全量，先10个测试
        // String s = "{\"pullTyp\":3}";
        String s = "{\"pullTyp\":2,\"ids\":[680871,680662]}";
        userPointHandler.execute(s);
    }

    @GetMapping("/peopleLableHandler")
    public void peopleLableHandler() throws Exception {
        ReturnT<String> execute = peopleLableHandler.execute("");
        System.out.println(execute);
    }

    @GetMapping("/peoplePackageLableHandler")
    public void peoplePackageLableHandler() throws Exception {
        ReturnT<String> execute = peoplePackageLableHandler.execute("");
        System.out.println(execute);
    }

    @GetMapping("/peopleLableHandler/batch")
    public void peopleLableHandlerBatch(@RequestParam Long id) throws Exception {

        LabelDTO labelDTO = new LabelDTO();
        labelDTO.setPullTyp(PullTypeEnum.BATCH.getCode());
        labelDTO.setIds(Lists.newArrayList(id));
        peopleLabelService.compute(labelDTO);
    }

    @GetMapping("/userOrderNickNameHandler")
    public void userOrderNickNameHandler() throws Exception {
        // 全量，先10个测试
        String s = "{\"pullTyp\":3}";
        // String s = "{\"pullTyp\":2,\"ids\":[680846]}";
        userOrderNickNameHandler.execute(s);
    }

    @GetMapping("/wechatActivityMsgHandler")
    public void wechatActivityMsgHandler() throws Exception {
        wechatActivityMsgHandler.execute(null);
    }

    @GetMapping("/loginContentAutoPushHandler")
    public void loginContentAutoPushHandler() throws Exception {
        loginContentAutoPushHandler.execute(null);
    }

    @GetMapping("/lowTemDispatchPlanNoticeHandler")
    public void lowTemDispatchPlanNoticeHandler() throws Exception {
        lowTemDispatchPlanNoticeHandler.execute(null);
    }

    @GetMapping("/orderDefaultCommentHandler")
    public void orderDefaultCommentHandler() throws Exception {
        orderDefaultCommentHandler.execute(null);
    }

    @GetMapping("/peopleLableManuaHandler")
    public void peopleLableManuaHandler() throws Exception {
        String str = "{\"pullTyp\":2,\"ids\":[339]}";
        peopleLableManuaHandler.execute(str);
    }

    @GetMapping("/pointMallAutoHandler")
    public void pointMallAutoHandler() throws Exception {
        pointMallAutoHandler.execute(null);
    }
    @GetMapping("/indexModuleContentAutoHandler")
    public void indexModuleContentAutoHandler() throws Exception {
        indexModuleContentAutoHandler.execute(null);
    }


    @PostMapping("/system/clear")
    public Response<String> systemClear(@RequestBody JSONObject dto) {
        log.info("系统数据清理");
        String secret = dto.getString("secret");
        if(!Objects.equals(secret, "cONUtRah")){
            throw new BusinessException("非法操作");
        }
        System.gc();
        return ResponseFactory.success();
    }

    @GetMapping("/dataFinderHandler")
    public void dataFinderHandler() throws Exception {
        dataFinderHandler.execute("");
    }

    @GetMapping("/dataFinderModuleContentHandler")
    public void dataFinderModuleContentHandler() throws Exception {
        dataFinderModuleContentHandler.execute("");
    }

    @GetMapping("/userMinBuyDateHandler")
    public void userMinBuyDateHandler() throws Exception {
        // 全量，先10个测试
        String str = "{\"pullTyp\":3}";
        userMinBuyDateHandler.execute(str);
    }

    @GetMapping("/couponInfoCountHandler")
    public void couponInfoCountHandler() throws Exception {
        couponInfoCountHandler.execute("");
    }

    @GetMapping("/couponInfoCountHandler/{id}")
    public void couponInfoCountHandler(@PathVariable(name = "id") Long id) throws Exception {
        CouponRule couponRule = couponRuleManager.findById(id);
        couponRuleManager.updateCount(couponRule);
    }

    @GetMapping("/couponStatsHandler")
    public void couponStatsHandler() throws Exception {
        couponStatsHandler.execute("");
    }

    @GetMapping("/couponStatsHandler/{id}")
    public void couponStatsHandler(@PathVariable(name = "id") Long id) throws Exception {
        CouponRule couponRule = couponRuleManager.findById(id);
        couponStatsService.updateStats(couponRule);
    }

    @GetMapping("/wechatUserSyncHandler")
    public void wechatUserSyncHandler() throws Exception {
        wechatUserSyncHandler.execute("");
    }

    @GetMapping("/wechatUserOpenIdSyncHandler")
    public void wechatUserOpenIdSyncHandler() throws Exception {
        wechatUserOpenIdSyncHandler.execute("");
    }

    @GetMapping("/orderReportVolcengineHandler")
    public void orderReportVolcengineHandler() throws Exception {
        orderReportVolcengineHandler.execute("{\"event\":\"order\"}");
    }

    @GetMapping("/userOrderInfoHandler")
    public void userOrderInfoHandler() throws Exception {
        // String str = "{\"pullTyp\":3}";
        String str = "{\"pullTyp\":2,\"ids\":[680662]}";
        userOrderInfoHandler.execute(str);
    }

    @GetMapping("/freeTrialDrawHandler")
    public void FreeTrialDrawHandler() throws Exception{
        freeTrialDrawHandler.execute("");
    }

    @GetMapping("/freeTrialUserCntHandler")
    public void FreeTrialUserCntHandler() throws Exception{
        freeTrialUserCntHandler.execute("");
    }

    @GetMapping("/freeTrialNewHandler")
    public void freeTrialNewHandler() throws Exception{
        freeTrialNewHandler.execute("");
    }

    @GetMapping("/freeTrialManualDrawHandler")
    public void freeTrialManualDrawHandler() throws Exception{
        freeTrialManualDrawHandler.execute("");
    }
    @GetMapping("/freeTrialBackHandler")
    public void freeTrialBackHandler() throws Exception{
        freeTrialBackHandler.execute("");
    }

    @GetMapping("/freeTrialStatsHandler")
    public void freeTrialStatsHandler() throws Exception{
        freeTrialStatsHandler.execute("");
    }

    @GetMapping("/automaticCloseHandler")
    public void AutomaticCloseHandler() throws Exception{
        automaticCloseHandler.execute("");
    }

    @GetMapping("/automaticSuccessHandler")
    public void AutomaticSuccessHandler() throws Exception{
        automaticSuccessHandler.execute("");
    }

    @GetMapping("/automaticSuccessMonitorHandler")
    public void AutomaticSuccessMonitorHandler() throws Exception{
        automaticSuccessMonitorHandler.execute("");
    }

    @GetMapping("/naikaShopHandler")
    public void NaiKaShopHandler() throws Exception{
        naiKaShopHandler.execute("");
    }

    @GetMapping("/cardSummaryHandler")
    public void cardSummaryHandler() throws Exception{
        cardSummaryHandler.execute("");
    }

    @GetMapping("/freeTrialCommentPrizeHandler")
    public void freeTrialCommentPrizeHandler() throws Exception{
        freeTrialCommentPrizeHandler.execute("");
    }

    @GetMapping("/freeTrialCommentWeightHandler")
    public void freeTrialCommentWeightHandler() throws Exception{
        freeTrialCommentWeightHandler.execute("");
    }

    @GetMapping("/freeTrialVoteStatsHandler")
    public void freeTrialVoteStatsHandler() throws Exception{
        freeTrialVoteStatsHandler.execute("");
    }

    @GetMapping("/discountNewNoticeHandler")
    public void discountNewNoticeHandler() throws Exception{
        discountNewNoticeHandler.execute("");
    }

    @GetMapping("/discountStartNoticeHandler")
    public void discountStartNoticeHandler() throws Exception{
        discountStartNoticeHandler.execute("");
    }

    @GetMapping("/omsTestHandler")
    public void omsTestHandler() throws Exception{
        omsTestHandler.execute("LPK202407251731426293595");
    }

    @GetMapping("/userLifeCycleStatsHandler")
    public void userLifeCycleStatsHandler() throws Exception{
        userLifeCycleStatsHandler.execute("");
    }

    @GetMapping("/knowledgeFilesHandler")
    public void knowledgeFilesHandler() throws Exception{
        knowledgeFilesHandler.execute("");
    }

    @GetMapping("/productHotListHandler")
    public void productHotListHandler() throws Exception{
        productHotListHandler.execute("");
    }

    @GetMapping("/workWxUserSyncHandler")
    public void workWxUserSyncHandler() throws Exception{
        workWxUserSyncHandler.execute("");
    }

    @GetMapping("/workWxDepartmentSyncHandler")
    public void workWxDepartmentSyncHandler() throws Exception{
        workWxDepartmentSyncHandler.execute("");
    }

    @GetMapping("/workWxExternalContactSyncHandler")
    public void workWxExternalContactSyncHandler() throws Exception{
        workWxExternalContactSyncHandler.execute("");
    }

    @GetMapping("/dispatchPlanRemindHandler")
    public void dispatchPlanRemindHandler() throws Exception{
        dispatchPlanRemindHandler.execute("");
    }

    @GetMapping("/communityOrderStatsHandler")
    public void communityOrderStatsHandler() throws Exception{
        communityOrderStatsHandler.execute("");
    }

}
