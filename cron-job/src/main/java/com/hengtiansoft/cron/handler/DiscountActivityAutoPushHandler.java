package com.hengtiansoft.cron.handler;

import com.hengtiansoft.cron.service.DiscountActivityService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * 折扣活动自动上下线
 */
@Component
@Slf4j
@JobHandler(value = "discountActivityAutoPushHandler")
public class DiscountActivityAutoPushHandler extends IJobHandler {

    @Resource
    private DiscountActivityService discountActivityService;
    private static Integer count=0;

    @Override
    public ReturnT<String> execute(String s){
        try {
            log.info("折扣活动自动上下线 . 开始");
            //根据count次数判断是否执行，count是20的倍数执行一次
            if(count%360==0 || Objects.equals("refresh",s)){
                discountActivityService.refreshRedis();
                count=0;
            }
            count++;
            discountActivityService.autoPush();
            log.info("折扣活动自动上下线 . 结束");
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            // 记录系统日志
            log.error("折扣活动自动上下线 . 出错", e);
            // 向xxl-job-admin汇报任务日志
            XxlJobLogger.log(e);
            // 返回任务执行失败
            return ReturnT.FAIL;
        }
    }
}