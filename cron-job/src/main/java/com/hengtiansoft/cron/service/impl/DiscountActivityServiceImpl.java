package com.hengtiansoft.cron.service.impl;

import com.alibaba.fastjson.JSON;
import com.hengtiansoft.common.entity.exception.BusinessException;
import com.hengtiansoft.common.enumeration.DeleteFlagEnum;
import com.hengtiansoft.common.enumeration.PeopleLimitEnum;
import com.hengtiansoft.common.redis.RedisOperation;
import com.hengtiansoft.common.util.BeanUtils;
import com.hengtiansoft.common.util.MsgUtil;
import com.hengtiansoft.common.util.StreamUtils;
import com.hengtiansoft.cron.service.DiscountActivityService;
import com.hengtiansoft.cron.service.MilkProducerService;
import com.hengtiansoft.item.entity.dto.DiscountActivityDTO;
import com.hengtiansoft.item.entity.dto.DiscountProductDTO;
import com.hengtiansoft.item.entity.dto.SkuDiscountActivityDTO;
import com.hengtiansoft.item.entity.po.DiscountActivity;
import com.hengtiansoft.item.entity.po.DiscountActivityRange;
import com.hengtiansoft.item.enumeration.DiscountActivityStatusEnum;
import com.hengtiansoft.item.interfaces.ProductManager;
import com.hengtiansoft.item.manager.DiscountActivityManager;
import com.hengtiansoft.item.manager.DiscountActivityRangeManager;
import com.hengtiansoft.item.utils.DiscountUtil;
import com.hengtiansoft.order.entity.vo.OrderDiscountVO;
import com.hengtiansoft.order.manager.OrderManager;
import com.hengtiansoft.privilege.entity.dto.WechatActivityMsgDTO;
import com.hengtiansoft.privilege.entity.dto.WechatDiscountActivityMsgDTO;
import com.hengtiansoft.privilege.enums.WeLinkTypeEnum;
import com.hengtiansoft.privilege.enums.WeSendModeEnum;
import com.hengtiansoft.privilege.manager.WechatActivityMsgManager;
import com.hengtiansoft.thirdpart.config.WxMiniMessageConfig;
import com.hengtiansoft.thirdpart.enumeration.MessageSubscribeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class DiscountActivityServiceImpl implements DiscountActivityService{
    @Resource
    private DiscountActivityManager discountActivityManager;
    @Resource
    private DiscountActivityRangeManager discountActivityRangeManager;
    @Resource
    private OrderManager orderManager;
    @Resource
    private RedisOperation redisOperation;
    @Resource
    private MilkProducerService milkProducerService;
    @Resource
    private WechatActivityMsgManager wechatActivityMsgManager;
    @Resource
    private ProductManager productManager;
    @Resource
    private WxMiniMessageConfig wxMiniMessageConfig;

    @Override
    public void countDiscountActivity() {
        //活动结束时间>当前日期-7
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_MONTH, -7);
        // 进行中和已结束的活动
        DiscountActivityDTO dto = new DiscountActivityDTO();
        // dto.setStatusList(Lists.newArrayList(DiscountActivityStatusEnum.IN_PROGRESS.getCode(), DiscountActivityStatusEnum.END.getCode()));
        dto.setEndTime(calendar.getTime());
        dto.setDelflag(DeleteFlagEnum.IS_NOT_DELETE.getCode());
        List<DiscountActivity> activityList = discountActivityManager.findByCondition(dto);
        for (DiscountActivity activity : activityList) {
            List<OrderDiscountVO> orderDiscountList = orderManager.discountOrder(activity.getId());
            if(CollectionUtils.isEmpty(orderDiscountList)){
                activity.setOrderAmount(new BigDecimal(0));
                activity.setOrderCount(0);
                activity.setDealCount(0);
                activity.setNewDealCount(0);
                discountActivityManager.update(activity);
                continue;
            }
            // 订单金额
            activity.setOrderAmount(orderDiscountList.stream().map(OrderDiscountVO::getSumRealAmount).reduce(BigDecimal.ZERO,BigDecimal::add));
            // 订单数量
            activity.setOrderCount(StreamUtils.mapToInt(orderDiscountList, OrderDiscountVO::getCountOrder).sum());
            // 成交人数
            activity.setDealCount(orderDiscountList.size());
            // 成交新客数量
            Integer newDealCount = 0;
            List<Long> userIds = StreamUtils.toList(orderDiscountList, OrderDiscountVO::getUserId);
            List<OrderDiscountVO> orderDiscountNotList = orderManager.discountNotOrder(activity.getId(), userIds);
            Map<Long, OrderDiscountVO> orderDiscountNotMap = StreamUtils.toMap(orderDiscountNotList, OrderDiscountVO::getUserId);
            for (OrderDiscountVO orderDiscountVO : orderDiscountList) {
                OrderDiscountVO minOrder = orderDiscountNotMap.get(orderDiscountVO.getUserId());
                if(minOrder == null){
                    newDealCount ++;
                    continue;
                }
                if(orderDiscountVO.getMinOrderTime().getTime()  <= minOrder.getMinOrderTime().getTime()){
                    newDealCount ++;
                }
            }
            activity.setNewDealCount(newDealCount);
            discountActivityManager.update(activity);
        }
    }

    @Override
    public void autoPush() {
        Map<Object, Object> objectMap = redisOperation.hgetAll(DiscountUtil.DISCOUNT_ACTIVITYID);
        for (Map.Entry<Object, Object> entry : objectMap.entrySet()) {
            DiscountActivity po = (DiscountActivity)entry.getValue();
            // 获取开始时间前1s
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(po.getStartTime());
            calendar.add(Calendar.SECOND, -1);
            DiscountActivityStatusEnum statusEnum = DiscountUtil.getStatus(calendar.getTime(), po.getEndTime());
            if(Objects.equals(statusEnum.getCode(), po.getStatus())){
                continue;
            }
            // 修改status
            try {
                po.setStatus(statusEnum.getCode());
                DiscountActivityDTO dto = new DiscountActivityDTO();
                BeanUtils.copy(po, dto);
                dto.setRangeList(null);
                discountActivityManager.insertOrUpdate(dto);
            } catch (Exception e) {
                log.error("折扣活动自动上下线 . 失败 -> id:{},name:{}", po.getId(), po.getName(), e);
                if(Objects.equals("限时折扣不存在", e.getMessage())){
                    redisOperation.hdel(DiscountUtil.DISCOUNT_ACTIVITYID, po.getId().toString());
                }
            }
        }
    }
    @Override
    public void refreshRedis() {
        DiscountActivityDTO dto = new DiscountActivityDTO();
        dto.setDelflag(DeleteFlagEnum.IS_NOT_DELETE.getCode());
        List<DiscountActivity> activityList = discountActivityManager.findByCondition(dto);
        List<Long> activityIds = StreamUtils.toList(activityList, DiscountActivity::getId);
        Map<Long, List<DiscountActivityRange>> rangeListMap = StreamUtils.group(discountActivityRangeManager.findByActivityIdsWithDel(activityIds),
                DiscountActivityRange::getDiscountActivityId);
        // 未删除
        for (DiscountActivity activity : activityList) {
            DiscountActivityStatusEnum statusEnum = DiscountActivityStatusEnum.getEnum(activity.getStatus());
            if(statusEnum == DiscountActivityStatusEnum.NOT_STARTED
                    || statusEnum == DiscountActivityStatusEnum.IN_PROGRESS){
                redisOperation.hset(DiscountUtil.DISCOUNT_ACTIVITYID, activity.getId().toString(), activity);
                rangeRedisUpdate(activity, rangeListMap.get(activity.getId()));
            }else{
                redisOperation.hdel(DiscountUtil.DISCOUNT_ACTIVITYID, activity.getId().toString());
            }
        }
        //已删除
        DiscountActivityDTO dto4Del = new DiscountActivityDTO();
        dto4Del.setDelflag(DeleteFlagEnum.IS_DELETE.getCode());
        // 当前一个月时间
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.MONTH, -1);
        dto4Del.setEndTime(calendar.getTime());
        List<DiscountActivity> activityDelList = discountActivityManager.findByCondition(dto4Del);
        // 获取activityDelList中的id
        List<Long> activityDelIds = StreamUtils.toList(activityDelList, DiscountActivity::getId);
        Map<Long, List<DiscountActivityRange>> rangeDelListMap = StreamUtils.group(discountActivityRangeManager.findByActivityIdsWithDel(activityDelIds),
                DiscountActivityRange::getDiscountActivityId);
        for (DiscountActivity activity : activityDelList) {
            redisOperation.hdel(DiscountUtil.DISCOUNT_ACTIVITYID, activity.getId().toString());
            rangeRedisUpdate(activity, rangeDelListMap.get(activity.getId()));
        }
    }

    @Override
    public void notice(Long activityId, MessageSubscribeEnum messageEnum) {
        log.info("限时折扣活动开始通知 . 开始 --> activityId:{}, messageEnum:{}", activityId, messageEnum);
        if(messageEnum == MessageSubscribeEnum.DISCOUNT_START_NOTICE){
            List<DiscountProductDTO> discountProductList = discountActivityRangeManager.findProductByActivityId(activityId);
            for (DiscountProductDTO dto: discountProductList) {
                Long msgId = this.createMsg(dto, messageEnum);
                if(null != msgId){
                    // 如果是立即发送，则以当前时间发送
                    milkProducerService.activityMsgPush(msgId, new Date());
                }
            }
        }else if(messageEnum == MessageSubscribeEnum.DISCOUNT_NEW_NOTICE){
            DiscountActivity activity = discountActivityManager.get(activityId);
            if(null == activity){
                return;
            }
            DiscountProductDTO dto = new DiscountProductDTO();
            dto.setStartTime(activity.getStartTime());
            dto.setDiscountActivityId(activity.getId());
            Long msgId = this.createMsg(dto, messageEnum);
            if(null != msgId){
                // 如果是立即发送，则以当前时间发送
                milkProducerService.activityMsgPush(msgId, new Date());
            }
        }
        log.info("限时折扣活动开始通知 . 结束 ");
    }

    private Long createMsg(DiscountProductDTO discountProductDTO, MessageSubscribeEnum messageEnum) {
        String remind = null;
        String link = null;
        WechatDiscountActivityMsgDTO msg = new WechatDiscountActivityMsgDTO();
        String templateId;
        switch (messageEnum){
            case DISCOUNT_NEW_NOTICE:
                // 上新通知
                remind = "您订阅的限时折扣上新啦！点击查看";
                link = "packageB/pages/discount/index";
                templateId = wxMiniMessageConfig.getDiscountNewTemplateId();
                //活动类型
                msg.setContent("限时折扣");
                //开始时间
                msg.setStartTime(discountProductDTO.getStartTime());
                //温馨提示
                msg.setRemind(remind);
                break;
            case DISCOUNT_START_NOTICE:
                // 开始通知
                remind = "您订阅的优惠活动即将开始！点击查看";
                link = "packageB/pages/singleDetail/singleDetail?id=" + discountProductDTO.getProductId();
                templateId = wxMiniMessageConfig.getDiscountStartTemplateId();
                msg.setProductId(discountProductDTO.getProductId());
                //商品名称
                msg.setProductName(MsgUtil.subName(discountProductDTO.getProductName()));
                //抢购时间
                msg.setStartTime(discountProductDTO.getStartTime());
                //商品价格
                msg.setDiscountPrice(discountProductDTO.getDiscountPrice());
                //温馨提示
                msg.setRemind(remind);
                break;
            default:
                log.error("限时折扣活动通知 . 未知的消息类型 --> messageEnum:{}", messageEnum);
                throw new BusinessException("限时折扣活动通知 . 未知的消息类型");
        }

        WechatActivityMsgDTO dto = new WechatActivityMsgDTO();
        dto.setTitle(DiscountUtil.TITLE);
        dto.setMsgType(messageEnum.getCode());
        dto.setContent(remind);
        dto.setName(DiscountUtil.NAME);
        dto.setRemind(remind);
        dto.setLinkType(WeLinkTypeEnum.INEEER_LINK.getCode());
        dto.setLink(link);
        dto.setSendMode(WeSendModeEnum.IMMED.getCode());
        dto.setPeopleLimit(PeopleLimitEnum.NO_LIMIT.getCode());
        dto.setActivityId(discountProductDTO.getDiscountActivityId());
        dto.setMsg(JSON.toJSONString(msg));
        Long msgId = wechatActivityMsgManager.insert(dto, "system-cron", templateId);
        return msgId;
    }

    private void rangeRedisUpdate(DiscountActivity activity, List<DiscountActivityRange> list) {
        if(CollectionUtils.isEmpty(list)){
            return;
        }
        if(DeleteFlagEnum.IS_DELETE.getCode().equals(activity.getDelflag())){
            // 删除
            for (DiscountActivityRange range : list) {
                redisOperation.hdel(DiscountUtil.DISCOUNT_ACTIVITY_KEY + range.getSkuId(), activity.getId().toString());
            }
        }else{
            DiscountActivityStatusEnum statusEnum = DiscountActivityStatusEnum.getEnum(activity.getStatus());
            if(statusEnum == DiscountActivityStatusEnum.NOT_STARTED
                    || statusEnum == DiscountActivityStatusEnum.IN_PROGRESS){
                // 未开始或进行中
                for (DiscountActivityRange range : list) {
                    if(DeleteFlagEnum.IS_DELETE.getCode().equals(range.getDelflag())){
                        redisOperation.hdel(DiscountUtil.DISCOUNT_ACTIVITY_KEY + range.getSkuId(), activity.getId().toString());
                    }else{
                        SkuDiscountActivityDTO skuDTO = DiscountUtil.buildSkuDTO(activity, range);
                        redisOperation.hset(DiscountUtil.DISCOUNT_ACTIVITY_KEY + range.getSkuId(), activity.getId().toString(), JSON.toJSONString(skuDTO));
                    }
                }
            }else{
                // 已结束
                for (DiscountActivityRange range : list) {
                    redisOperation.hdel(DiscountUtil.DISCOUNT_ACTIVITY_KEY + range.getSkuId(), activity.getId().toString());
                }
            }
        }
    }
}
