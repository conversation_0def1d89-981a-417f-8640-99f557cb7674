package com.hengtiansoft.cron;

import com.hengtiansoft.order.entity.po.CouponInfo;
import com.hengtiansoft.order.enums.CouponInfoStatusTypeEnum;
import com.hengtiansoft.order.manager.CouponRuleManager;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.context.web.WebAppConfiguration;

import javax.annotation.Resource;

import java.util.Date;

/**
 * CouponRuleManagerImplTest
 * <AUTHOR>
 */
@SpringBootTest
@WebAppConfiguration
@RunWith(SpringRunner.class)
@Slf4j
public class CouponRuleManagerImplTest {
    @Resource
    private CouponRuleManager couponRuleManager;

    @Test
    public void updateExpire() {
        CouponInfo couponInfo=new CouponInfo();
        couponInfo.setUpdateTime(new Date());
        couponInfo.setStatus(CouponInfoStatusTypeEnum.EXPIRE.getCode());
        couponRuleManager.updateExpire(couponInfo);
    }
}