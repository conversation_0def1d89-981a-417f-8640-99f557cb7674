package com.hengtiansoft.cron;

import com.alibaba.fastjson.JSON;
import com.hengtiansoft.cron.entity.dto.PushDTO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.JUnit4;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@RunWith(JUnit4.class)
public class SimpleTest {

    @Test
    public void testJson(){
        String s="{\"pushFlag\":3,\"startTime\":\"2022-09-30 01:00:00\"}";
        PushDTO pushDTO = JSON.parseObject(s, PushDTO.class);
        System.out.println(pushDTO);
    }

    @Test
    public void testBigdecimal(){
        BigDecimal amount = BigDecimal.valueOf(18.91);
        BigDecimal orderAmount = BigDecimal.valueOf(18.90);
        System.out.println(amount.compareTo(orderAmount));
        if (amount.compareTo(orderAmount) != 0) {
            System.out.println(amount.subtract(orderAmount).abs().compareTo(BigDecimal.valueOf(0.01)));
        }
    }

    @Test
    public void testBigdecimal2(){
        BigDecimal amount = BigDecimal.valueOf(0);
        long totalFee = amount.multiply(BigDecimal.valueOf(100)).longValue();
        System.out.println(totalFee);
    }

}
