package com.hengtiansoft.item.dao;

import com.hengtiansoft.common.enumeration.DeleteFlagEnum;
import com.hengtiansoft.item.entity.dto.CardCategoryListDTO;
import com.hengtiansoft.item.entity.po.CardCategory;
import com.hengtiansoft.item.mapper.CardCategoryMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 奶卡类别dao
 *
 * <AUTHOR>
 * @date 2020/9/2 15:47
 */
@Repository
public class CardCategoryDao {

    @Autowired
    private CardCategoryMapper cardCategoryMapper;

    public CardCategory selectByPrimaryKey(Long id) {
        Condition condition = new Condition(CardCategory.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("id", id).andEqualTo("deleteFlag", DeleteFlagEnum.IS_NOT_DELETE.getCode());
        return cardCategoryMapper.selectOneByExample(condition);
    }

    public List<CardCategory> selectByPrimaryKey(List<Long> ids) {
        if(CollectionUtils.isEmpty(ids)){
            return Collections.emptyList();
        }
        Condition condition = new Condition(CardCategory.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andIn("id", ids).andEqualTo("deleteFlag", DeleteFlagEnum.IS_NOT_DELETE.getCode());
        return cardCategoryMapper.selectByCondition(condition);
    }

    /**
     * 根据类别名称批量查询
     *
     * @param names
     * @return
     */
    public List<CardCategory> selectByNames(List<String> names) {
        Condition condition = new Condition(CardCategory.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andIn("categoryName", names).andEqualTo("deleteFlag", DeleteFlagEnum.IS_NOT_DELETE.getCode());
        return cardCategoryMapper.selectByExample(condition);
    }

    /**
     * 获取所有类别
     *
     * @return
     */
    public List<CardCategory> getAll() {
        Condition condition = new Condition(CardCategory.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("deleteFlag", DeleteFlagEnum.IS_NOT_DELETE.getCode());
        return cardCategoryMapper.selectByExample(condition);

    }

    /**
     * 列表查询
     *
     * @param dto
     * @return
     */
    public List<CardCategory> findByCondition(CardCategoryListDTO dto) {
        Condition condition = new Condition(CardCategory.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("deleteFlag", DeleteFlagEnum.IS_NOT_DELETE.getCode());
        if (StringUtils.isNotBlank(dto.getCategoryName())) {
            criteria.andLike("categoryName", "%" + dto.getCategoryName().trim() + "%");
        }
        if (Objects.nonNull(dto.getCategoryCount())) {
            criteria.andEqualTo("categoryCount", dto.getCategoryCount());
        }
        if (Objects.nonNull(dto.getTemperature())) {
            criteria.andEqualTo("temperature", dto.getTemperature());
        }
        if (CollectionUtils.isNotEmpty(dto.getProductCategoryList())) {
            criteria.andIn("productCategory", dto.getProductCategoryList());
        }
        condition.orderBy("createTime").desc();
        return cardCategoryMapper.selectByExample(condition);
    }

    /**
     * 礼品卡特殊sql 列表查询
     *
     * @param dto
     * @return
     */
    public List<CardCategory> findByECard(CardCategoryListDTO dto, List<Integer> categoryCount) {
        Condition condition = new Condition(CardCategory.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("deleteFlag", DeleteFlagEnum.IS_NOT_DELETE.getCode());
        if (StringUtils.isNotBlank(dto.getCategoryName())) {
            criteria.andEqualTo("categoryName", dto.getCategoryName());
        }
        if (Objects.nonNull(dto.getCategoryCount())) {
            criteria.andEqualTo("categoryCount", dto.getCategoryCount());
        }
        if (CollectionUtils.isNotEmpty(categoryCount)) {
            criteria.andNotIn("categoryCount", categoryCount);
        }
        condition.orderBy("createTime").desc();
        return cardCategoryMapper.selectByExample(condition);
    }

    /**
     * 重名校验
     *
     * @param name
     * @return 重名的id
     */
    public Long checkName(String name) {
        Condition condition = new Condition(CardCategory.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("categoryName", name).andEqualTo("deleteFlag", DeleteFlagEnum.IS_NOT_DELETE.getCode());
        CardCategory milkCardCategory = cardCategoryMapper.selectOneByExample(condition);
        return Objects.isNull(milkCardCategory) ? null : milkCardCategory.getId();
    }

    public Long insert(CardCategory milkCardCategory) {
        cardCategoryMapper.insertSelective(milkCardCategory);
        return milkCardCategory.getId();
    }

    public Long update(CardCategory milkCardCategory) {
        milkCardCategory.setUpdateTime(new Date());
        cardCategoryMapper.updateByPrimaryKeySelective(milkCardCategory);
        return milkCardCategory.getId();
    }

    public Long updateAll(CardCategory milkCardCategory) {
        milkCardCategory.setUpdateTime(new Date());
        cardCategoryMapper.updateByPrimaryKey(milkCardCategory);
        return milkCardCategory.getId();
    }

    public int delete(Long id) {
        CardCategory cardCategory = new CardCategory();
        cardCategory.setId(id);
        cardCategory.setDeleteFlag(DeleteFlagEnum.IS_DELETE.getCode());
        cardCategory.setUpdateTime(new Date());
        return cardCategoryMapper.updateByPrimaryKeySelective(cardCategory);
    }
}
