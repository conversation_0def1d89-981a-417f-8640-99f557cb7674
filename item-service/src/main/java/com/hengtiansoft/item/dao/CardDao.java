package com.hengtiansoft.item.dao;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.hengtiansoft.common.constant.CardConstant;
import com.hengtiansoft.common.entity.vo.CardParentNoVO;
import com.hengtiansoft.common.enumeration.DeleteFlagEnum;
import com.hengtiansoft.common.util.DateUtil;
import com.hengtiansoft.item.entity.dto.*;
import com.hengtiansoft.item.entity.po.Card;
import com.hengtiansoft.item.entity.vo.CardCountCategoryVO;
import com.hengtiansoft.item.entity.vo.CardCountVO;
import com.hengtiansoft.item.entity.vo.CardRemainingCountVO;
import com.hengtiansoft.item.enumeration.CardStatusEnum;
import com.hengtiansoft.item.enumeration.CardTypeEnum;
import com.hengtiansoft.item.enumeration.CardUsageStatusEnum;
import com.hengtiansoft.item.mapper.CardMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.util.*;

/**
 * 奶卡dao
 *
 * <AUTHOR>
 * @date 2020/9/2 15:49
 */
@Repository
public class CardDao {

    @Value("${divisionDate:2022-02-20}")
    private String divisionDate;

    @Autowired
    private CardMapper cardMapper;

    public Card selectByPrimaryKey(Long id) {
        Condition condition = new Condition(Card.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("id", id).andEqualTo("deleteFlag", DeleteFlagEnum.IS_NOT_DELETE.getCode());
        return cardMapper.selectOneByExample(condition);
    }

    public List<Card> selectByPrimaryKeyList(Collection<Long> ids) {
        Condition condition = new Condition(Card.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andIn("id", ids).andEqualTo("deleteFlag", DeleteFlagEnum.IS_NOT_DELETE.getCode());
        return cardMapper.selectByExample(condition);
    }

    public Card selectByNumber(String number) {
        Condition condition = new Condition(Card.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("cardNumber", number).andEqualTo("deleteFlag", DeleteFlagEnum.IS_NOT_DELETE.getCode());
        return cardMapper.selectOneByExample(condition);
    }

    public Card selectByIdAndTime(Long id,Date time) {
        Condition condition = new Condition(Card.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("id", id);
        criteria.andEqualTo("updateTime", time);
        return cardMapper.selectOneByExample(condition);
    }

    public List<Card> selectByNumberList(List<String> numberList) {
        if(CollectionUtils.isEmpty(numberList)){
            return new ArrayList<>(0);
        }
        Condition condition = new Condition(Card.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andIn("cardNumber", numberList).andEqualTo("deleteFlag", DeleteFlagEnum.IS_NOT_DELETE.getCode());
        return cardMapper.selectByExample(condition);
    }

    public Long update(Card card) {
        if (Objects.nonNull(card.getExpirationTime())) {
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(card.getExpirationTime());
            calendar.set(Calendar.HOUR_OF_DAY, 23);
            calendar.set(Calendar.MINUTE, 59);
            calendar.set(Calendar.SECOND, 59);
            calendar.set(Calendar.MILLISECOND, 0);
            Date date = calendar.getTime();
            card.setExpirationTime(date);
        }
        cardMapper.updateByPrimaryKeySelective(card);
        return card.getId();
    }

    public void updateByIdNoUpdateTime(Card card){
        cardMapper.updateByPrimaryKeySelective(card);
    }

    public void updateByPrimaryKey(List<Long> cardIds, Card recode){
        Example example = new Example(Card.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andIn("id", cardIds);
        cardMapper.updateByExampleSelective(recode, example);
    }

    public void updateByCardNumber(Card card, String cardNumber) {
        Condition condition = new Condition(Card.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("cardNumber", cardNumber);
        cardMapper.updateByConditionSelective(card, condition);
    }

    public void updateByCardNumbers(Card card, List<String> cardNumber) {
        Condition condition = new Condition(Card.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andIn("cardNumber", cardNumber);
        criteria.andIsNull("invoiceTime");
        cardMapper.updateByConditionSelective(card, condition);
    }

    public Long insert(Card card) {
        if (Objects.nonNull(card.getExpirationTime())) {
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(card.getExpirationTime());
            calendar.set(Calendar.HOUR_OF_DAY, 23);
            calendar.set(Calendar.MINUTE, 59);
            calendar.set(Calendar.SECOND, 59);
            calendar.set(Calendar.MILLISECOND, 0);
            Date date = calendar.getTime();
            card.setExpirationTime(date);
        }
        cardMapper.insertSelective(card);
        return card.getId();
    }

    public int delete(Long id) {
        Card card = new Card();
        card.setId(id);
        card.setUpdateTime(new Date());
        card.setDeleteFlag(DeleteFlagEnum.IS_DELETE.getCode());
        return cardMapper.updateByPrimaryKeySelective(card);
    }

    public int pause(Long id) {
        Card card = new Card();
        card.setId(id);
        card.setUpdateTime(new Date());
        card.setUsageStatus(CardUsageStatusEnum.PENDING.getCode());
        return cardMapper.updateByPrimaryKeySelective(card);
    }

    public int activate(Long id) {
        Card card = new Card();
        card.setId(id);
        card.setUpdateTime(new Date());
        card.setUsageStatus(CardUsageStatusEnum.INSERVICE.getCode());
        return cardMapper.updateByPrimaryKeySelective(card);
    }

    public int batchInsert(List<Card> cardList) {
        return cardMapper.batchInsert(cardList);
    }

    public int batchUpdateStatus(List<Card> cardList) {
        return cardMapper.batchUpdateStatus(cardList);
    }

    /**
     * 条件查询
     *
     * @param dto
     * @return
     */
    public List<Card> findByCondition(CardListDTO dto) {
        Condition condition = new Condition(Card.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("deleteFlag", DeleteFlagEnum.IS_NOT_DELETE.getCode());
        if (Objects.nonNull(dto.getCategoryId())) {
            criteria.andEqualTo("categoryId", dto.getCategoryId());
        }
        if (CollectionUtils.isNotEmpty(dto.getCategoryIdList())) {
            criteria.andIn("categoryId", dto.getCategoryIdList());
        }
        if (StringUtils.isNotBlank(dto.getCardNumber())) {
            criteria.andEqualTo("cardNumber", dto.getCardNumber());
        }
        if (StringUtils.isNotBlank(dto.getUserPhone())) {
            criteria.andEqualTo("userPhone", dto.getUserPhone());
        }
        if (Objects.nonNull(dto.getUsageStatus())) {
            criteria.andEqualTo("usageStatus", dto.getUsageStatus()).andNotEqualTo("cardStatus", CardStatusEnum.VOIDED.getCode());
        }
        if (CollectionUtils.isNotEmpty(dto.getUsageStatusList())) {
            criteria.andIn("usageStatus", dto.getUsageStatusList()).andNotEqualTo("cardStatus", CardStatusEnum.VOIDED.getCode());
        }
        if (Objects.nonNull(dto.getCardStatus())) {
            criteria.andEqualTo("cardStatus", dto.getCardStatus());
        }
        if (CollectionUtils.isNotEmpty(dto.getCardStatusList())) {
            criteria.andIn("cardStatus", dto.getCardStatusList());
        }
        if (Objects.nonNull(dto.getReceiveStartTime())) {
            criteria.andGreaterThanOrEqualTo("receiveTime", dto.getReceiveStartTime());
        }
        if (Objects.nonNull(dto.getReceiveEndTime())) {
            criteria.andLessThanOrEqualTo("receiveTime", dto.getReceiveEndTime());
        }
        if (CollectionUtils.isNotEmpty(dto.getCardIds())) {
            criteria.andIn("id",dto.getCardIds());
        }
        if (Objects.nonNull(dto.getStartTime())&&Objects.nonNull(dto.getEndTime())) {
            criteria.andBetween("createTime",dto.getStartTime(),dto.getEndTime());
        }
        if (Objects.nonNull(dto.getCardType())) {
            criteria.andEqualTo("cardType",dto.getCardType());
        }

        // 奶卡领取列表
        if (dto.getUserFlag()) {
            criteria.andIsNotNull("userId").andNotEqualTo("cardStatus", CardStatusEnum.VOIDED.getCode());
            condition.orderBy("receiveTime").desc();
        } else {
            if (StringUtils.isNotBlank(dto.getUserPhone())) {
                // 用户奶卡列表
                condition.orderBy("activateTime").desc();
            } else {
                // 奶卡列表
                condition.orderBy("id").desc();
            }
        }
        return cardMapper.selectByExample(condition);
    }


    public int countCardPwd(CardPwdListDTO dto) {
        Condition condition = new Condition(Card.class);
        Example.Criteria criteria = condition.createCriteria();

        criteria.andEqualTo("deleteFlag", DeleteFlagEnum.IS_NOT_DELETE.getCode());
        condition.orderBy("id").desc();
        if (Objects.nonNull(dto.getCategoryId())) {
            criteria.andEqualTo("categoryId", dto.getCategoryId());
        }

        if (StringUtils.isNotBlank(dto.getCardNumber())) {
            criteria.andEqualTo("cardNumber", dto.getCardNumber());
        }

        if (Objects.nonNull(dto.getUsageStatus())) {
            criteria.andEqualTo("usageStatus", dto.getUsageStatus());
        }

        if (Objects.nonNull(dto.getCardStatus())) {
            criteria.andEqualTo("cardStatus", dto.getCardStatus());
        }

        if (Objects.nonNull(dto.getStartTime())&&Objects.nonNull(dto.getEndTime())) {
            criteria.andBetween("createTime",dto.getStartTime(),dto.getEndTime());
        }
        if (Objects.nonNull(dto.getCardType())) {
            criteria.andEqualTo("cardType",dto.getCardType());
        }
        return cardMapper.selectCountByCondition(condition);
    }

    public List<Card> findCardPwdList(CardPwdListDTO dto) {
        Condition condition = new Condition(Card.class);
        Example.Criteria criteria = condition.createCriteria();

        criteria.andEqualTo("deleteFlag", DeleteFlagEnum.IS_NOT_DELETE.getCode());
        condition.orderBy("id").desc();
        if (Objects.nonNull(dto.getCategoryId())) {
            criteria.andEqualTo("categoryId", dto.getCategoryId());
        }

        if (StringUtils.isNotBlank(dto.getCardNumber())) {
            criteria.andEqualTo("cardNumber", dto.getCardNumber());
        }

        if (Objects.nonNull(dto.getUsageStatus())) {
            criteria.andEqualTo("usageStatus", dto.getUsageStatus());
        }

        if (Objects.nonNull(dto.getCardStatus())) {
            criteria.andEqualTo("cardStatus", dto.getCardStatus());
        }

        if (Objects.nonNull(dto.getStartTime())&&Objects.nonNull(dto.getEndTime())) {
            criteria.andBetween("createTime",dto.getStartTime(),dto.getEndTime());
        }
        if (Objects.nonNull(dto.getCardType())) {
            criteria.andEqualTo("cardType",dto.getCardType());
        }

        return cardMapper.selectByExample(condition);
    }

    /**
     * 条件查询
     *
     * @param dto
     * @return
     */
    public int countByCondition(CardListDTO dto) {
        Condition condition = new Condition(Card.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("deleteFlag", DeleteFlagEnum.IS_NOT_DELETE.getCode());
        if (Objects.nonNull(dto.getCategoryId())) {
            criteria.andEqualTo("categoryId", dto.getCategoryId());
        }
        if (CollectionUtils.isNotEmpty(dto.getCategoryIdList())) {
            criteria.andIn("categoryId", dto.getCategoryIdList());
        }
        if (StringUtils.isNotBlank(dto.getCardNumber())) {
            criteria.andEqualTo("cardNumber", dto.getCardNumber());
        }
        if (StringUtils.isNotBlank(dto.getUserPhone())) {
            criteria.andEqualTo("userPhone", dto.getUserPhone());
        }
        if (Objects.nonNull(dto.getUsageStatus())) {
            criteria.andEqualTo("usageStatus", dto.getUsageStatus()).andNotEqualTo("cardStatus", CardStatusEnum.VOIDED.getCode());
        }
        if (CollectionUtils.isNotEmpty(dto.getUsageStatusList())) {
            criteria.andIn("usageStatus", dto.getUsageStatusList()).andNotEqualTo("cardStatus", CardStatusEnum.VOIDED.getCode());
        }
        if (Objects.nonNull(dto.getCardStatus())) {
            criteria.andEqualTo("cardStatus", dto.getCardStatus());
        }
        if (CollectionUtils.isNotEmpty(dto.getCardStatusList())) {
            criteria.andIn("cardStatus", dto.getCardStatusList());
        }
        if (Objects.nonNull(dto.getReceiveStartTime())) {
            criteria.andGreaterThanOrEqualTo("receiveTime", dto.getReceiveStartTime());
        }
        if (Objects.nonNull(dto.getReceiveEndTime())) {
            criteria.andLessThanOrEqualTo("receiveTime", dto.getReceiveEndTime());
        }
        if (CollectionUtils.isNotEmpty(dto.getCardIds())) {
            criteria.andIn("id",dto.getCardIds());
        }
        if (Objects.nonNull(dto.getStartTime())&&Objects.nonNull(dto.getEndTime())) {
            criteria.andBetween("createTime",dto.getStartTime(),dto.getEndTime());
        }
        if (Objects.nonNull(dto.getCardType())) {
            criteria.andEqualTo("cardType",dto.getCardType());
        }

        // 奶卡领取列表
        if (dto.getUserFlag()) {
            criteria.andIsNotNull("userId").andNotEqualTo("cardStatus", CardStatusEnum.VOIDED.getCode());
        }
        return cardMapper.selectCountByExample(condition);
    }

    /**
     * 查询用户未过期、未作废/未冻结奶卡
     *
     * @param userId
     * @return
     */
    public List<Card> findByUserWithEffect(Long userId) {
        Condition condition = new Condition(Card.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("userId", userId).andEqualTo("deleteFlag", DeleteFlagEnum.IS_NOT_DELETE.getCode())
                .andNotIn("usageStatus", CardUsageStatusEnum.getUnEffected()).andNotIn("cardStatus", Arrays.asList(CardStatusEnum.VOIDED.getCode(), CardStatusEnum.FROZEN.getCode()));
        criteria.andIn("cardType",Arrays.asList(CardTypeEnum.ENTITY_CARD.getCode(),CardTypeEnum.ELECTRONIC_CARD.getCode()));
        condition.orderBy("receiveTime").desc();
        return cardMapper.selectByExample(condition);
    }

    /**
     * 查询用户已订完奶卡（服务完成/已过期、未作废/未冻结）
     *
     * @param userId
     * @return
     */
    public List<Card> findByUserWithFinished(Long userId) {
        Condition condition = new Condition(Card.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("userId", userId).andEqualTo("deleteFlag", DeleteFlagEnum.IS_NOT_DELETE.getCode())
                .andNotIn("cardStatus", Arrays.asList(CardStatusEnum.VOIDED.getCode(), CardStatusEnum.FROZEN.getCode()))
                .andIn("usageStatus", CardUsageStatusEnum.getUnEffected());
        criteria.andIn("cardType",Arrays.asList(CardTypeEnum.ENTITY_CARD.getCode(),CardTypeEnum.ELECTRONIC_CARD.getCode()));
        condition.orderBy("expirationTime").desc();
        return cardMapper.selectByExample(condition);
    }

    /**
     *
     *
     * @param userId
     * @return
     */
    public List<Card> findByUserWithInUse(Long userId) {
        Condition condition = new Condition(Card.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("userId", userId).andEqualTo("deleteFlag", DeleteFlagEnum.IS_NOT_DELETE.getCode())
                .andIn("cardStatus", Arrays.asList(CardStatusEnum.SPENDABLE.getCode(), CardStatusEnum.USED.getCode()))
                .andIn("usageStatus", Arrays.asList(CardUsageStatusEnum.INSERVICE.getCode(), CardUsageStatusEnum.PENDING.getCode()));
        condition.orderBy("expirationTime").desc();
        return cardMapper.selectByExample(condition);
    }

    public List<Card> findByUserWithInUseBefore(Long userId, Integer graceDay) {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DATE, 0 - graceDay);
        Condition condition = new Condition(Card.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("userId", userId).andEqualTo("deleteFlag", DeleteFlagEnum.IS_NOT_DELETE.getCode())
                .andIn("cardStatus", Arrays.asList(CardStatusEnum.SPENDABLE.getCode(), CardStatusEnum.USED.getCode()))
                .andIn("usageStatus", Arrays.asList(CardUsageStatusEnum.INSERVICE.getCode(), CardUsageStatusEnum.PENDING.getCode()))
        .andLessThan("receiveTime",calendar.getTime());
        return cardMapper.selectByExample(condition);
    }


    /**
     * 根据卡号卡密查询记录
     *
     * @param card
     * @return
     */
    public Card findByNumberAndPassword(Card card) {
        Condition condition = new Condition(Card.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("cardNumber", card.getCardNumber()).andEqualTo("cardPassword", card.getCardPassword())
                .andEqualTo("deleteFlag", DeleteFlagEnum.IS_NOT_DELETE.getCode());
        return cardMapper.selectOneByExample(condition);
    }

    /**
     * 按照使用状态计数
     *
     * @param dto
     * @return
     */
    public List<CardCountVO> countByStatus(CardListDTO dto) {
        return cardMapper.countByStatus(dto);
    }

    /**
     * 校验卡号重复
     *
     * @param number
     * @return
     */
    public Long checkNumber(String number) {
        Condition condition = new Condition(Card.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("cardNumber", number).andEqualTo("deleteFlag", DeleteFlagEnum.IS_NOT_DELETE.getCode());
        Card card = cardMapper.selectOneByExample(condition);
        return Objects.isNull(card) ? null : card.getId();
    }

    /**
     * 查询卡号
     *
     * @param numbers
     * @return
     */
    public List<Card> findByNumbers(List<String> numbers) {
        Condition condition = new Condition(Card.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andIn("cardNumber", numbers).andEqualTo("deleteFlag", DeleteFlagEnum.IS_NOT_DELETE.getCode());
        return cardMapper.selectByExample(condition);
    }

    /**
     * 查询实体卡号
     *
     * @param numbers
     * @return
     */
    public List<Card> findEntityCardByNumbers(List<String> numbers) {
        Condition condition = new Condition(Card.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andIn("cardNumber", numbers);
        criteria.andEqualTo("deleteFlag", DeleteFlagEnum.IS_NOT_DELETE.getCode());
        criteria.andEqualTo("cardType", CardTypeEnum.ENTITY_CARD);
        return cardMapper.selectByExample(condition);
    }

    public List<Card> findByOrderNos(List<String> orderNos) {
        if(CollectionUtils.isEmpty(orderNos)){
            return new ArrayList<>();
        }
        Condition condition = new Condition(Card.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andIn("orderNo", orderNos);
        return cardMapper.selectByExample(condition);
    }

    /**
     * 校验类别下有无奶卡
     *
     * @param name
     * @return
     */
    public Boolean checkRelevance(String name) {
        Condition condition = new Condition(Card.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("categoryName", name).andEqualTo("deleteFlag", DeleteFlagEnum.IS_NOT_DELETE.getCode());
        List<Card> cardList = cardMapper.selectByExample(condition);
        return CollectionUtils.isNotEmpty(cardList);
    }

    /**
     * 获取过期奶卡
     *
     * @return
     */
    public List<Card> findCancelCards() {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        Date date = DateUtil.getDateAfterDays(calendar.getTime(), CardConstant.MUNUS_ONE);
        Condition condition = new Condition(Card.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("deleteFlag", DeleteFlagEnum.IS_NOT_DELETE.getCode()).andNotIn("usageStatus", Arrays.asList(CardUsageStatusEnum.EXPIRED.getCode(),CardUsageStatusEnum.SERVICECOMPLETED.getCode()))
                .andIsNotNull("expirationTime").andBetween("expirationTime", date, DateUtil.getDateAfterDays(date, CardConstant.ONE));
        return cardMapper.selectByExample(condition);
    }

    /**
     * 统计激活奶卡数
     *
     * @param dateFlag
     * @return
     */
    public BigDecimal countActivateCards(boolean dateFlag) {
        Condition condition = new Condition(Card.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andIsNotNull("activateTime");
        if (dateFlag) {
            Calendar calendar = Calendar.getInstance();
            calendar.set(Calendar.HOUR_OF_DAY, 0);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.SECOND, 0);
            calendar.set(Calendar.MILLISECOND, 0);
            Date now = calendar.getTime();
            Date before = DateUtil.getDateAfterDays(now, CardConstant.MONTH_AGO);
            criteria.andGreaterThanOrEqualTo("activateTime", before).andLessThanOrEqualTo("activateTime", now);
        }
        return new BigDecimal(cardMapper.selectCountByExample(condition));
    }

    /**
     * 统计剩余0提的用户
     *
     * @return
     */
    public List<CardRemainingCountVO> countRemainingCount() {
        return cardMapper.countRemainingCount();
    }

    public int countRemainingCountSum() {
        return cardMapper.countRemainingCountSum();
    }

    public List<CardRemainingCountVO> countRemainingByDate(String date) {
        return cardMapper.countRemainingByDate(date);
    }

    /**
     * 统计持卡状态
     *
     * @param usageStatus
     * @return
     */
    public BigDecimal countUsageStatus(Integer usageStatus) {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        Date now = calendar.getTime();
        Date before = DateUtil.getDateAfterDays(now, CardConstant.MONTH_AGO);

        Condition condition = new Condition(Card.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("deleteFlag", DeleteFlagEnum.IS_NOT_DELETE.getCode()).andNotEqualTo("cardStatus", CardStatusEnum.VOIDED.getCode())
                .andIsNotNull("receiveTime").andBetween("receiveTime", before, now)
                .andNotEqualTo("usageStatus", CardUsageStatusEnum.UNRECEIVED.getCode());
        if (Objects.nonNull(usageStatus)) {
            criteria.andEqualTo("usageStatus", usageStatus);
        }
        return new BigDecimal(cardMapper.selectCountByExample(condition));
    }

    /**
     * 统计使用完成比例
     *
     * @param dto
     * @return
     */
    public List<CardCountCategoryVO> countCategory(CardCountCategoryDTO dto) {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        Date now = calendar.getTime();
        Date before = DateUtil.getDateAfterDays(now, CardConstant.MONTH_AGO);
        dto.setActivateStartTime(before);
        dto.setActivateEndTime(now);
        return cardMapper.countCategory(dto);
    }

    /**
     * 统计过期奶卡数量
     *
     * @param days       N天内
     * @param userIdList
     * @return
     */
    public List<Card> findExpiration(Integer days, List<Long> userIdList) {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        Date now = calendar.getTime();
        Date before = DateUtil.getDateAfterDays(now, days);

        Condition condition = new Condition(Card.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("deleteFlag", DeleteFlagEnum.IS_NOT_DELETE.getCode()).andNotEqualTo("cardStatus", CardStatusEnum.VOIDED.getCode());
        if (CollectionUtils.isNotEmpty(userIdList)) {
            criteria.andIn("userId", userIdList);
            if (days != CardConstant.ZERO) {
                criteria.andBetween("receiveTime", before, now);
            }
        } else {
            criteria.andEqualTo("usageStatus", CardUsageStatusEnum.EXPIRED.getCode());
            if (days != CardConstant.ZERO) {
                criteria.andBetween("expirationTime", before, now);
            }
        }
        return cardMapper.selectByExample(condition);
    }

    /**
     * 查询剩余0提用户奶卡
     *
     * @return
     */
    public List<Card> findInHome(List<Long> userIdList,String date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(DateUtil.stringToDate(date,DateUtil.SIMPLE_YMD));
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        Date start = calendar.getTime();
        Date end = DateUtil.setDefaultEndDate(start);

        Condition condition = new Condition(Card.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("deleteFlag", DeleteFlagEnum.IS_NOT_DELETE.getCode()).andNotEqualTo("cardStatus", CardStatusEnum.VOIDED.getCode())
                .andIn("userId", userIdList)
                .andBetween("finishTime", start, end).andEqualTo("remainingCount", CardConstant.ZERO);
        return cardMapper.selectByExample(condition);
    }

    public List<Card> findCards() {
        Condition condition = new Condition(Card.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andNotEqualTo("userPhone", "");
        criteria.andGreaterThan("userId", 0);
        return cardMapper.selectByExample(condition);
    }

    public List<Card> findFixPlanByNumbers() {
        Condition condition = new Condition(Card.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andGreaterThan("userId", 0);
        criteria.andEqualTo("deleteFlag", DeleteFlagEnum.IS_NOT_DELETE.getCode());
        return cardMapper.selectByExample(condition);
    }


    public List<Card> findByUserIds(List<Long> userIds) {
        Condition condition = new Condition(Card.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andIn("userId", userIds);
        criteria.andEqualTo("deleteFlag", DeleteFlagEnum.IS_NOT_DELETE.getCode());
        criteria.andIn("cardStatus", Arrays.asList(CardStatusEnum.SPENDABLE.getCode(), CardStatusEnum.USED.getCode(), CardStatusEnum.FROZEN.getCode()));
        criteria.andIn("cardType", Arrays.asList(CardTypeEnum.ENTITY_CARD.getCode(), CardTypeEnum.ELECTRONIC_CARD.getCode()));
        return cardMapper.selectByExample(condition);
    }

    public int updateByExample(Card record, Date now, Long id) {
        Condition condition = new Condition(Card.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("updateTime", now);
        criteria.andEqualTo("id", id);
        return cardMapper.updateByExampleSelective(record, condition);
    }

    public Card selectByIdAndUpdateTime(Long id, Date updateTime) {
        Condition condition = new Condition(Card.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("id", id);
        criteria.andEqualTo("updateTime", updateTime);
        return cardMapper.selectOneByExample(condition);
    }

    /**
     * 拷贝表
     */
    public void backupsTable(String newTableName, String oldTableName) {
        cardMapper.createTable(newTableName, oldTableName);
        Long number = cardMapper.countNumber(oldTableName);
        //每次复制3w条
        Long limitNum = 30000L;
        //需要复制的次数
        Long num = number / limitNum;
        Long id = cardMapper.getTableId(oldTableName, 0L);
        for (int i = 0; i <= num; i++) {
            if (i == num) {
                cardMapper.backupsData(newTableName, oldTableName, id, number % limitNum);
            } else {
                cardMapper.backupsData(newTableName, oldTableName, id, limitNum);
            }
            id = cardMapper.getTableId(oldTableName, (i + 1) * limitNum);
        }
    }

    /**
     * 根据卡号和查询日期 从静态备份表/拉链表 查询奶卡
     */
    @DS("db2")
    public List<Card> selectByCardNumbersInBackup(String date, List<String> cardNumbers, Date searchDate) {
        if (CollectionUtils.isNotEmpty(cardNumbers)) {
            Date divisionTime = DateUtil.stringToDate(divisionDate, DateUtil.SIMPLE_YMD);
            if(searchDate.before(divisionTime)){
                //备份表
                return cardMapper.selectByCardNumbersInBackup(date, cardNumbers);
            }else{
                //拉链表
                return cardMapper.selectByCardNumbersInZipper(date, cardNumbers);
            }
        }
        return new ArrayList<>();
    }

    /**
     * 根据卡号从 拉链表 查询奶卡
     */
    @DS("db2")
    public List<Card> selectByCardNumbersInZipper(String date, List<String> cardNumbers) {
        if (CollectionUtils.isNotEmpty(cardNumbers)) {
            return cardMapper.selectByCardNumbersInZipper(date, cardNumbers);
        }
        return new ArrayList<>();
    }

    /**
     * 展示所有备份表
     *
     * @return
     */
    @DS("db2")
    public List<String> showAllTables() {
        return cardMapper.showAllTables();
    }

    public void updateAdjustAmountInBackup(String date,CardAmountUpdateDTO dto){
         cardMapper.updateAdjustAmountInBackup(date,dto);
    }


    public List<CardFixDTO> findCardRemainingDifferent(CardFixJobParamDTO jobParam) {
        return cardMapper.findCardRemainingDifferent(jobParam);
    }

    public int countUserBindCardInYear(String phone, Date begDate, Date endDate){
        Condition condition = new Condition(Card.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("userPhone", phone);
        criteria.andGreaterThanOrEqualTo("receiveTime", begDate);
        criteria.andLessThanOrEqualTo("receiveTime", endDate);
        criteria.andNotEqualTo("cardStatus", CardStatusEnum.VOIDED.getCode());
        criteria.andEqualTo("deleteFlag", DeleteFlagEnum.IS_NOT_DELETE.getCode());
        criteria.andEqualTo("cardType", CardTypeEnum.ENTITY_CARD.getCode());
        return cardMapper.selectCountByExample(condition);
    }

    public List<CardParentNoVO> findCardWithParentNo(List<String> cardNumberList) {
        return cardMapper.findCardWithParentNo(cardNumberList);
    }

    public Integer countRemainingCountByPhone(String phone){
        return cardMapper.countRemainingCountByPhone(phone);
    }

    public List<Card> selectCardByPhone(String phone){
        Condition condition = new Condition(Card.class, true, true);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("userPhone", phone);
        criteria.andNotEqualTo("cardStatus", CardStatusEnum.VOIDED.getCode());
        criteria.andEqualTo("deleteFlag", DeleteFlagEnum.IS_NOT_DELETE.getCode());
        return cardMapper.selectByExample(condition);
    }

    public List<Card> selectCardByPhoneAndReceiveTime(String phone, Date date){
        Condition condition = new Condition(Card.class, true, true);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("userPhone", phone);
        criteria.andNotEqualTo("cardStatus", CardStatusEnum.VOIDED.getCode());
        criteria.andEqualTo("deleteFlag", DeleteFlagEnum.IS_NOT_DELETE.getCode());
        criteria.andGreaterThanOrEqualTo("receiveTime", date);
        return cardMapper.selectByExample(condition);
    }

    public List<String> findPlanRemindUserPhone(Date date){
        return cardMapper.findPlanRemindUserPhone(date);
    }

    public int countSumRemainCountByPhone(String userPhone) {
        return cardMapper.countSumRemainCountByPhone(userPhone);
    }

    public int countSumCardCountByPhone(String userPhone) {
        return cardMapper.countSumCardCountByPhone(userPhone);
    }

    public Card findFirstByUserIdAndCategoryIds(Long userId, List<Long> categoryIds){
        if(CollectionUtils.isEmpty(categoryIds)){
            return null;
        }
        return cardMapper.findFirstByUserIdAndCategoryIds(userId, categoryIds);
    }

    public List<Card> findUpgradeByCategoryIds(List<Long> categoryIds, List<Long> userIds){
        if(CollectionUtils.isEmpty(categoryIds) || CollectionUtils.isEmpty(userIds)){
            return new ArrayList<>();
        }
        Condition condition = new Condition(Card.class, true, true);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andIn("categoryId", categoryIds);
        criteria.andIn("userId", userIds);
        criteria.andNotIn("cardStatus", Arrays.asList(CardStatusEnum.FROZEN.getCode(), CardStatusEnum.VOIDED.getCode()));
        criteria.andNotIn("usageStatus", Arrays.asList(CardUsageStatusEnum.SERVICECOMPLETED.getCode(), CardUsageStatusEnum.EXPIRED.getCode()));
        criteria.andGreaterThan("remainingCount", 0);
        criteria.andIsNotNull("receiveTime");
        return cardMapper.selectByExample(condition);
    }

    @DS("db3")
    public List<Card> selectMaxReceiveTimeByUserIds(List<Long> userIds, Date startTime, Date endTime){
        if(CollectionUtils.isEmpty(userIds)){
            return new ArrayList<>();
        }
        return cardMapper.selectMaxReceiveTimeByUserIds(userIds, startTime, endTime);
    }

    @DS("db3")
    public List<String> selectByPhones(List<String> phones){
        if(CollectionUtils.isEmpty(phones)){
            return new ArrayList<>();
        }
        return cardMapper.selectByPhones(phones);
    }

    @DS("db3")
    public List<Card> findByPageDTO(CardDataDTO dto) {
        Condition condition = new Condition(Card.class);
        Example.Criteria criteria = condition.createCriteria();
        if (Objects.nonNull(dto.getUserId())) {
            criteria.andEqualTo("userId", dto.getUserId());
        }
        if(Objects.nonNull(dto.getStartUpdateTime())){
            criteria.andGreaterThanOrEqualTo("updateTime", dto.getStartUpdateTime());
        }
        if(Objects.nonNull(dto.getEndUpdateTime())){
            criteria.andLessThanOrEqualTo("updateTime", dto.getEndUpdateTime());
        }
        criteria.andEqualTo("deleteFlag", DeleteFlagEnum.IS_NOT_DELETE.getCode());
        criteria.andGreaterThan("userId", 0);
        condition.orderBy("updateTime").asc();
        return cardMapper.selectByCondition(condition);
    }
}
