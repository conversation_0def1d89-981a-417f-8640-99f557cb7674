package com.hengtiansoft.item.dao;


import com.hengtiansoft.common.enumeration.DeleteFlagEnum;
import com.hengtiansoft.item.entity.dto.GroupTemplateSearchDTO;
import com.hengtiansoft.item.entity.po.GroupTemplate;
import com.hengtiansoft.item.mapper.GroupTemplateMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tk.mybatis.mapper.entity.Example;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

@Component
public class GroupTemplateDao {

    @Autowired
    private GroupTemplateMapper groupTemplateMapper;

    public List<GroupTemplate> selectByCondition(GroupTemplateSearchDTO condition) {
        Example example = new Example(GroupTemplate.class);
        Example.Criteria criteria = example.createCriteria();
        if (StringUtils.isNotBlank(condition.getFuzzyName())) {
            criteria.andLike("name", "%" + condition.getFuzzyName().trim() + "%");
        }

        if (StringUtils.isNotBlank(condition.getName())) {
            criteria.andEqualTo("name", condition.getName());
        }

        if (Objects.nonNull(condition.getNonId())) {
            criteria.andNotEqualTo("id", condition.getNonId());
        }

        if (Objects.nonNull(condition.getId())) {
            criteria.andEqualTo("id", condition.getId());
        }

        if (CollectionUtils.isNotEmpty(condition.getIds())) {
            criteria.andIn("id", condition.getIds());
        }

        if (Objects.nonNull(condition.getPromoVisible())) {
            criteria.andEqualTo("promoVisible", condition.getPromoVisible());
        }
        criteria.andEqualTo("delflag", DeleteFlagEnum.IS_NOT_DELETE.getCode());
        example.setOrderByClause("update_time desc");
        return groupTemplateMapper.selectByExample(example);
    }


    public void insert(GroupTemplate groupTemplate) {
        groupTemplateMapper.insertSelective(groupTemplate);
    }

    public void update(GroupTemplate groupTemplate) {
        groupTemplateMapper.updateByPrimaryKeySelective(groupTemplate);
    }

    public GroupTemplate findById(Long id){
        return groupTemplateMapper.selectByPrimaryKey(id);
    }

    public List<GroupTemplate> findByIds(List<Long> ids){
        if(CollectionUtils.isEmpty(ids)){
            return Collections.emptyList();
        }
        Example example = new Example(GroupTemplate.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andIn("id", ids);
        criteria.andEqualTo("delflag", DeleteFlagEnum.IS_NOT_DELETE.getCode());
        return groupTemplateMapper.selectByExample(example);
    }
}
