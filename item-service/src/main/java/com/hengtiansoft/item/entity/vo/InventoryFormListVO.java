package com.hengtiansoft.item.entity.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;


@Data
@ApiModel("进销存报表VO")
@ContentRowHeight(15) //内容行高
@HeadRowHeight(20)//表头行高
@ColumnWidth(25)
@ExcelIgnoreUnannotated
public class InventoryFormListVO {

    @ExcelProperty(value = "奶卡名称", index = 0)
    @ApiModelProperty("奶卡名称")
    private String productName;

    @ExcelProperty(value = "奶卡卡号", index = 1)
    @ApiModelProperty("奶卡卡号")
    private String cardCode;

    @ExcelProperty(value = "旺店通订单号", index = 2)
    @ApiModelProperty("旺店通订单号")
    private String orderNo;


    @ExcelProperty(value = "原始单号", index = 3)
    @ApiModelProperty("原始订单号")
    private String srcNo;


    @ExcelProperty(value = "旺店通奶卡物料编码", index = 4)
    @ApiModelProperty("旺店通奶卡物料编码")
    private String skuCode;

    @ExcelProperty(value = "物料描述", index = 5)
    @ApiModelProperty("物料描述")
    private String skuDesc;

    @ExcelProperty(value = "渠道名称", index = 6)
    @ApiModelProperty("渠道名称")
    private String shopName;

    @ExcelProperty(value = "渠道编号", index = 7)
    @ApiModelProperty("渠道编号")
    private String shopNo;

    @ExcelProperty(value = "发货时间", index = 8)
    @ApiModelProperty("发货时间string")
    private String deliveryTimeString;

    @ExcelProperty(value = "奶卡使用状态", index = 9)
    @ApiModelProperty("使用状态[string]")
    private String usageStatusDesc;

    @ExcelProperty(value = "奶卡状态", index = 10)
    @ApiModelProperty("奶卡状态[string]")
    private String cardstatusString;

    @ExcelProperty(value = "对账金额", index = 11)
    @ApiModelProperty("对账金额")
    private BigDecimal reconciliationAmount;

    @ExcelProperty(value = "对账时间", index = 12)
    @ApiModelProperty("对账时间[string]")
    private String reconciliationDateString;

    @ApiModelProperty("对账时间")
    private Date reconciliationDate;


    @ExcelProperty(value = "期初提数", index = 13)
    @ApiModelProperty("期初提数")
    private Integer startCount;


    @ExcelProperty(value = "期初提奶金额", index = 14)
    @ApiModelProperty("期初提奶金额")
    private BigDecimal startAmount;


    @ExcelProperty(value = "本期增加提数", index = 15)
    @ApiModelProperty("本月增加提数")
    private Integer addCount;

    @ExcelProperty(value = "本期增加提数金额", index = 16)
    @ApiModelProperty("本期增加提数金额")
    private BigDecimal addAmount;

    @ExcelProperty(value = "本期减少提数", index = 17)
    @ApiModelProperty("本期减少提数")
    private Integer reduceCount;

    @ExcelProperty(value = "本期减少提数金额", index = 18)
    @ApiModelProperty("本期减少提数金额")
    private BigDecimal reduceAmount;


    @ExcelProperty(value = "调整金额", index = 19)
    @ApiModelProperty("调整金额")
    private BigDecimal adjustAmount;


    @ExcelProperty(value = "期末提数", index = 20)
    @ApiModelProperty("期末提数")
    private Integer endCount;


    @ExcelProperty(value = "期末提数金额", index = 21)
    @ApiModelProperty("期末提数金额")
    private BigDecimal endAmount;


    @ExcelProperty(value = "冲抵金额", index = 22)
    @ApiModelProperty("冲抵金额")
    private BigDecimal writeOffAmount;

    @ExcelProperty(value = "是否补发单", index = 23)
    @ApiModelProperty("订单类型（1-正常订单，2-补发订单")
    private String orderInfoTypeStr;

    @ApiModelProperty("计划剩余未提数")
    private Integer remainingCount;


    @ApiModelProperty("计划剩余金额字段")
    private BigDecimal remainingAmount;


    @ApiModelProperty("奶卡总提数")
    private Integer cardCount;

    @ApiModelProperty("奶卡总金额")
    private BigDecimal cardAmount;

    @ApiModelProperty("实际剩余未提数")
    private Integer realRemainingCount;

    @ApiModelProperty("实际剩余金额字段")
    private BigDecimal realRemainingAmount;

    @ApiModelProperty("订单总金额")
    private BigDecimal realAmount;

    @ApiModelProperty("发货时间")
    private Date deliveryTime;

    @ApiModelProperty("支付时间")
    private Date payTime;

    @ApiModelProperty("下单商品类型（1-电子卡，2-实体卡）")
    private Integer productType;

    @ApiModelProperty("使用状态（0-未领取，1-未使用，2-服务中，3-暂停中，4-服务完成，5-已过期）")
    private Integer usageStatus;

    @ApiModelProperty("奶卡状态")
    private Integer cardstatus;

    @ApiModelProperty("金额变更id")
    private Long changeAountId;

    @ApiModelProperty("订单类型（1-正常订单，2-补发订单")
    private Integer orderInfoType;

    @ApiModelProperty("来源平台")
    private String gwSourceGroup;

    @ApiModelProperty("创建时间")
    private Date createTime;

}
