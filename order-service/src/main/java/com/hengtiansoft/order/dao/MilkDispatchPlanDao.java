package com.hengtiansoft.order.dao;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.hengtiansoft.common.entity.exception.BusinessException;
import com.hengtiansoft.common.enumeration.DeleteFlagEnum;
import com.hengtiansoft.common.util.BeanUtils;
import com.hengtiansoft.common.util.DataUtil;
import com.hengtiansoft.common.util.DateUtil;
import com.hengtiansoft.common.util.StreamUtils;
import com.hengtiansoft.item.entity.dto.MonthlyPlanFormListDTO;
import com.hengtiansoft.item.entity.dto.ReportFormExportBackupDTO;
import com.hengtiansoft.item.entity.po.MonthlyPlanFormList;
import com.hengtiansoft.order.entity.dto.*;
import com.hengtiansoft.order.entity.mapper.MilkDispatchPlanMapper;
import com.hengtiansoft.order.entity.po.MilkDispatchPlan;
import com.hengtiansoft.order.entity.vo.MilkAmountVO;
import com.hengtiansoft.order.entity.vo.PlanedSkuAmountVO;
import com.hengtiansoft.order.enums.PlanBillFlagEnum;
import com.hengtiansoft.order.enums.PlanStatusEnum;
import com.hengtiansoft.order.enums.PushFlagEnum;
import com.hengtiansoft.order.enums.TmSmsFlagEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;

/**
 * @Author: <EMAIL>
 */
@Repository
public class MilkDispatchPlanDao {

    @Value("${divisionDate:2022-02-20}")
    private String divisionDate;

    @Resource
    private MilkDispatchPlanMapper milkDispatchPlanMapper;

    public void insert(MilkDispatchPlan record) {
        replaceBlank(record);
        milkDispatchPlanMapper.insertSelective(record);
    }

    public void batchInsert(List<MilkDispatchPlan> records) {
        milkDispatchPlanMapper.batchInsert(records);
    }

    public MilkDispatchPlan findById(Long id) {
        Condition condition = new Condition(MilkDispatchPlan.class, true, true);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("id", id);
        criteria.andEqualTo("delflag", DeleteFlagEnum.IS_NOT_DELETE.getCode());
        return milkDispatchPlanMapper.selectOneByExample(condition);
    }

    public List<MilkDispatchPlan> findByRuleId(Long ruleId) {
        Example example = new Example(MilkDispatchPlan.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("ruleId", ruleId);
        criteria.andEqualTo("delflag", DeleteFlagEnum.IS_NOT_DELETE.getCode());
        return milkDispatchPlanMapper.selectByExample(example);
    }

    public List<MilkDispatchPlan> findByRuleIdOrderByDispatchDate(Long ruleId) {
        Example example = new Example(MilkDispatchPlan.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("ruleId", ruleId);
        criteria.andEqualTo("delflag", DeleteFlagEnum.IS_NOT_DELETE.getCode());
        example.orderBy("dispatchDate").desc();
        return milkDispatchPlanMapper.selectByExample(example);
    }

    public List<MilkDispatchPlan> findByRuleIdAndStatus(Long ruleId, List<Integer> planStatus) {
        Example example = new Example(MilkDispatchPlan.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("ruleId", ruleId);
        criteria.andEqualTo("delflag", DeleteFlagEnum.IS_NOT_DELETE.getCode());
        criteria.andIn("planStatus", planStatus);
        return milkDispatchPlanMapper.selectByExample(example);
    }

    public List<MilkDispatchPlan> findAllPlanByStatus(List<Integer> planStatus) {
        Example example = new Example(MilkDispatchPlan.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("delflag", DeleteFlagEnum.IS_NOT_DELETE.getCode());
        criteria.andIn("planStatus", planStatus);
        return milkDispatchPlanMapper.selectByExample(example);
    }

    public List<MilkDispatchPlan> findAllPlanByStatusRuleId(List<Integer> planStatus,Long ruleId) {
        Example example = new Example(MilkDispatchPlan.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("ruleId",ruleId);
        criteria.andEqualTo("delflag", DeleteFlagEnum.IS_NOT_DELETE.getCode());
        criteria.andIn("planStatus", planStatus);
        return milkDispatchPlanMapper.selectByExample(example);
    }

    public List<MilkDispatchPlan> findByRuleIds(List<Long> ruleIds) {
        if(CollectionUtils.isEmpty(ruleIds)){
            return Collections.emptyList();
        }
        Example example = new Example(MilkDispatchPlan.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("delflag", DeleteFlagEnum.IS_NOT_DELETE.getCode());
        criteria.andIn("ruleId", ruleIds);
        return milkDispatchPlanMapper.selectByExample(example);
    }

    public List<MilkDispatchPlan> findByIds(List<Long> ids) {
        Example example = new Example(MilkDispatchPlan.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("delflag", DeleteFlagEnum.IS_NOT_DELETE.getCode());
        criteria.andIn("id", ids);
        return milkDispatchPlanMapper.selectByExample(example);
    }

    public List<MilkDispatchPlan> findByCardNumber(String cardNumber) {
        Example example = new Example(MilkDispatchPlan.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("cardNumber", cardNumber);
        criteria.andEqualTo("delflag", DeleteFlagEnum.IS_NOT_DELETE.getCode());
        return milkDispatchPlanMapper.selectByExample(example);
    }

    public List<MilkDispatchPlan> findByPlanOrderNo(String planOrderNo) {
        Example example = new Example(MilkDispatchPlan.class, true, true);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("planOrderNo", planOrderNo);
        criteria.andEqualTo("delflag", DeleteFlagEnum.IS_NOT_DELETE.getCode());
        return milkDispatchPlanMapper.selectByExample(example);
    }

    public List<MilkDispatchPlan> findByCardAndOrder(String cardNumber, String planOrderNo) {
        Example example = new Example(MilkDispatchPlan.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("cardNumber", cardNumber);
        criteria.andEqualTo("planOrderNo", planOrderNo);
        criteria.andEqualTo("delflag", DeleteFlagEnum.IS_NOT_DELETE.getCode());
        return milkDispatchPlanMapper.selectByExample(example);
    }

    public List<MilkDispatchPlan> findByCardNumbers(Collection<String> cardNumbers) {
        Example example = new Example(MilkDispatchPlan.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("delflag", DeleteFlagEnum.IS_NOT_DELETE.getCode());
        criteria.andIn("cardNumber", cardNumbers);
        return milkDispatchPlanMapper.selectByExample(example);
    }

    public List<MilkDispatchPlan> findByCardNumbersInBackup(String date, List<String> cardNumbers) {
        return milkDispatchPlanMapper.findByCardNumbersInBackup(date, cardNumbers);
    }


    public List<MilkDispatchPlan> findByCardNumbersAndTime(Collection<String> cardNumbers,Date SearchTime) {
        Example example = new Example(MilkDispatchPlan.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("delflag", DeleteFlagEnum.IS_NOT_DELETE.getCode());
        criteria.andIn("cardNumber", cardNumbers);
        criteria.andLessThan("deliveryTime",SearchTime);
        return milkDispatchPlanMapper.selectByExample(example);
    }

    @DS("db2")
    public List<MilkDispatchPlan> findByCardNumbersAndTime(String date, Date searchTimeStart, Collection<String> cardNumbers) {
        Date divisionTime = DateUtil.stringToDate(divisionDate, DateUtil.SIMPLE_YMD);
        if(searchTimeStart.before(divisionTime)){
            //备份表
            return milkDispatchPlanMapper.selectLastDayOfLastMonthInBackup(date,searchTimeStart,cardNumbers);
        }else {
            //拉链表
            return milkDispatchPlanMapper.selectLastDayOfLastMonthInZipper(date,searchTimeStart,cardNumbers);
        }
    }

    @DS("db2")
    public List<MilkDispatchPlan> findByCardNumbersAndSearchTime(String date,Collection<String> cardNumbers,Date searchTimeStart,Date searchTimeEnd) {
        Date divisionTime = DateUtil.stringToDate(divisionDate, DateUtil.SIMPLE_YMD);
        if(searchTimeEnd.before(divisionTime)){
            //备份表
            return milkDispatchPlanMapper.selectMilkDispatchPlanBySearchTimeInBackup(date,cardNumbers,searchTimeStart,searchTimeEnd);
        }else{
            //拉链表
            return milkDispatchPlanMapper.selectMilkDispatchPlanBySearchTimeInZipper(date,cardNumbers,searchTimeStart,searchTimeEnd);
        }
    }


    public void deleteByRuleId(Long ruleId, List<Integer> planStatus, MilkDispatchPlan record) {
        Example example = new Example(MilkDispatchPlan.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("ruleId", ruleId);
        criteria.andIn("planStatus", planStatus);
        criteria.andNotEqualTo("pushFlag", PushFlagEnum.COMPLETED.getCode());
        milkDispatchPlanMapper.updateByExampleSelective(record, example);
    }

    public void updateByRuleId(MilkDispatchPlan record, Long ruleId) {
        replaceBlank(record);
        Condition condition = new Condition(MilkDispatchPlan.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("ruleId", ruleId);
        milkDispatchPlanMapper.updateByExampleSelective(record, condition);
    }

    public void updateByIds(MilkDispatchPlan record, List<Long> ids) {
        replaceBlank(record);
        Condition condition = new Condition(MilkDispatchPlan.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andIn("id", ids);
        milkDispatchPlanMapper.updateByExampleSelective(record, condition);
    }

    public void updateByCreatetime(MilkDispatchPlan record,Collection<String> cardNumbers, Date startTime,Date endTime) {
        replaceBlank(record);
        Condition condition = new Condition(MilkDispatchPlan.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("delflag",DeleteFlagEnum.IS_NOT_DELETE.getCode());
        criteria.andIn("planStatus",Arrays.asList(PlanStatusEnum.PLAN.getCode(),
                PlanStatusEnum.DISTRIBUTION.getCode(), PlanStatusEnum.SIGNED.getCode(),
                PlanStatusEnum.PAUSE.getCode(), PlanStatusEnum.DELIVER.getCode(), PlanStatusEnum.AFTER.getCode()));
        criteria.andLessThanOrEqualTo("createTime",endTime);
        criteria.andGreaterThanOrEqualTo("createTime",startTime);
        criteria.andIn("cardNumber",cardNumbers);
        milkDispatchPlanMapper.updateByExampleSelective(record, condition);
    }

    public int countByCreatetime(Collection<String> cardNumbers, Date startTime,Date endTime) {
        Condition condition = new Condition(MilkDispatchPlan.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("delflag",DeleteFlagEnum.IS_NOT_DELETE.getCode());
        criteria.andNotEqualTo("planStatus",PlanStatusEnum.EXPIRED.getCode());
        criteria.andLessThanOrEqualTo("createTime",endTime);
        criteria.andGreaterThanOrEqualTo("createTime",startTime);
        criteria.andIn("cardNumber",cardNumbers);
        return milkDispatchPlanMapper.selectCountByExample(condition);
    }

    public void updateById(MilkDispatchPlan record) {
        replaceBlank(record);
        milkDispatchPlanMapper.updateByPrimaryKeySelective(record);
    }

    public void updateAll(MilkDispatchPlan record) {
        replaceBlank(record);
        milkDispatchPlanMapper.updateByPrimaryKey(record);
    }

    public int updateByIdAndUpdateTime(MilkDispatchPlan record){
        replaceBlank(record);
        Condition condition = new Condition(MilkDispatchPlan.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("id", record.getId());
        criteria.andEqualTo("updateTime", record.getUpdateTime());
        return milkDispatchPlanMapper.updateByExampleSelective(record,condition);
    }

    public void updateNotPushByRuleId(MilkDispatchPlan record) {
        replaceBlank(record);
        Condition condition = new Condition(MilkDispatchPlan.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andIn("pushFlag",Arrays.asList(PushFlagEnum.NOT_PUSH.getCode(),PushFlagEnum.FAILED.getCode()));
        criteria.andEqualTo("delflag",DeleteFlagEnum.IS_NOT_DELETE.getCode());
        criteria.andEqualTo("ruleId",record.getRuleId());
        milkDispatchPlanMapper.updateByExampleSelective(record,condition);
    }

    public List<MilkDispatchPlan> selectNotPushByRuleId(Long ruleId) {
        Condition condition = new Condition(MilkDispatchPlan.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andIn("pushFlag",Arrays.asList(PushFlagEnum.NOT_PUSH.getCode(),PushFlagEnum.FAILED.getCode()));
        criteria.andEqualTo("delflag",DeleteFlagEnum.IS_NOT_DELETE.getCode());
        criteria.andEqualTo("ruleId",ruleId);
        return milkDispatchPlanMapper.selectByExample(condition);
    }

    public void updateInvoiceById(Long id) {
        milkDispatchPlanMapper.updateInvoiceById(id);
    }

    public int countBySearch(DispatchPlanSearchDTO dto, List<Long> userIds) {
//        Condition condition = new Condition(MilkDispatchPlan.class);
//        //condition.orderBy("dispatchDate").desc();// 按照配送日期倒序排列，若配送日期一致，则按照配送计划创建时间倒序排列
//        Example.Criteria criteria = condition.createCriteria();
//        criteria.andEqualTo("delflag", DeleteFlagEnum.IS_NOT_DELETE.getCode());
//
//        if (StringUtils.isNotBlank(dto.getRuleId())) {
//            criteria.andEqualTo("ruleId", dto.getRuleId());
//        }
//        if (CollectionUtils.isNotEmpty(dto.getPlanStatusList())) {
//            criteria.andIn("planStatus", dto.getPlanStatusList());
//        }
//        if (StringUtils.isNotBlank(dto.getCardNumber())) {
//            criteria.andEqualTo("cardNumber", dto.getCardNumber());
//        }
//        if (CollectionUtils.isNotEmpty(dto.getCardNumberList())) {
//            criteria.andIn("cardNumber", dto.getCardNumberList());
//        }
//        if (CollectionUtils.isNotEmpty(userIds)) {
//            criteria.andIn("userId", userIds);
//        }
//        if (null != dto.getProductId()) {
//            criteria.andEqualTo("productId", dto.getProductId());
//        }
//        if (null != dto.getDispatchDateStart()) {
//            criteria.andGreaterThanOrEqualTo("dispatchDate", dto.getDispatchDateStart());
//        }
//        if (null != dto.getDispatchDateEnd()) {
//            criteria.andLessThanOrEqualTo("dispatchDate", dto.getDispatchDateEnd());
//        }
//
//        if (StringUtils.isNotBlank(dto.getTrackingNumber())) {
//            criteria.andLike("logistics", "%" + dto.getTrackingNumber() + "%");
//        }
//
//        if (StringUtils.isNotBlank(dto.getProductName())) {
//            criteria.andLike("productName", "%" + dto.getProductName() + "%");
//        }
//
//        if (StringUtils.isNotBlank(dto.getSkuCode())) {
//            criteria.andEqualTo("skuCode", dto.getSkuCode());
//        }
//
//        if (CollectionUtils.isNotEmpty(dto.getProvinceCode())) {
//            criteria.andIn("provinceCode", dto.getProvinceCode());
//        }
//
//        if (CollectionUtils.isNotEmpty(dto.getCityCode())) {
//            criteria.andIn("cityCode", dto.getCityCode());
//        }
//
//        if (CollectionUtils.isNotEmpty(dto.getDistrictCode())) {
//            criteria.andIn("districtCode", dto.getDistrictCode());
//        }
//
//        if (CollectionUtils.isNotEmpty(dto.getProvince())) {
//            criteria.andIn("province", dto.getProvince());
//        }
//
//        if (CollectionUtils.isNotEmpty(dto.getCity())) {
//            criteria.andIn("city", dto.getCity());
//        }
//
//        if (CollectionUtils.isNotEmpty(dto.getDistrict())) {
//            criteria.andIn("district", dto.getDistrict());
//        }
//
//        if (Objects.nonNull(dto.getPushFlag())) {
//            criteria.andEqualTo("pushFlag", dto.getPushFlag());
//        }
//
//        if (StringUtils.isNotBlank(dto.getPushFailReason())) {
//            criteria.andEqualTo("pushFailReason", dto.getPushFailReason());
//        }
//
//        if(Objects.nonNull(dto.getSubject())){
//            criteria.andEqualTo("subject", dto.getSubject());
//        }

        return milkDispatchPlanMapper.selectCountByDto(dto, userIds);
    }

    public List<MilkDispatchPlan> search(DispatchPlanSearchDTO dto, List<Long> userIds) {

        return milkDispatchPlanMapper.search(dto, userIds);

//        Condition condition = new Condition(MilkDispatchPlan.class);
//        condition.orderBy("dispatchDate").desc();// 按照配送日期倒序排列，若配送日期一致，则按照配送计划创建时间倒序排列
//        Example.Criteria criteria = condition.createCriteria();
//        criteria.andEqualTo("delflag", DeleteFlagEnum.IS_NOT_DELETE.getCode());
//
//        if (StringUtils.isNotBlank(dto.getRuleId())) {
//            criteria.andEqualTo("ruleId", dto.getRuleId());
//        }
//        if (CollectionUtils.isNotEmpty(dto.getPlanStatusList())) {
//            criteria.andIn("planStatus", dto.getPlanStatusList());
//        }
//        if (StringUtils.isNotBlank(dto.getCardNumber())) {
//            criteria.andEqualTo("cardNumber", dto.getCardNumber());
//        }
//        if (CollectionUtils.isNotEmpty(dto.getCardNumberList())) {
//            criteria.andIn("cardNumber", dto.getCardNumberList());
//        }
//        if (CollectionUtils.isNotEmpty(userIds)) {
//            criteria.andIn("userId", userIds);
//        }
//        if (null != dto.getProductId()) {
//            criteria.andEqualTo("productId", dto.getProductId());
//        }
//        if (null != dto.getDispatchDateStart()) {
//            criteria.andGreaterThanOrEqualTo("dispatchDate", dto.getDispatchDateStart());
//        }
//        if (null != dto.getDispatchDateEnd()) {
//            criteria.andLessThanOrEqualTo("dispatchDate", dto.getDispatchDateEnd());
//        }
//
//        if (StringUtils.isNotBlank(dto.getTrackingNumber())) {
//            criteria.andLike("logistics", "%" + dto.getTrackingNumber() + "%");
//        }
//        if (StringUtils.isNotBlank(dto.getProductName())) {
//            criteria.andLike("productName", "%" + dto.getProductName() + "%");
//        }
//
//        if (StringUtils.isNotBlank(dto.getSkuCode())) {
//            criteria.andEqualTo("skuCode", dto.getSkuCode());
//        }
//
//        if (CollectionUtils.isNotEmpty(dto.getProvinceCode())) {
//            criteria.andIn("provinceCode", dto.getProvinceCode());
//        }
//
//        if (CollectionUtils.isNotEmpty(dto.getCityCode())) {
//            criteria.andIn("cityCode", dto.getCityCode());
//        }
//
//        if (CollectionUtils.isNotEmpty(dto.getDistrictCode())) {
//            criteria.andIn("districtCode", dto.getDistrictCode());
//        }
//
//        if (CollectionUtils.isNotEmpty(dto.getProvince())) {
//            criteria.andIn("province", dto.getProvince());
//        }
//
//        if (CollectionUtils.isNotEmpty(dto.getCity())) {
//            criteria.andIn("city", dto.getCity());
//        }
//
//        if (CollectionUtils.isNotEmpty(dto.getDistrict())) {
//            criteria.andIn("district", dto.getDistrict());
//        }
//
//        if (Objects.nonNull(dto.getPushFlag())) {
//            criteria.andEqualTo("pushFlag", dto.getPushFlag());
//        }
//
//        if (StringUtils.isNotBlank(dto.getPushFailReason())) {
//            criteria.andEqualTo("pushFailReason", dto.getPushFailReason());
//        }
//
//        if(Objects.nonNull(dto.getSubject())){
//            criteria.andEqualTo("subject", dto.getSubject());
//        }
//
//        return milkDispatchPlanMapper.selectByExample(condition);
    }

    public List<MilkDispatchPlan> searchByCardNumber(String cardNumber) {
        Condition condition = new Condition(MilkDispatchPlan.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("delflag", DeleteFlagEnum.IS_NOT_DELETE.getCode());
        criteria.andEqualTo("cardNumber", cardNumber);
        return milkDispatchPlanMapper.selectByExample(condition);
    }

    public List<MilkDispatchPlan> searchByRuleAndStatus(DispatchPlanListDTO dto) {
        Condition condition = new Condition(MilkDispatchPlan.class);
        if (null == dto.getRuleId() && null != dto.getPlanStatus()
                && !dto.getPlanStatus().equals(PlanStatusEnum.PLAN.getCode())
                && !dto.getPlanStatus().equals(PlanStatusEnum.PAUSE.getCode())) {
            // 已完成列表
            condition.orderBy("dispatchDate").desc();
        } else {
            condition.orderBy("dispatchDate").asc();
        }

        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("delflag", DeleteFlagEnum.IS_NOT_DELETE.getCode());
        if (null != dto.getRuleId()) {
            criteria.andEqualTo("ruleId", dto.getRuleId());
        }
        if (CollectionUtils.isNotEmpty(dto.getPlanStatus())) {
            criteria.andIn("planStatus", dto.getPlanStatus());
        }
        if (null != dto.getUserId()) {
            criteria.andEqualTo("userId", dto.getUserId());
        }
        return milkDispatchPlanMapper.selectByExample(condition);
    }

    public List<MilkDispatchPlan> findAloneList(String cardNumber, List<Long> ruleIds) {
        Example example = new Example(MilkDispatchPlan.class);
        Example.Criteria criteria = example.createCriteria();
        // 按照配送日期正序排列，若配送日期一致，则按照配送计划创建时间倒序排列
        example.orderBy("dispatchDate").asc();
        criteria.andEqualTo("cardNumber", cardNumber);
        criteria.andEqualTo("delflag", DeleteFlagEnum.IS_NOT_DELETE.getCode());
        if (CollectionUtils.isNotEmpty(ruleIds)) {
            criteria.andNotIn("ruleId", ruleIds);
        }
        criteria.andIn("planStatus", Arrays.asList(PlanStatusEnum.SIGNED.getCode(),
                PlanStatusEnum.DISTRIBUTION.getCode(), PlanStatusEnum.DELIVER.getCode()));
        return milkDispatchPlanMapper.selectByExample(example);
    }

    public List<MilkDispatchPlan> findPushHandlerList(Date time) {
        Example example = new Example(MilkDispatchPlan.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("delflag", DeleteFlagEnum.IS_NOT_DELETE.getCode());
        criteria.andEqualTo("planStatus", PlanStatusEnum.PLAN.getCode());
        criteria.andEqualTo("pushFlag", PushFlagEnum.NOT_PUSH.getCode());
        criteria.andEqualTo("dispatchDate", time);
        return milkDispatchPlanMapper.selectByExample(example);
    }

    public List<MilkDispatchPlan> findPlanByDateAndPushFlag(Date date, Integer pushFlag, Integer planStauts){
        Example example = new Example(MilkDispatchPlan.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("delflag", DeleteFlagEnum.IS_NOT_DELETE.getCode());
        if(null != planStauts){
            criteria.andEqualTo("planStatus", planStauts);
        }
        criteria.andEqualTo("pushFlag", pushFlag);
        criteria.andEqualTo("dispatchDate", date);
        return milkDispatchPlanMapper.selectByExample(example);
    }


    public List<MilkDispatchPlan> findPushFailOfEpidemic(Date time) {
        Example example = new Example(MilkDispatchPlan.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("delflag", DeleteFlagEnum.IS_NOT_DELETE.getCode());
        criteria.andEqualTo("planStatus", PlanStatusEnum.PLAN.getCode());
        criteria.andEqualTo("pushFlag", PushFlagEnum.FAILED.getCode());
        criteria.andEqualTo("dispatchDate", time);
        criteria.andIsNull("logistics");
        criteria.andLike("pushFailReason", "%疫情%");
        return milkDispatchPlanMapper.selectByExample(example);
    }

    public List<MilkDispatchPlan> findPushNoLogistics(Date time) {
        Example example = new Example(MilkDispatchPlan.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("delflag", DeleteFlagEnum.IS_NOT_DELETE.getCode());
        criteria.andEqualTo("planStatus", PlanStatusEnum.DISTRIBUTION.getCode());
        criteria.andEqualTo("pushFlag", PushFlagEnum.COMPLETED.getCode());
        criteria.andEqualTo("dispatchDate", time);
        criteria.andIsNull("logistics");
        return milkDispatchPlanMapper.selectByExample(example);
    }

    public List<MilkDispatchPlan> findPackageHandlerList() {
        Example example = new Example(MilkDispatchPlan.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("delflag", DeleteFlagEnum.IS_NOT_DELETE.getCode());
        criteria.andEqualTo("pushFlag", PushFlagEnum.COMPLETED.getCode());
        criteria.andEqualTo("planStatus", PlanStatusEnum.DISTRIBUTION.getCode());
        return milkDispatchPlanMapper.selectByExample(example);
    }

    public List<MilkDispatchPlan> searchByStatus(List<Integer> planStatus) {
        Example example = new Example(MilkDispatchPlan.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("delflag", DeleteFlagEnum.IS_NOT_DELETE.getCode());
        criteria.andIn("planStatus", planStatus);
        return milkDispatchPlanMapper.selectByExample(example);
    }

    /**
     * 月提货统计报表-条件查询
     *
     * @param dto
     * @return
     */
    public List<MilkDispatchPlan> findInCondition(MonthlyPlanFormListDTO dto) {
        MonthlyPlanFormList po = BeanUtils.copy(dto, MonthlyPlanFormList::new);
        return milkDispatchPlanMapper.getMilkDispatchPlanByCondition(po,
                DeleteFlagEnum.IS_NOT_DELETE.getCode());
    }

    public List<PlanedSkuAmountVO> getSkAmountByDate(Date start, Date end) {
        return milkDispatchPlanMapper.getSkAmountByDate(start, end);
    }

    public List<PlanedSkuAmountVO> getAllSkAmount() {
        return milkDispatchPlanMapper.getAllSkAmount();
    }

    public List<MilkDispatchPlan> findAutoConfirm() {
        Example example = new Example(MilkDispatchPlan.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("delflag", DeleteFlagEnum.IS_NOT_DELETE.getCode());
        criteria.andEqualTo("pushFlag", PushFlagEnum.COMPLETED.getCode());
        criteria.andIn("planStatus", Arrays.asList(PlanStatusEnum.DELIVER.getCode(), PlanStatusEnum.DISTRIBUTION.getCode()));
        criteria.andIsNotNull("logistics");

        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_MONTH, -7);
        criteria.andLessThanOrEqualTo("deliveryTime", calendar.getTime());
        return milkDispatchPlanMapper.selectByExample(example);
    }

    public List<MilkDispatchPlan> findTmThreeDayDeliver(Date date, Integer flag) {
        Example example = new Example(MilkDispatchPlan.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("delflag", DeleteFlagEnum.IS_NOT_DELETE.getCode());
        criteria.andEqualTo("pushFlag", PushFlagEnum.NOT_PUSH.getCode());
        criteria.andEqualTo("planStatus", PlanStatusEnum.PLAN.getCode());
        criteria.andEqualTo("dispatchDate", date);
        criteria.andEqualTo("tmSmsFlag", flag);
        criteria.andNotEqualTo("tmToken", "");
        return milkDispatchPlanMapper.selectByExample(example);
    }

    public List<MilkDispatchPlan> findTmSign() {
        Example example = new Example(MilkDispatchPlan.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("delflag", DeleteFlagEnum.IS_NOT_DELETE.getCode());
        criteria.andEqualTo("pushFlag", PushFlagEnum.COMPLETED.getCode());
        criteria.andIn("planStatus", Arrays.asList(PlanStatusEnum.DISTRIBUTION.getCode(), PlanStatusEnum.DELIVER.getCode()));
        criteria.andNotEqualTo("tmSmsFlag", TmSmsFlagEnum.SIGN.getCode());
        criteria.andNotEqualTo("tmToken", "");
        criteria.andIsNotNull("logistics");

        return milkDispatchPlanMapper.selectByExample(example);
    }

    public MilkDispatchPlan findLatestPlan(String cardNumber) {
        Example example = new Example(MilkDispatchPlan.class);
        Example.Criteria criteria = example.createCriteria();
        example.setOrderByClause("dispatch_date");
        criteria.andEqualTo("cardNumber", cardNumber);
        criteria.andEqualTo("delflag", DeleteFlagEnum.IS_NOT_DELETE.getCode());
        criteria.andEqualTo("cardNumber", cardNumber);
        criteria.andEqualTo("planStatus", PlanStatusEnum.PLAN.getCode());
        List<MilkDispatchPlan> plans = milkDispatchPlanMapper.selectByExample(example);
        if (CollectionUtils.isNotEmpty(plans)) {
            return plans.get(0);
        }
        return null;
    }

    public MilkDispatchPlan findByIdNoDel(Long id) {
        Condition condition = new Condition(MilkDispatchPlan.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("id", id);
        return milkDispatchPlanMapper.selectOneByExample(condition);
    }

    public void updateByCardNumber(MilkDispatchPlan record, String cardNumber) {
        replaceBlank(record);
        Condition condition = new Condition(MilkDispatchPlan.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("cardNumber", cardNumber);
        milkDispatchPlanMapper.updateByExampleSelective(record, condition);
    }

    public void updateInvoiceTimeById(MilkDispatchPlan record) {
        replaceBlank(record);
        Condition condition = new Condition(MilkDispatchPlan.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("id", record.getId());
        milkDispatchPlanMapper.updateByExampleSelective(record, condition);
    }

    public Integer updateAmountByCardNumber(String cardNumber, BigDecimal amount) {
        return milkDispatchPlanMapper.updateAmountByCardNumber(cardNumber,amount);
    }

    public List<MilkDispatchPlan> findFixAmountByCardNumber(String cardNumber) {
        Example example = new Example(MilkDispatchPlan.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("cardNumber", cardNumber);
        criteria.andNotEqualTo("planStatus", PlanStatusEnum.EXPIRED.getCode());
        criteria.andEqualTo("delflag", DeleteFlagEnum.IS_NOT_DELETE.getCode());
        return milkDispatchPlanMapper.selectByExample(example);
    }

    public List<MilkDispatchPlan> findOneDayByCardNumber(Long oneDaay) {
        Example example = new Example(MilkDispatchPlan.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("dispatchDate", new Date(oneDaay));
        return milkDispatchPlanMapper.selectByExample(example);
    }

    public List<MilkDispatchPlan> findOr() {
        Example example = new Example(MilkDispatchPlan.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andGreaterThan("amount", 0);
        criteria.andLessThan("amount", 10);
        criteria.andEqualTo("delflag", DeleteFlagEnum.IS_NOT_DELETE.getCode());
        return milkDispatchPlanMapper.selectByExample(example);
    }

    public List<MilkDispatchPlan> findByOrders(List<String> list) {
        Example example = new Example(MilkDispatchPlan.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andIn("planOrderNo", list);
        return milkDispatchPlanMapper.selectByExample(example);
    }

    public List<MilkDispatchPlan> findByUserIdAndSkuCode(String skuCode, Long userId, Long ruleId, Long productId) {
        Condition condition = new Condition(MilkDispatchPlan.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("userId", userId);
        criteria.andEqualTo("skuCode", skuCode);
        if (Objects.nonNull(productId)){
            criteria.andEqualTo("productId", productId);
        }
        if (null != ruleId) {
            criteria.andNotEqualTo("ruleId", ruleId);
        }
        criteria.andNotEqualTo("planStatus", PlanStatusEnum.EXPIRED.getCode());
        criteria.andEqualTo("delflag", DeleteFlagEnum.IS_NOT_DELETE.getCode());
        return milkDispatchPlanMapper.selectByExample(condition);
    }

    public List<MilkDispatchPlan> findByRuleIdAndStatus(String skuCode, Long userId, Long ruleId, Long productId) {
        Condition condition = new Condition(MilkDispatchPlan.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("userId", userId);
        criteria.andEqualTo("skuCode", skuCode);
        criteria.andEqualTo("ruleId", ruleId);
        if (Objects.nonNull(productId)){
            criteria.andEqualTo("productId", productId);
        }
        criteria.andIn("planStatus", Arrays.asList(PlanStatusEnum.DISTRIBUTION.getCode(),
                PlanStatusEnum.SIGNED.getCode(), PlanStatusEnum.DELIVER.getCode()));
        criteria.andEqualTo("delflag", DeleteFlagEnum.IS_NOT_DELETE.getCode());
        return milkDispatchPlanMapper.selectByExample(condition);
    }

    public List<PlanGroupByDTO> selectByCardNumbersInBackup(String date, Collection<String> cardNumbers) {
        if (CollectionUtils.isNotEmpty(cardNumbers)) {
            List<List<String>> cardNoGroupList = StreamUtils.split(cardNumbers, 500);

            List<PlanGroupByDTO> result = new LinkedList<>();

            for (List<String> subList : cardNoGroupList) {

                List<PlanGroupByDTO> subResult;
                if (StringUtils.isNotBlank(date)) {
                    subResult = milkDispatchPlanMapper.selectByCardNumbersInBackup(date, subList);
                } else {
                    subResult = milkDispatchPlanMapper.selectGroupByCardNumber(subList);
                }
                if (CollectionUtils.isNotEmpty(subResult)) {
                    result.addAll(subResult);
                }
            }
            return result;
        }
        return new ArrayList<>();
    }

    public List<MilkDispatchPlan> findByUserIdAndSkuCodeAndCardNumber(String skuCode, Long userId, Long ruleId, Long productId, String cardNumber) {
        Condition condition = new Condition(MilkDispatchPlan.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("userId", userId);
        criteria.andEqualTo("skuCode", skuCode);
        if (Objects.nonNull(productId)){
            criteria.andEqualTo("productId", productId);
        }
        if (null != ruleId) {
            criteria.andNotEqualTo("ruleId", ruleId);
        }
        if (StringUtils.isNotBlank(cardNumber)) {
            criteria.andEqualTo("cardNumber", cardNumber);
        }
        criteria.andNotIn("planStatus", Arrays.asList(PlanStatusEnum.EXPIRED.getCode(), PlanStatusEnum.CANCEL.getCode(), PlanStatusEnum.AFTER_COMPLETED.getCode()));
        criteria.andEqualTo("delflag", DeleteFlagEnum.IS_NOT_DELETE.getCode());
        return milkDispatchPlanMapper.selectByExample(condition);
    }

    public List<MilkDispatchPlan> findByUserIdAndSkuCodeAndCardNumberAndPlanId(String skuCode, Long userId, Long planId, Long productId, String cardNumber) {
        Condition condition = new Condition(MilkDispatchPlan.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("userId", userId);
        criteria.andEqualTo("skuCode", skuCode);
        if (Objects.nonNull(productId)){
            criteria.andEqualTo("productId", productId);
        }
        if (null != planId) {
            criteria.andNotEqualTo("id", planId);
        }
        if (StringUtils.isNotBlank(cardNumber)) {
            criteria.andEqualTo("cardNumber", cardNumber);
        }
        criteria.andNotEqualTo("planStatus", PlanStatusEnum.EXPIRED.getCode());
        criteria.andEqualTo("delflag", DeleteFlagEnum.IS_NOT_DELETE.getCode());
        return milkDispatchPlanMapper.selectByExample(condition);
    }

    public List<MilkDispatchPlan> findByRuleIdAndStatusAndCardNumber(String skuCode, Long userId, Long ruleId, Long productId, String cardNumber) {
        Condition condition = new Condition(MilkDispatchPlan.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("userId", userId);
        criteria.andEqualTo("skuCode", skuCode);
        criteria.andEqualTo("ruleId", ruleId);
        if (Objects.nonNull(productId)){
            criteria.andEqualTo("productId", productId);
        }
        if (StringUtils.isNotBlank(cardNumber)) {
            criteria.andEqualTo("cardNumber", cardNumber);
        }
        criteria.andIn("planStatus", Arrays.asList(PlanStatusEnum.DISTRIBUTION.getCode(),
                PlanStatusEnum.SIGNED.getCode(), PlanStatusEnum.DELIVER.getCode()));
        criteria.andEqualTo("delflag", DeleteFlagEnum.IS_NOT_DELETE.getCode());
        return milkDispatchPlanMapper.selectByExample(condition);
    }

    /**
     * 查询备份表
     *
     * @param dto
     * @return
     */
    @DS("db2")
    public List<MilkDispatchPlan> searchInBackup(ReportFormExportBackupDTO dto) {
        return milkDispatchPlanMapper.searchInBackup(dto);
    }

    /**
     * 查询实际已提数
     */
    public Integer selectMilkAmount(String cardNumber) {
        return milkDispatchPlanMapper.selectMilkAmount(cardNumber);
    }

    /**
     * 查询已推计划提数
     */
    public Integer selectMilkAmountOfPlan(String cardNumber) {
        return milkDispatchPlanMapper.selectMilkAmountOfPlan(cardNumber);
    }

    /**
     * 查询实际已提金额
     */
    public BigDecimal selectAmount(String cardNumber) {
        return milkDispatchPlanMapper.selectAmount(cardNumber);
    }

    /**
     * 查询已推计划已提金额
     */
    public BigDecimal selectAmountOfPlan(String cardNumber) {
        return milkDispatchPlanMapper.selectAmountOfPlan(cardNumber);
    }

    /**
     * 批量查询实际已提数
     */
    public List<MilkAmountVO> selectMilkAmounts(List<String> cardNumbers) {
        return milkDispatchPlanMapper.selectMilkAmounts(cardNumbers);
    }

    /**
     * 批量查询实际已提数和已提金额
     */
    public List<MilkAmountVO> selectMilkAmountsAndAmounts(List<String> cardNumbers) {
        if(CollectionUtils.isEmpty(cardNumbers)){
            return Collections.emptyList();
        }
        return milkDispatchPlanMapper.selectMilkAmountsAndAmounts(cardNumbers);
    }

    /**
     * 批量查询已传但未发货但提数和金额
     */
    public List<MilkAmountVO> selectNoIssuedMilkAmountsAndAmounts(List<String> cardNumbers) {
        if(CollectionUtils.isEmpty(cardNumbers)){
            return Collections.emptyList();
        }
        return milkDispatchPlanMapper.selectNoIssuedMilkAmountsAndAmounts(cardNumbers);
    }

    /**
     * 备份表批量查询实际已提数
     */
    @DS("db2")
    public List<MilkAmountVO> selectMilkAmountsBackup(String date,List<String> cardNumbers) {
        return milkDispatchPlanMapper.selectMilkAmountsBackup(date,cardNumbers);
    }

    /**
     * 备份表批量查询实际已提数/已提金额
     */
    @DS("db2")
    public List<MilkAmountVO> selectMilkAmountsAndAmountsBackup(String date,List<String> cardNumbers) {
        return milkDispatchPlanMapper.selectMilkAmountsAndAmountsBackup(date,cardNumbers);
    }

    /**
     * 备份表批量查询已传中台但未发货提数/金额
     */
    @DS("db2")
    public List<MilkAmountVO> selectNoIssuedMilkAmountsAndAmountsBackup(String date,List<String> cardNumbers) {
        return milkDispatchPlanMapper.selectNoIssuedMilkAmountsAndAmountsBackup(date,cardNumbers);
    }

    /**
     * 拉链表批量查询实际已提数/已提金额
     */
    @DS("db2")
    public List<MilkAmountVO> selectMilkAmountsAndAmountsZipper(String date,List<String> cardNumbers) {
        return milkDispatchPlanMapper.selectMilkAmountsAndAmountsZipper(date,cardNumbers);
    }

    /**
     * 拉链表批量查询已传中台但未发货提数/金额
     */
    @DS("db2")
    public List<MilkAmountVO> selectNoIssuedMilkAmountsAndAmountsZipper(String date,List<String> cardNumbers) {
        return milkDispatchPlanMapper.selectNoIssuedMilkAmountsAndAmountsZipper(date,cardNumbers);
    }

    /**
     * 备份表查询实际已提数
     */
    @DS("db2")
    public Integer selectMilkAmountBackup(String date, String cardNumber) {
        return milkDispatchPlanMapper.selectMilkAmountBackup(date, cardNumber);
    }

    /**
     * 拉链表查询实际已提数
     */
    @DS("db2")
    public Integer selectMilkAmountZipper(String date, String cardNumber) {
        return milkDispatchPlanMapper.selectMilkAmountZipper(date, cardNumber);
    }


    /**
     * 备份表查询实际已提金额
     */
    @DS("db2")
    public BigDecimal selectAmountBackup(String date,String cardNumber) {
        return milkDispatchPlanMapper.selectAmountBackup(date, cardNumber);
    }

    /**
     * 拉链表查询实际已提金额
     */
    @DS("db2")
    public BigDecimal selectAmountZipper(String date,String cardNumber) {
        return milkDispatchPlanMapper.selectAmountZipper(date, cardNumber);
    }

    /**
     * 已推计划中最后一提
     */
    public MilkDispatchPlan queryLastPlan(String cardNumber){
        return milkDispatchPlanMapper.queryLastPlan(cardNumber);
    }

    public int countDeliveryPlan(String cardNumber){
        return milkDispatchPlanMapper.countDeliveryPlan(cardNumber);
    }

    public List<MilkDispatchPlan> findPlanByTime(Long ruleId, Date startTime, Date endTime, Integer planStatus) {
        Example example = new Example(MilkDispatchPlan.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("ruleId",ruleId);
        criteria.andEqualTo("delflag", DeleteFlagEnum.IS_NOT_DELETE.getCode());
        criteria.andEqualTo("planStatus", planStatus);
        criteria.andEqualTo("pushFlag", PushFlagEnum.NOT_PUSH.getCode());
        criteria.andGreaterThanOrEqualTo("dispatchDate", startTime);
        criteria.andLessThanOrEqualTo("dispatchDate", endTime);
        return milkDispatchPlanMapper.selectByExample(example);
    }

    public List<MilkDispatchPlan> selectLowTemPlanByTime(Long productId, Date startTime, Date endTime){
        return milkDispatchPlanMapper.selectLowTemPlanByTime(productId, startTime, endTime);
    }

    /**
     * 已推的所有计划
     */
    public List<MilkDispatchPlan> findPlanByCardNumber(String cardNumber){
        Example example = new Example(MilkDispatchPlan.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("delflag", DeleteFlagEnum.IS_NOT_DELETE.getCode());
        criteria.andEqualTo("pushFlag", PushFlagEnum.COMPLETED.getCode());
        criteria.andEqualTo("cardNumber", cardNumber);
        criteria.andIn("planStatus",Arrays.asList(PlanStatusEnum.DISTRIBUTION.getCode(),
                PlanStatusEnum.SIGNED.getCode(), PlanStatusEnum.DELIVER.getCode(),
                PlanStatusEnum.AFTER.getCode()));
        example.setOrderByClause("dispatch_date desc");
        return milkDispatchPlanMapper.selectByExample(example);
    }

    /**
     * 未推的所有可修改的计划
     */
    public List<MilkDispatchPlan> findNoPushPlanByCardNumber(String cardNumber){
        Example example = new Example(MilkDispatchPlan.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("delflag", DeleteFlagEnum.IS_NOT_DELETE.getCode());
        criteria.andNotEqualTo("pushFlag", PushFlagEnum.COMPLETED.getCode());
        criteria.andEqualTo("cardNumber", cardNumber);
        criteria.andIn("planStatus",Arrays.asList(PlanStatusEnum.PLAN.getCode(),PlanStatusEnum.PAUSE.getCode()));
        example.setOrderByClause("dispatch_date desc");
        return milkDispatchPlanMapper.selectByExample(example);
    }

    public void updateNoPushPlanByUserId(MilkDispatchPlan plan, Long userId){
        Example example = new Example(MilkDispatchPlan.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("delflag", DeleteFlagEnum.IS_NOT_DELETE.getCode());
        criteria.andIn("pushFlag", Arrays.asList(PushFlagEnum.NOT_PUSH.getCode(), PushFlagEnum.FAILED.getCode()));
        criteria.andEqualTo("userId", userId);
        criteria.andIn("planStatus",Arrays.asList(PlanStatusEnum.PLAN.getCode(),PlanStatusEnum.PAUSE.getCode()));
        milkDispatchPlanMapper.updateByExampleSelective(plan, example);
    }

    public void updateNoPushPlanByCardNumber(MilkDispatchPlan plan, String cardNumber){
        Example example = new Example(MilkDispatchPlan.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("delflag", DeleteFlagEnum.IS_NOT_DELETE.getCode());
        criteria.andIn("pushFlag", Arrays.asList(PushFlagEnum.NOT_PUSH.getCode(), PushFlagEnum.FAILED.getCode()));
        criteria.andEqualTo("cardNumber", cardNumber);
        criteria.andIn("planStatus",Arrays.asList(PlanStatusEnum.PLAN.getCode(),PlanStatusEnum.PAUSE.getCode()));
        milkDispatchPlanMapper.updateByExampleSelective(plan, example);
    }

    /**
     * 过滤收货人名称和详细地址特殊字符
     */
    private void replaceBlank(MilkDispatchPlan record){
        if(Objects.nonNull(record.getReceiverName()) && StringUtils.isNotBlank(record.getReceiverPhone())){
            record.setReceiverName(record.getReceiverName().replaceAll("[^\\u0000-\\uFFFF]",""));
            record.setReceiverName(DataUtil.replaceAllBlank(record.getReceiverName()));
        }

        if(Objects.nonNull(record.getAddressDetail()) && StringUtils.isNotBlank(record.getAddressDetail())){
            record.setAddressDetail(record.getAddressDetail().replaceAll("[^\\u0000-\\uFFFF]",""));
            record.setAddressDetail(DataUtil.replaceAllBlank(record.getAddressDetail()));
        }
    }

    public List<MilkDispatchPlan> findByPushDTO(Integer pushFlag, Date startTime, Date endTime) {
        if(!ObjectUtils.allNotNull(pushFlag, startTime, endTime)){
            throw new BusinessException("参数不能为空");
        }
        Example example = new Example(MilkDispatchPlan.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("delflag", DeleteFlagEnum.IS_NOT_DELETE.getCode());
        if(pushFlag != null){
            criteria.andEqualTo("pushFlag", pushFlag);
        }
        criteria.andGreaterThanOrEqualTo("dispatchDate", startTime);
        criteria.andLessThanOrEqualTo("dispatchDate", endTime);
        return milkDispatchPlanMapper.selectByExample(example);
    }

    public void updateBillFlag(List<Long> ids){
        MilkDispatchPlan plan = new MilkDispatchPlan();
        plan.setBillFlag(PlanBillFlagEnum.BILL.getCode());
        Example example = new Example(MilkDispatchPlan.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andIn("id", ids);
        milkDispatchPlanMapper.updateByExampleSelective(plan, example);
    }

    public List<DispatchPlanProductDTO> productCount(Date time) {
        return milkDispatchPlanMapper.countPlanProduct(time);
    }


    public int batchUpdateRemark(List<MilkDispatchPlan> dto) {
        return milkDispatchPlanMapper.batchUpdateRemark(dto);
    }

    public List<MilkDispatchPlan> selectByCardNumerList(List<String> cardNumberList){
        if(CollectionUtils.isEmpty(cardNumberList)){
            return new ArrayList<>();
        }
        Example example = new Example(MilkDispatchPlan.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("delflag", DeleteFlagEnum.IS_NOT_DELETE.getCode());
        criteria.andIn("cardNumber", cardNumberList);
        criteria.andNotIn("planStatus",Arrays.asList(PlanStatusEnum.SIGNED.getCode(),PlanStatusEnum.EXPIRED.getCode(),
                PlanStatusEnum.CANCEL.getCode(),PlanStatusEnum.AFTER_COMPLETED.getCode()));
        return milkDispatchPlanMapper.selectByExample(example);
    }

    public MilkDispatchPlan findOnePlanByUserPhone(String userPhone){
        return milkDispatchPlanMapper.findOnePlanByUserPhone(userPhone);
    }

    public MilkDispatchPlan findOneByUserPhone(String userPhone, Date date){
        return milkDispatchPlanMapper.findOneByUserPhone(userPhone, date);
    }

    public Integer sumPlanPrivilegeCnt(Long userId, Long privilegeLableId, Long ruleId, Long planId){
        return milkDispatchPlanMapper.sumPlanPrivilegeCnt(userId, privilegeLableId, ruleId, planId);
    }

    public Integer sumPlanPrivilegeCnt2(Long userId, Long privilegeLableId, Long ruleId){
        return milkDispatchPlanMapper.sumPlanPrivilegeCnt2(userId, privilegeLableId, ruleId);
    }

    public Integer sumPlanCntByRuleIdAndProductId(Long ruleId, Long productId){
        return milkDispatchPlanMapper.sumPlanCntByRuleIdAndProductId(ruleId, productId);
    }

    public MilkDispatchPlan findOneByRuleIdAndProductId(Long ruleId, Long productId){
        return milkDispatchPlanMapper.findOneByRuleIdAndProductId(ruleId, productId);
    }

    public List<MilkDispatchPlanWithOmsInfoDTO> searchForExport(DispatchPlanSearchDTO searchDTO, List<Long> userIds) {
        return milkDispatchPlanMapper.searchForExport(searchDTO, userIds);
    }
}
