package com.hengtiansoft.order.dao;

import com.hengtiansoft.common.enumeration.DeleteFlagEnum;
import com.hengtiansoft.common.util.DataUtil;
import com.hengtiansoft.common.util.StreamUtils;
import com.hengtiansoft.item.enumeration.ProductTemperatureEnum;
import com.hengtiansoft.order.entity.dto.DispatchRuleSearchDTO;
import com.hengtiansoft.order.entity.mapper.MilkDispatchRuleMapper;
import com.hengtiansoft.order.entity.po.MilkDispatchRule;
import com.hengtiansoft.order.enums.DispatchRuleTypeEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * @Author: <EMAIL>
 */
@Repository
public class MilkDispatchRuleDao {

    @Resource
    private MilkDispatchRuleMapper milkDispatchRuleMapper;

    public List<MilkDispatchRule> findByIds(List<Long> ids){
        Condition condition = new Condition(MilkDispatchRule.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andIn("id", ids);
        return milkDispatchRuleMapper.selectByExample(condition);
    }

    public MilkDispatchRule findById(Long id){
        Condition condition = new Condition(MilkDispatchRule.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("id", id);
        criteria.andEqualTo("delflag", DeleteFlagEnum.IS_NOT_DELETE.getCode());
        return milkDispatchRuleMapper.selectOneByExample(condition);
    }

    public void delete(MilkDispatchRule record){
        milkDispatchRuleMapper.updateByPrimaryKeySelective(record);
    }


    public void insert(MilkDispatchRule record){
        replaceBlank(record);
        milkDispatchRuleMapper.insertSelective(record);
    }

    public void batchInsert(List<MilkDispatchRule> records){
        milkDispatchRuleMapper.batchInsert(records);
    }

    public void update(MilkDispatchRule record){
        replaceBlank(record);
        milkDispatchRuleMapper.updateByPrimaryKeySelective(record);
    }

    public void updateByIds(MilkDispatchRule record, List<Long> ids){
        replaceBlank(record);
        Condition condition = new Condition(MilkDispatchRule.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andIn("id", ids);
        milkDispatchRuleMapper.updateByExampleSelective(record, condition);
    }


    public List<MilkDispatchRule> search(DispatchRuleSearchDTO dto,List<Long> userIds) {
        Condition condition = new Condition(MilkDispatchRule.class);
        condition.orderBy("id").desc();
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("delflag", DeleteFlagEnum.IS_NOT_DELETE.getCode());

        if (StringUtils.isNotBlank(dto.getCardNumber())) {
            criteria.andEqualTo("cardNumber", dto.getCardNumber());
        }

        if (CollectionUtils.isNotEmpty(dto.getCardNumberList())) {
            criteria.andIn("cardNumber", dto.getCardNumberList());
        }

        if (CollectionUtils.isNotEmpty(userIds)) {
            criteria.andIn("userId", userIds);
        }
        if (null != dto.getUserId()) {
            criteria.andEqualTo("userId", dto.getUserId());
        }
        if (CollectionUtils.isNotEmpty(dto.getRuleStatusList())) {
            criteria.andIn("ruleStatus", dto.getRuleStatusList());
        }
        if(Objects.nonNull(dto.getSubject())){
            criteria.andEqualTo("subject", dto.getSubject());
        }
        if(ProductTemperatureEnum.NORMAL.getCode().equals(dto.getTemperature())){
            criteria.andIn("ruleType", Arrays.asList(DispatchRuleTypeEnum.NORMAL.getCode(),DispatchRuleTypeEnum.NORMAL_PRIVILEGE.getCode()));
            if (Objects.nonNull(dto.getDispatchDay())){
                criteria.andEqualTo("dispatchDate", dto.getDispatchDay());
            }
        }else if(ProductTemperatureEnum.LOW.getCode().equals(dto.getTemperature())){
            criteria.andIn("ruleType", Arrays.asList(DispatchRuleTypeEnum.LOW.getCode(),DispatchRuleTypeEnum.LOW_PRIVILEGE.getCode()));
            if(Objects.nonNull(dto.getDispatchDayStart())){
                criteria.andGreaterThanOrEqualTo("dispatchDate",dto.getDispatchDayStart());
            }
            if(Objects.nonNull(dto.getDispatchDayEnd())){
                criteria.andLessThanOrEqualTo("dispatchDate",dto.getDispatchDayEnd());
            }
        }
        return milkDispatchRuleMapper.selectByExample(condition);
    }

    public List<MilkDispatchRule> searchByCardNumber(String cardNumber) {
        Condition condition = new Condition(MilkDispatchRule.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("delflag", DeleteFlagEnum.IS_NOT_DELETE.getCode());
        criteria.andEqualTo("cardNumber", cardNumber);
        return milkDispatchRuleMapper.selectByExample(condition);
    }

    public MilkDispatchRule findLastestRuleByCardNumber(String cardNumber){
        Condition condition = new Condition(MilkDispatchRule.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("delflag", DeleteFlagEnum.IS_NOT_DELETE.getCode());
        criteria.andEqualTo("cardNumber", cardNumber);
        condition.orderBy("id").desc();
        List<MilkDispatchRule> ruleList = milkDispatchRuleMapper.selectByExample(condition);
        return StreamUtils.getFirst(ruleList);
    }

    public MilkDispatchRule findByIdWhetherDel(Long id){
        Condition condition = new Condition(MilkDispatchRule.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("id", id);
        return milkDispatchRuleMapper.selectOneByExample(condition);
    }

    public List<MilkDispatchRule> findByUserIdWithCardNumber(Long userId, String cardNumber) {
        Condition condition = new Condition(MilkDispatchRule.class);
        condition.orderBy("id").desc();
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("userId", userId);
        criteria.andEqualTo("cardNumber", cardNumber);
        return milkDispatchRuleMapper.selectByExample(condition);
    }

    public int countBySearch(DispatchRuleSearchDTO dto, List<Long> userIds) {
        Condition condition = new Condition(MilkDispatchRule.class);
        //condition.orderBy("createTime").desc();
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("delflag", DeleteFlagEnum.IS_NOT_DELETE.getCode());

        if (StringUtils.isNotBlank(dto.getCardNumber())) {
            criteria.andEqualTo("cardNumber", dto.getCardNumber());
        }

        if (CollectionUtils.isNotEmpty(dto.getCardNumberList())) {
            criteria.andIn("cardNumber", dto.getCardNumberList());
        }

        if (CollectionUtils.isNotEmpty(userIds)) {
            criteria.andIn("userId", userIds);
        }
        if (null != dto.getUserId()) {
            criteria.andEqualTo("userId", dto.getUserId());
        }
        if (CollectionUtils.isNotEmpty(dto.getRuleStatusList())) {
            criteria.andIn("ruleStatus", dto.getRuleStatusList());
        }
        if(Objects.nonNull(dto.getSubject())){
            criteria.andEqualTo("subject", dto.getSubject());
        }

        if(ProductTemperatureEnum.NORMAL.getCode().equals(dto.getTemperature())){
            criteria.andIn("ruleType", Arrays.asList(DispatchRuleTypeEnum.NORMAL.getCode(),DispatchRuleTypeEnum.NORMAL_PRIVILEGE.getCode()));
            if (Objects.nonNull(dto.getDispatchDay())){
                criteria.andEqualTo("dispatchDate", dto.getDispatchDay());
            }
        }else if(ProductTemperatureEnum.LOW.getCode().equals(dto.getTemperature())){
            criteria.andIn("ruleType", Arrays.asList(DispatchRuleTypeEnum.LOW.getCode(),DispatchRuleTypeEnum.LOW_PRIVILEGE.getCode()));
            if(Objects.nonNull(dto.getDispatchDayStart())){
                criteria.andGreaterThanOrEqualTo("dispatchDate",dto.getDispatchDayStart());
            }
            if(Objects.nonNull(dto.getDispatchDayEnd())){
                criteria.andLessThanOrEqualTo("dispatchDate",dto.getDispatchDayEnd());
            }
        }

        return milkDispatchRuleMapper.selectCountByExample(condition);
    }

    public MilkDispatchRule findByIdNoDel(Long id){
        Condition condition = new Condition(MilkDispatchRule.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("id", id);
        return milkDispatchRuleMapper.selectOneByExample(condition);
    }

    public void updateByCardNumber(MilkDispatchRule record, String cardNumber) {
        replaceBlank(record);
        Condition condition = new Condition(MilkDispatchRule.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("cardNumber", cardNumber);
        milkDispatchRuleMapper.updateByExampleSelective(record, condition);
    }

    private void replaceBlank(MilkDispatchRule record){
        if(Objects.nonNull(record.getReceiverName()) && StringUtils.isNotBlank(record.getReceiverPhone())){
            record.setReceiverName(record.getReceiverName().replaceAll("[^\\u0000-\\uFFFF]",""));
            record.setReceiverName(DataUtil.replaceAllBlank(record.getReceiverName()));
        }

        if(Objects.nonNull(record.getAddressDetail()) && StringUtils.isNotBlank(record.getAddressDetail())){
            record.setAddressDetail(record.getAddressDetail().replaceAll("[^\\u0000-\\uFFFF]",""));
            record.setAddressDetail(DataUtil.replaceAllBlank(record.getAddressDetail()));
        }
    }

    public Integer batchUpdateRemark(List<MilkDispatchRule> ruleList) {

        return milkDispatchRuleMapper.batchUpdateRemark(ruleList);
    }
}
