package com.hengtiansoft.order.dao;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.hengtiansoft.common.util.DataUtil;
import com.hengtiansoft.order.entity.mapper.OrderAddressMapper;
import com.hengtiansoft.order.entity.po.OrderAddress;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * @Author: <EMAIL>
 */
@Repository
public class OrderAddressDao {

    @Resource
    private OrderAddressMapper orderAddressMapper;

    public OrderAddress selectByOrderNo(String orderNo) {
        Condition condition = new Condition(OrderAddress.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("orderNo", orderNo);
        List<OrderAddress> orderAddressList = orderAddressMapper.selectByExample(condition);
        if(CollectionUtils.isEmpty(orderAddressList)){
            return null;
        }
        return orderAddressList.get(0);
    }

    public void insert(OrderAddress record){

        if(Objects.nonNull(record.getReceiverName()) && StringUtils.isNotBlank(record.getReceiverPhone())){
            record.setReceiverName(record.getReceiverName().replaceAll("[^\\u0000-\\uFFFF]",""));
            record.setReceiverName(DataUtil.replaceAllBlank(record.getReceiverName()));
        }

        if(Objects.nonNull(record.getAddress()) && StringUtils.isNotBlank(record.getAddress())){
            record.setAddress(record.getAddress().replaceAll("[^\\u0000-\\uFFFF]",""));
            record.setAddress(DataUtil.replaceAllBlank(record.getAddress()));
        }

        orderAddressMapper.insertSelective(record);
    }

    public List<OrderAddress> selectByOrderNoList(List<String> orderNo) {
        Condition condition = new Condition(OrderAddress.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andIn("orderNo", orderNo);
        return orderAddressMapper.selectByExample(condition);
    }

    public void updateByOrderNo(OrderAddress record, String orderNo){
        Condition condition = new Condition(OrderAddress.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("orderNo", orderNo);
        orderAddressMapper.updateByExampleSelective(record, condition);
    }

    @DS("db3")
    public List<OrderAddress> queryOrderAddressByOrderNoList(List<String> orderNoList) {
        if(CollectionUtils.isEmpty(orderNoList)){
            return new ArrayList<>();
        }
        Condition condition = new Condition(OrderAddress.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andIn("orderNo", orderNoList);
        return orderAddressMapper.selectByExample(condition);
    }
}
