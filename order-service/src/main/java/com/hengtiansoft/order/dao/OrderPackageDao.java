package com.hengtiansoft.order.dao;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.hengtiansoft.order.entity.mapper.OrderPackageMapper;
import com.hengtiansoft.order.entity.po.OrderPackage;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * @Author: <EMAIL>
 */
@Repository
public class OrderPackageDao {

    @Resource
    private OrderPackageMapper orderPackageMapper;

    public List<OrderPackage> findByOrderNo(String orderNo) {
        Condition condition = new Condition(OrderPackage.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("orderNo", orderNo);
        return orderPackageMapper.selectByExample(condition);
    }

    public List<OrderPackage> findByOrderNoList(List<String> orderNoList) {
        Condition condition = new Condition(OrderPackage.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andIn("orderNo", orderNoList);
        return orderPackageMapper.selectByExample(condition);
    }

    @DS("db2")
    public List<OrderPackage> selectByOrderNosInBackup(List<String> orderNos, String date) {
        if (CollectionUtils.isNotEmpty(orderNos)) {
            return orderPackageMapper.selectByOrderNosInBackup(date, orderNos);
        }
        return new ArrayList<>();
    }

    @DS("db2")
    public List<OrderPackage> selectByOrderNosInZipper(List<String> orderNos, String date) {
        if (CollectionUtils.isNotEmpty(orderNos)) {
            return orderPackageMapper.selectByOrderNosInZipper(date, orderNos);
        }
        return new ArrayList<>();
    }

    public void insert(OrderPackage record) {
        orderPackageMapper.insertSelective(record);
    }

    public List<OrderPackage> findByOrderNoAndTrackingNo(String orderNo, String trackingNo) {
        Condition condition = new Condition(OrderPackage.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("orderNo", orderNo);
        criteria.andEqualTo("trackingNo", trackingNo);
        return orderPackageMapper.selectByExample(condition);
    }

    public List<OrderPackage> findByTrackingNos(List<String> trackingNos) {
        Condition condition = new Condition(OrderPackage.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andIn("trackingNo", trackingNos);
        return orderPackageMapper.selectByExample(condition);
    }

    public void updateByOrderNo(OrderPackage record, String orderNo) {
        Condition condition = new Condition(OrderPackage.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("orderNo", orderNo);
        orderPackageMapper.updateByExampleSelective(record, condition);
    }
}
