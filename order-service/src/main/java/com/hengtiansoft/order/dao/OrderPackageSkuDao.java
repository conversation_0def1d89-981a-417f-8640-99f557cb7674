package com.hengtiansoft.order.dao;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.hengtiansoft.order.entity.mapper.OrderPackageSkuMapper;
import com.hengtiansoft.order.entity.po.OrderPackageSku;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * @Author: <EMAIL>
 */
@Repository
public class OrderPackageSkuDao {

    @Resource
    private OrderPackageSkuMapper orderPackageSkuMapper;

    public void insert(OrderPackageSku record) {
        orderPackageSkuMapper.insertSelective(record);
    }

    public void batchInsert(List<OrderPackageSku> records) {
        orderPackageSkuMapper.insertList(records);
    }

    public List<OrderPackageSku> selectByOrderNoList(List<String> orderNoList) {
        Example example = new Example(OrderPackageSku.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andIn("orderNo", orderNoList);
        return orderPackageSkuMapper.selectByExample(example);
    }

    public void updateByOrderNo(OrderPackageSku record, String orderNo) {
        Example example = new Example(OrderPackageSku.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("orderNo", orderNo);
        orderPackageSkuMapper.updateByExampleSelective(record, example);
    }

    @DS("db2")
    public List<OrderPackageSku> selectByOrderNosInBackup(List<String> orderNos, String date) {
        if (CollectionUtils.isNotEmpty(orderNos)) {
            return orderPackageSkuMapper.selectByOrderNosInBackup(date, orderNos);
        }
        return new ArrayList<>();
    }

    @DS("db2")
    public List<OrderPackageSku> selectByOrderNosInZipper(List<String> orderNos, String date) {
        if (CollectionUtils.isNotEmpty(orderNos)) {
            return orderPackageSkuMapper.selectByOrderNosInZipper(date, orderNos);
        }
        return new ArrayList<>();
    }
}
