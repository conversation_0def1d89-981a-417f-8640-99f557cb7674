package com.hengtiansoft.order.dao;

import com.hengtiansoft.common.enumeration.DeleteFlagEnum;
import com.hengtiansoft.order.entity.mapper.OrderSkuCardMapper;
import com.hengtiansoft.order.entity.po.OrderSkuCard;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
public class OrderSkuCardDao {

    @Resource
    private OrderSkuCardMapper orderSkuCardMapper;

    public void insertList(List<OrderSkuCard> orderSkuCards) {
        orderSkuCardMapper.insertList(orderSkuCards);
    }

    public List<OrderSkuCard> findByOrderSkuIds(List<Long> orderSkuIds){
        if(CollectionUtils.isEmpty(orderSkuIds)){
            return new ArrayList<>();
        }
        Condition condition = new Condition(OrderSkuCard.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("delflag", DeleteFlagEnum.IS_NOT_DELETE.getCode());
        criteria.andIn("orderSkuId", orderSkuIds);
        return orderSkuCardMapper.selectByExample(condition);
    }

    public List<OrderSkuCard> findByCardNumbers(List<String> cardNumbers){
        Condition condition = new Condition(OrderSkuCard.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("delflag", DeleteFlagEnum.IS_NOT_DELETE.getCode());
        criteria.andIn("cardNumber", cardNumbers);
        return orderSkuCardMapper.selectByExample(condition);
    }


    public List<OrderSkuCard> findByOrderNoOrSrcNo(String orderNoOrSrcNo) {
        return orderSkuCardMapper.findByOrderNoOrSrcNo(orderNoOrSrcNo);
    }
}
