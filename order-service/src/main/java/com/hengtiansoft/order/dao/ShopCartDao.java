package com.hengtiansoft.order.dao;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.hengtiansoft.common.enumeration.DeleteFlagEnum;
import com.hengtiansoft.order.entity.mapper.ShopCartMapper;
import com.hengtiansoft.order.entity.po.ShopCart;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 购物车数据基础层
 */
@Repository
public class ShopCartDao {

    @Resource
    private ShopCartMapper shopCartMapper;


    private int updateById(ShopCart shopCart) {
        return shopCartMapper.updateByPrimaryKeySelective(shopCart);
    }

    public int delete(List<Long> ids) {

        if (CollectionUtils.isNotEmpty(ids)) {

            return shopCartMapper.deleteBatch(ids);
        }

        return 0;
    }

    public void updateByUserId(ShopCart record, Long userId){
        Example example = new Example(ShopCart.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("userId", userId);
        shopCartMapper.updateByExampleSelective(record, example);
    }

    public ShopCart getOne(Long id){
        return shopCartMapper.selectByPrimaryKey(id);
    }


    public List<ShopCart> findByUserId(Long userId, Integer source) {
        Example example = new Example(ShopCart.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("userId", userId);
        if(Objects.nonNull(source)){
            criteria.andEqualTo("source", source);
        }
        criteria.andEqualTo("delflag", DeleteFlagEnum.IS_NOT_DELETE.getCode());
        example.setOrderByClause("create_time desc");
        return shopCartMapper.selectByExample(example);
    }

    public List<ShopCart> findByIds(List<Long> ids, Long userId) {
        Example example = new Example(ShopCart.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andIn("id", ids);
        criteria.andEqualTo("userId", userId);
        criteria.andEqualTo("delflag", DeleteFlagEnum.IS_NOT_DELETE.getCode());
        example.setOrderByClause("create_time desc");
        return shopCartMapper.selectByExample(example);
    }

    public List<ShopCart> findByUserId(Long userId) {
        Example example = new Example(ShopCart.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("userId", userId);
        criteria.andEqualTo("delflag", DeleteFlagEnum.IS_NOT_DELETE.getCode());
        example.setOrderByClause("create_time desc");
        return shopCartMapper.selectByExample(example);
    }

    public ShopCart findByUserIdAndSkuId(Long userId, Long skuId, String skuCode) {
        Example example = new Example(ShopCart.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("userId", userId);
        criteria.andEqualTo("skuId", skuId);
        criteria.andEqualTo("skuCode", skuCode);
        criteria.andEqualTo("cycleId", 0 );
        criteria.andEqualTo("delflag", DeleteFlagEnum.IS_NOT_DELETE.getCode());
        List<ShopCart> shopCarts = shopCartMapper.selectByExample(example);

        if (CollectionUtils.isEmpty(shopCarts)) {
            return null;
        }

        return shopCarts.get(0);
    }

    public ShopCart findByUserIdAndSkuId(Long userId, Long skuId) {
        Example example = new Example(ShopCart.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("userId", userId);
        criteria.andEqualTo("skuId", skuId);
        criteria.andEqualTo("cycleId", 0 );
        criteria.andEqualTo("delflag", DeleteFlagEnum.IS_NOT_DELETE.getCode());
        List<ShopCart> shopCarts = shopCartMapper.selectByExample(example);

        if (CollectionUtils.isEmpty(shopCarts)) {
            return null;
        }

        return shopCarts.get(0);
    }


    public int count(Long userId) {
        Example example = new Example(ShopCart.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("userId", userId);
        criteria.andEqualTo("delflag", DeleteFlagEnum.IS_NOT_DELETE.getCode());
        return  shopCartMapper.selectCountByExample(example);
    }


    public int save(ShopCart shopCart) {
        if (shopCart.getId() != null) {
            return this.updateById(shopCart);
        } else {
            return shopCartMapper.insertSelective(shopCart);
        }
    }

    @DS("db3")
    public List<ShopCart> selectByUserIdsAndTime(List<Long> userIds, Date startTime, Date endTime){
        return shopCartMapper.selectByUserIdsAndTime(userIds, startTime, endTime);
    }

}
