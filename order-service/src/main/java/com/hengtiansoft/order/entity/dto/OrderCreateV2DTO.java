package com.hengtiansoft.order.entity.dto;


import com.hengtiansoft.privilege.entity.dto.FullReduceMallDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class OrderCreateV2DTO {

    @ApiModelProperty("订单编号")
    private String orderNo;

    @ApiModelProperty("用户id")
    private Long userId;

    @ApiModelProperty("用户订单昵称")
    private String orderNickName;

    @ApiModelProperty("应付金额-根据售价计算")
    private BigDecimal totalAmount;

    @ApiModelProperty("实付金额")
    private BigDecimal realAmount;

    @ApiModelProperty("标价金额-根据标价计算")
    private BigDecimal productAmount;

    @ApiModelProperty("满减优惠金额")
    private BigDecimal reduceDiscount;

    @ApiModelProperty("优惠券优惠金额")
    private BigDecimal couponDiscount;

    @ApiModelProperty("拼团优惠金额")
    private BigDecimal ptDiscount = BigDecimal.ZERO;

    @ApiModelProperty("积分抵现金额")
    private BigDecimal pointDiscount = BigDecimal.ZERO;

    @ApiModelProperty("订单sku数据")
    private List<OrderSkuCreateV2DTO> orderSkuCreateV2DTOList;

    @ApiModelProperty("收货地址id")
    private Long addressId;

    @ApiModelProperty("优惠券id")
    private Long couponId;

    @ApiModelProperty("赠品券id")
    private Long couponGiftId;

    @ApiModelProperty("赠品id")
    private Long giftSkuId;

    @ApiModelProperty("赠品数量")
    private Integer giftCount;

    @ApiModelProperty("订单类型（1-奶卡订单，2-续卡订单 3-单品订单 4-周期购订单）")
    private Integer orderType;

    private List<FullReduceMallDTO> fullReduceMallList;

    @ApiModelProperty("使用积分抵扣：0否1是")
    private Integer usePoint;

    @ApiModelProperty("积分")
    private Integer point = 0;

    @ApiModelProperty("是否购物车下单 0否1是")
    private Integer isShopCart;

    @ApiModelProperty("订单归属标记")
    private String belongMark;

    @ApiModelProperty("企业微信销售映射表id")
    private String wxsId;

    @ApiModelProperty("unionid")
    private String unionId;

    @ApiModelProperty("社区帖子引导下单")
    private Long postId;
}
