package com.hengtiansoft.order.manager.impl;

import com.google.common.collect.Lists;
import com.hengtiansoft.common.entity.exception.BusinessException;
import com.hengtiansoft.common.enumeration.DeleteFlagEnum;
import com.hengtiansoft.common.enumeration.FlagEnum;
import com.hengtiansoft.common.redis.RedisOperation;
import com.hengtiansoft.common.util.CompareUtil;
import com.hengtiansoft.item.entity.dto.CouponReceiveResponDTO;
import com.hengtiansoft.order.dao.CouponInfoDao;
import com.hengtiansoft.order.entity.dto.CouponInfoReceiveDTO;
import com.hengtiansoft.order.entity.mapper.CouponInfoMapper;
import com.hengtiansoft.order.entity.po.CouponInfo;
import com.hengtiansoft.order.entity.po.CouponRule;
import com.hengtiansoft.order.enums.CouponInfoStatusTypeEnum;
import com.hengtiansoft.order.manager.CouponInfoManager;
import com.hengtiansoft.order.manager.CouponRuleManager;
import com.hengtiansoft.order.util.CouponUtil;
import com.hengtiansoft.user.enums.UserLogOffEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;


@Slf4j
@Component
public class CouponInfoManagerImpl implements CouponInfoManager {

    @Autowired
    private CouponInfoDao couponInfoDao;
    @Autowired
    private RedisOperation redisOperation;
    @Resource
    private CouponInfoMapper couponInfoMapper;
    @Resource
    private CouponRuleManager couponRuleManager;


    @Override
    public List<CouponInfo> queryReceivedByUserIdAndRuleIds(List<Long> ruleIdList, Long userId) {
        return couponInfoDao.countReceivedByUserIdAndRuleIds(ruleIdList, userId);
    }

    @Override
    public void insertNotLogined(CouponReceiveResponDTO couponReceiveResponDTO, CouponRule couponRule) {
        Long num = redisOperation.incr("Coupon:receive:ruleId:" + couponRule.getId() + "_openId:" + couponReceiveResponDTO.getOpenId(), 1);
        redisOperation.expire("Coupon:receive:ruleId:" + couponRule.getId() + "_openId:" + couponReceiveResponDTO.getOpenId(), 5000L);
        if (num >= 2) {
            throw new BusinessException("调用太频繁，请稍后再试");
        }
        // 检查是否领过
        CouponInfo couponInfoWithNotLogined = this.findByOpenIdAndStockId(couponReceiveResponDTO.getOpenId(), couponReceiveResponDTO.getStockId());
        // 领过
        if(couponInfoWithNotLogined != null){
            return;
        }else{
        // 没领过
            CouponInfo couponInfo = CouponUtil.buildCouponInfo(CouponInfoStatusTypeEnum.NOT_RECEIVE, null, couponReceiveResponDTO, couponRule);
            if (couponInfoDao.insert(couponInfo) == 0) {
                throw new BusinessException("未登录用户领取失败!");
            }
        }
    }

    @Override
    public CouponInfo findByOpenIdAndStockId(String openId, String stockId){
        List<CouponInfo> couponInfoNotLogined = couponInfoDao.findByOpenIdAndStockId(openId, stockId);
        if(CollectionUtils.isEmpty(couponInfoNotLogined)){
            return null;
        }
        if(couponInfoNotLogined.size() > 1){
            log.info("未登录用户同类型优惠券多于1条 . 参数 --> openId:{}, stockId:{}", openId, stockId);
            throw new BusinessException("未登录用户同类型优惠券多于1条");
        }
        return couponInfoNotLogined.get(0);
    }

    @Override
    public List<CouponInfo> findByOpenId(String openId) {
        List<CouponInfo> couponInfoNotLogined = couponInfoDao.findUserIdNullByOpenId(openId);
        if(CollectionUtils.isEmpty(couponInfoNotLogined)){
            return Lists.newArrayList();
        }
        return couponInfoNotLogined;
    }

    @Override
    public void updateNotLogined(Long userId, String openId) {
        couponInfoDao.updateNotLogined(userId, openId);
    }

    @Override
    public void logOffCoupon(Long userId) {
        CouponInfo couponInfo = new CouponInfo();
        couponInfo.setLogOff(UserLogOffEnum.LOGGED_OUT.getCode());
        couponInfo.setUpdateTime(new Date());
        couponInfoDao.updateByUserId(couponInfo, userId);
    }

    @Override
    public void changeUserId(Long oldUserId, Long newUserId) {
        CouponInfo couponInfo = new CouponInfo();
        couponInfo.setUserId(newUserId);
        couponInfoDao.updateByUserId(couponInfo, oldUserId);
    }

    @Override
    public List<CouponInfo> findByUserId(Long id) {
        return couponInfoDao.findByUserId(id);
    }

    @Override
    public List<CouponInfo> findByIds(List<Long> ids, Long userId) {
        return couponInfoDao.findByIds(ids, userId);
    }

    @Override
    public List<CouponInfo> selectCouponByStatusAndChannel(Long userId, Integer channel, List<Integer> statusList) {
        return couponInfoMapper.selectCouponByStatusAndChannel(userId, channel, statusList);
    }

    @Override
    public List<CouponInfo> findNotReceiveByRuleId(Long userId, Long ruleId) {
        return couponInfoDao.findNotReceiveByRuleId(userId, ruleId);
    }

    @Override
    public void insertList(List<CouponInfo> couponInfoList) {
        couponInfoDao.insertList(couponInfoList);
    }

    @Override
    public void deactivateCoupon(List<Long> couponIds) {
        if(CollectionUtils.isEmpty(couponIds)){
            return;
        }
        List<CouponInfo> couponInfoList = couponInfoDao.findByIds(couponIds);
        for (CouponInfo couponInfo: couponInfoList) {
            if(null == couponInfo
                    || StringUtils.isBlank(couponInfo.getStockId())
                    || StringUtils.isBlank(couponInfo.getCouponCode())){
                continue;
            }
            couponRuleManager.deactivateCoupon(couponInfo.getStockId(), couponInfo.getCouponCode());
        }
    }

    @Override
    public List<CouponInfo> findByRuleIdAndUserIdAndStatus(Long id, Long userId, Integer status) {
        Condition condition = new Condition(CouponInfo.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("ruleId", id);
        criteria.andEqualTo("userId", userId);
        criteria.andEqualTo("status", status);
        criteria.andEqualTo("delflag", DeleteFlagEnum.IS_NOT_DELETE.getCode());
        return couponInfoMapper.selectByExample(condition);
    }

    @Override
    public void updateStatusByIds(List<Long> ids, Integer status) {
        couponInfoDao.updateStatusByIds(ids, status);
    }

    @Override
    public void deleteByIds(List<Long> ids) {
        couponInfoDao.deleteByIds(ids);
    }

    @Override
    public List<CouponInfo> findByCondition(CouponInfoReceiveDTO dto) {
        Condition condition = new Condition(CouponInfo.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("ruleId", dto.getCouponRuleId());
        if(null != dto.getUserId()){
            criteria.andEqualTo("userId", dto.getUserId());
        }
        if(null != dto.getStatus()){
            criteria.andEqualTo("status", dto.getStatus());
        }
        if(CollectionUtils.isNotEmpty(dto.getStatusList())){
            criteria.andIn("status", dto.getStatusList());
        }
        if(dto.getReceiveTimeStart() != null){
            criteria.andGreaterThanOrEqualTo("receiveTime", dto.getReceiveTimeStart());
        }
        if(dto.getReceiveTimeEnd() != null){
            criteria.andLessThanOrEqualTo("receiveTime", dto.getReceiveTimeEnd());
        }
        if(dto.getUseTimeStart() != null){
            criteria.andGreaterThanOrEqualTo("useTime", dto.getUseTimeStart());
        }
        if(dto.getUseTimeEnd() != null){
            criteria.andLessThanOrEqualTo("useTime", dto.getUseTimeEnd());
        }
        criteria.andEqualTo("delflag", DeleteFlagEnum.IS_NOT_DELETE.getCode());
        if(FlagEnum.YES.getCode().equals(dto.getWithCouponRule())){
            condition.setOrderByClause("CASE WHEN status = 0 THEN 1 ELSE 2 END, receive_time DESC");
            // condition.setOrderByClause("");
        }else{
            if(CollectionUtils.isNotEmpty(dto.getStatusList()) && CompareUtil.isEqualList(dto.getStatusList(), Lists.newArrayList(CouponInfoStatusTypeEnum.USED.getCode()))){
                condition.orderBy("useTime").desc();
            }else{
                condition.orderBy("receiveTime").desc();
            }
        }
        return couponInfoMapper.selectByExample(condition);
    }

    @Override
    public List<Long> findRuleIdList(List<Long> couponIds) {
        if(CollectionUtils.isEmpty(couponIds)){
            return Lists.newArrayList();
        }
        return couponInfoMapper.findRuleIdList(couponIds);
    }

    @Override
    public List<Long> findRuleIdListByOrderNo(String orderNo) {
        return couponInfoMapper.findRuleIdListByOrderNo(orderNo);
    }

    @Override
    public CouponInfo findById(Long couponInfoId) {
        return couponInfoDao.findById(couponInfoId);
    }

    @Override
    public List<CouponInfo> findNotReceiveByChannel(Long userId, List<Integer> channelList) {
        return couponInfoMapper.findNotReceiveByChannel(userId, channelList);
    }

    @Override
    public int countReceivedByUserIdAndRuleId(Long ruleId, Long userId) {
        return couponInfoDao.countReceivedByUserIdAndRuleId(ruleId, userId);
    }


}
