package com.hengtiansoft.order.manager.impl;

import cn.hutool.core.lang.Assert;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.hengtiansoft.common.constant.ResponseCode;
import com.hengtiansoft.common.entity.dto.PageParams;
import com.hengtiansoft.common.entity.exception.BusinessException;
import com.hengtiansoft.common.enumeration.BasicFlagEnum;
import com.hengtiansoft.common.redis.RedisOperation;
import com.hengtiansoft.common.util.BeanUtils;
import com.hengtiansoft.common.util.CollectionUtil;
import com.hengtiansoft.common.util.DateUtil;
import com.hengtiansoft.common.util.StreamUtils;
import com.hengtiansoft.item.dao.CardDao;
import com.hengtiansoft.item.dao.ProductDao;
import com.hengtiansoft.item.entity.dto.CouponReceiveResponDTO;
import com.hengtiansoft.item.entity.dto.CouponRuleListDTO;
import com.hengtiansoft.item.entity.dto.ProductBaseDTO;
import com.hengtiansoft.item.entity.dto.ProductBaseSearchDTO;
import com.hengtiansoft.item.entity.po.Card;
import com.hengtiansoft.item.entity.po.Product;
import com.hengtiansoft.item.enumeration.ProductSaleStatusEnum;
import com.hengtiansoft.item.enumeration.ProductShowEnum;
import com.hengtiansoft.item.enumeration.ProductTypeEnum;
import com.hengtiansoft.item.interfaces.ProductManager;
import com.hengtiansoft.order.dao.*;
import com.hengtiansoft.order.entity.dto.*;
import com.hengtiansoft.order.entity.mapper.CouponInfoMapper;
import com.hengtiansoft.order.entity.po.*;
import com.hengtiansoft.order.entity.vo.*;
import com.hengtiansoft.order.enums.*;
import com.hengtiansoft.order.manager.CouponPrivilegeManager;
import com.hengtiansoft.order.manager.CouponRangeManager;
import com.hengtiansoft.order.manager.CouponRuleManager;
import com.hengtiansoft.order.util.CouponUtil;
import com.hengtiansoft.order.util.GenNoUtil;
import com.hengtiansoft.pay.config.PropertiesConfig;
import com.hengtiansoft.pay.config.WeChatMiniProgramConfig;
import com.hengtiansoft.pay.utils.WxHttpClientTemplate;
import com.hengtiansoft.privilege.entity.po.PrivilegeItemUser;
import com.hengtiansoft.privilege.manager.PeopleManager;
import com.hengtiansoft.privilege.util.MonitorPageUtil;
import com.hengtiansoft.thirdpart.entity.dto.wechat.*;
import com.hengtiansoft.thirdpart.enumeration.BaiduLinkTypeEnum;
import com.hengtiansoft.thirdpart.enumeration.MiniCouponAmountLimitTypeEnum;
import com.hengtiansoft.thirdpart.enumeration.MiniCouponWayTypeEnum;
import com.hengtiansoft.thirdpart.enumeration.WeixinUrlLinkTypeEnum;
import com.hengtiansoft.thirdpart.interfaces.BaiduManager;
import com.hengtiansoft.thirdpart.interfaces.WeChatMiniCouponManager;
import com.hengtiansoft.user.dao.CustomerUserLabelDao;
import com.hengtiansoft.user.entity.dto.CustomerUserPageDTO;
import com.hengtiansoft.user.entity.po.CustomerUser;
import com.hengtiansoft.user.entity.po.CustomerUserLabel;
import com.hengtiansoft.user.entity.vo.CustomerUserVO;
import com.hengtiansoft.user.enums.UserLogOffEnum;
import com.hengtiansoft.user.manager.CustomerUserManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.MessageFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Component
public class CouponRuleManagerImpl implements CouponRuleManager {

    @Autowired
    private CouponRuleDao couponRuleDao;
    @Autowired
    private CouponPrivilegeDao couponPrivilegeDao;
    @Autowired
    private CouponRangeDao couponRangeDao;
    @Autowired
    private CouponInfoDao couponInfoDao;
    @Autowired
    private CouponInfoMapper couponInfoMapper;
    @Autowired
    private WeChatMiniCouponManager weChatMiniCouponManager;
    @Autowired
    private WeChatMiniProgramConfig weChatMiniProgramConfig;
    @Autowired
    private WxHttpClientTemplate wxHttpClientTemplate;
    @Autowired
    private RedisOperation redisOperation;
    @Autowired
    private CouponPrivilegeManager couponPrivilegeManager;
    @Autowired
    private CardDao cardDao;
    @Autowired
    private ProductDao productDao;
    @Autowired
    private CouponCallbackLogDao couponCallbackLogDao;
    @Autowired
    private CustomerUserManager customerUserManager;
    @Autowired
    private CouponMiniCallbackLogDao couponMiniCallbackLogDao;
    @Autowired
    private CouponGiftRangeDao couponGiftRangeDao;
    @Autowired
    private CouponRangeManager couponRangeManager;
    @Autowired
    private ProductManager productManager;
    @Autowired
    private PropertiesConfig propertiesConfig;
    @Resource
    private PeopleManager peopleManager;
    @Resource
    private GenNoUtil genNoUtil;
    @Resource
    private BaiduManager baiduManager;
    @Resource
    private RedissonClient redissonClient;
    @Resource
    private CustomerUserLabelDao customerUserLabelDao;

    private static final String CREATE_COUPON = "https://api.mch.weixin.qq.com/v3/marketing/busifavor/stocks";
    private static final String QUERY_COUPON = "https://api.mch.weixin.qq.com/v3/marketing/busifavor/stocks/{0}";

    private static final String BUDGET_COUPON = "https://api.mch.weixin.qq.com/v3/marketing/busifavor/stocks/{0}/budget";
    private static final String UPDATE_COUPON = "https://api.mch.weixin.qq.com/v3/marketing/busifavor/stocks/{0}";

    private static final String USE_COUPON = "https://api.mch.weixin.qq.com/v3/marketing/busifavor/coupons/use";

    private static final String RETURN_COUPON = "https://api.mch.weixin.qq.com/v3/marketing/busifavor/coupons/return";

    private static final String CREATE_PARTNER = "https://api.mch.weixin.qq.com/v3/marketing/partnerships/build";

    private static final String QUERY_PARTNER = "https://api.mch.weixin.qq.com/v3/marketing/partnerships";

    private static final String CALLBACKS = "https://api.mch.weixin.qq.com/v3/marketing/busifavor/callbacks";

    private static final String DEACTIVATE_COUPON = "https://api.mch.weixin.qq.com/v3/marketing/busifavor/coupons/deactivate";



    @Override
    public List<CouponRule> couponRuleList(CouponRuleListDTO dto) {
        return couponRuleDao.search(dto);
    }

    @Override
    public CouponRule findById(Long id) {
        return couponRuleDao.findById(id);
    }

    @Override
    public void createShortLink(String localH5Domain, CouponRule couponRule) {
        String longLink = localH5Domain + MonitorPageUtil.H5_WECHAT_URL + "?id=" + couponRule.getId()+"&type="+ WeixinUrlLinkTypeEnum.COUPON.getCode();
        String shortLink = baiduManager.getShortLink(longLink, BaiduLinkTypeEnum.ONE_YEAR.getCode());
        CouponRule updateRule = new CouponRule();
        updateRule.setId(couponRule.getId());
        updateRule.setShortLink(shortLink);
        couponRuleDao.updateByIdSelective(updateRule);
    }

    @Override
    public String createFreeShortLink(String localH5Domain, Long freeTrialId) {
        String longLink = localH5Domain + MonitorPageUtil.H5_WECHAT_URL + "?id=" + freeTrialId + "&type="+ WeixinUrlLinkTypeEnum.FREE_TRIAL.getCode();
        String shortLink = baiduManager.getShortLink(longLink, BaiduLinkTypeEnum.ONE_YEAR.getCode());
        return shortLink;
    }

    @Override
    public String minicreateCouponRule(Long id) {

        CouponRule rule = couponRuleDao.findById(id);
        if (Objects.isNull(rule)) {
            throw new BusinessException("优惠券规则不存在，请重新选择！");
        }
        Map<String, Object> wxMiniCouponRequestMap = new HashMap<>();
        wxMiniCouponRequestMap.put("stock_name", rule.getCouponName());
        wxMiniCouponRequestMap.put("belong_merchant", weChatMiniProgramConfig.getMchID());


        Map<String, Object> couponUseRuleMap = new HashMap<>();
        Map<String, Object> couponAvailableTimeMap = new HashMap<>();
        couponAvailableTimeMap.put("available_begin_time", DateUtil.dateToString(rule.getStartTime(), DateUtil.SIMPLE_FMT_TIMEZONE));
        couponAvailableTimeMap.put("available_end_time", DateUtil.dateToString(rule.getEndTime(), DateUtil.SIMPLE_FMT_TIMEZONE));
        if(Objects.nonNull(rule.getEffectiveTime())){
            couponAvailableTimeMap.put("available_day_after_receive",rule.getEffectiveTime());
            if(Objects.nonNull(rule.getDelayTime())){
                couponAvailableTimeMap.put("wait_days_after_receive",rule.getDelayTime());
            }
        }

        couponUseRuleMap.put("coupon_available_time", couponAvailableTimeMap);
        couponUseRuleMap.put("use_method", "MINI_PROGRAMS");
        couponUseRuleMap.put("mini_programs_appid", weChatMiniProgramConfig.getAppID());
        couponUseRuleMap.put("mini_programs_path", "/packageA/pages/myCoupons/myCoupons");
        wxMiniCouponRequestMap.put("coupon_use_rule", couponUseRuleMap);
        Map<String, Object> stockSendRuleMap = new HashMap<>();

        stockSendRuleMap.put("max_coupons", rule.getIssuedQuantity().intValue());
        // stockSendRuleMap.put("max_coupons_per_user", rule.getReceiveNum());
        // 全部默认99，不用微信区校验用户领取次数限制
        stockSendRuleMap.put("max_coupons_per_user", 99);

        wxMiniCouponRequestMap.put("stock_send_rule", stockSendRuleMap);
        wxMiniCouponRequestMap.put("out_request_no", String.valueOf(System.currentTimeMillis()));
        wxMiniCouponRequestMap.put("coupon_code_mode", "WECHATPAY_MODE");

        Map<String,Object> notifyConfig=new HashMap<>();
        notifyConfig.put("notify_appid",weChatMiniProgramConfig.getAppID());
        wxMiniCouponRequestMap.put("notify_config",notifyConfig);
        if(CouponTypeEnum.GIFT_COUPON.getCode().equals(rule.getCouponType())){
            // wxMiniCouponRequestMap.put("goods_name", "指定商品");
            Map<String, Object> exchangeCouponMap = new HashMap<>();
            exchangeCouponMap.put("exchange_price", 0);//单品换购价
            exchangeCouponMap.put("transaction_minimum", rule.getAmountFull().multiply(new BigDecimal(100)).intValue());//消费门槛
            wxMiniCouponRequestMap.put("stock_type", "EXCHANGE");
            couponUseRuleMap.put("exchange_coupon", exchangeCouponMap);
        }else{
            MiniCouponAmountLimitTypeEnum miniCouponAmountLimitTypeEnum = MiniCouponAmountLimitTypeEnum.getEnumByCode(rule.getAmountLimit());
            MiniCouponWayTypeEnum miniCouponWayTypeEnum = MiniCouponWayTypeEnum.getEnumByCode(rule.getCouponWay());
            if (null == miniCouponAmountLimitTypeEnum) {
                throw new BusinessException("限制类型有误");
            }
            switch (miniCouponWayTypeEnum) {
                case NORMAL:
                    // wxMiniCouponRequestMap.put("goods_name", "指定商品");
                    Map<String, Object> fixedNormalCouponMap = new HashMap<>();
                    fixedNormalCouponMap.put("discount_amount", rule.getAmountReduce().multiply(new BigDecimal(100)).intValue());
                    fixedNormalCouponMap.put("transaction_minimum", rule.getAmountFull().multiply(new BigDecimal(100)).intValue());
                    wxMiniCouponRequestMap.put("stock_type", "NORMAL");
                    couponUseRuleMap.put("fixed_normal_coupon", fixedNormalCouponMap);
                    break;
                case DISCOUNT:
                    // wxMiniCouponRequestMap.put("goods_name", "指定商品");
                    wxMiniCouponRequestMap.put("stock_type", "DISCOUNT");
                    Map<String, Object> discountCouponMap = new HashMap<>();
                    discountCouponMap.put("discount_percent", rule.getDiscount());
                    discountCouponMap.put("transaction_minimum", rule.getAmountFull().multiply(new BigDecimal(100)).intValue());
                    couponUseRuleMap.put("discount_coupon", discountCouponMap);
                    break;
                default:
                    throw new BusinessException("当前发布状态有误");
            }
        }
        Map<String, Object> displayPatternInfoMap = new HashMap<>();

        CouponRangeTypeEnum rangeTypeEnum = CouponRangeTypeEnum.getEnumByCode(rule.getCouponRange());
        List<CouponRange> couponRangeList;
        ProductBaseSearchDTO dto;
        List<Product> productList;
        switch (rangeTypeEnum) {
            case APPOINT_CARD_CATEGORY:
                couponRangeList = couponRangeDao.queryByRuleId(rule.getId());
                List<Long> categoryIdList = StreamUtils.toList(couponRangeList, CouponRange::getCategoryId);
                if(CollectionUtils.isNotEmpty(categoryIdList)){
                    dto = new ProductBaseSearchDTO();
                    dto.setProductType(ProductTypeEnum.PACKAGE.getCode());
                    dto.setCardCategoryIds(categoryIdList);
                    productList = productDao.findByConditionSort(dto);
                    if(CollectionUtils.isEmpty(productList)){
                        throw new BusinessException("该优惠券商品范围失效，请重新选择商品范围！");
                    }
                    // StringBuilder sb1 = new StringBuilder();
                    // for (Product product : productList) {
                    //     sb1.append(product.getProductName()).append("、");
                    // }
                    // limitDescLength(sb1);
                    // displayPatternInfoMap.put("description", "1、本券可用于:" + sb1.substring(0, sb1.length() - 1).toString());
                    displayPatternInfoMap.put("description", rule.getUseRule());
                }
                break;
            case APPOINT_CARD:
                // couponRangeList = couponRangeDao.queryByRuleId(rule.getId());
                // StringBuilder sb2 = new StringBuilder();
                // for (CouponRange range : couponRangeList) {
                //     sb2.append(range.getProductName()).append("、");
                // }
                // limitDescLength(sb2);
                // displayPatternInfoMap.put("description", "1、本券可用于:" + sb2.substring(0, sb2.length() - 1).toString());
                displayPatternInfoMap.put("description", rule.getUseRule());
                break;
            case EXCLUDE_CARD_CATEGORY:
                couponRangeList = couponRangeDao.queryByRuleId(rule.getId());
                List<Long> categoryIdExcludeList = StreamUtils.toList(couponRangeList, CouponRange::getCategoryId);
                if(CollectionUtils.isNotEmpty(categoryIdExcludeList)){
                    dto = new ProductBaseSearchDTO();
                    dto.setCardCategoryIds(categoryIdExcludeList);
                    dto.setProductType(ProductTypeEnum.PACKAGE.getCode());
                    productList = productDao.findByConditionSort(dto);
                    if(CollectionUtils.isEmpty(productList)){
                        throw new BusinessException("该优惠券商品范围失效，请重新选择商品范围！");
                    }
                    // StringBuilder sb3 = new StringBuilder();
                    // for (Product product : productList) {
                    //     sb3.append(product.getProductName()).append("、");
                    // }
                    // limitDescLength(sb3);
                    // wxMiniCouponRequestMap.put("goods_name", "排除商品");
                    // displayPatternInfoMap.put("description", "1、本券不可用于:" + sb3.substring(0, sb3.length() - 1).toString());
                    displayPatternInfoMap.put("description", rule.getUseRule());
                }
                break;
            case EXCLUDE_CARD:
                // couponRangeList = couponRangeDao.queryByRuleId(rule.getId());
                // StringBuilder sb4 = new StringBuilder();
                // for (CouponRange range : couponRangeList) {
                //     sb4.append(range.getProductName()).append("、");
                // }
                // limitDescLength(sb4);
                // wxMiniCouponRequestMap.put("goods_name", "排除商品");
                // displayPatternInfoMap.put("description", "1、本券不可用于:" + sb4.substring(0, sb4.length() - 1).toString());
                displayPatternInfoMap.put("description", rule.getUseRule());
                break;
            case ALL:
                couponRangeList = couponRangeDao.queryByRuleId(rule.getId());
                // StringBuilder sbAll = new StringBuilder();
                // String description = "";
                if(CollectionUtils.isNotEmpty(couponRangeList)){
                    // sbAll.append("除");
                    // for (CouponRange range : couponRangeList) {
                    //     sbAll.append(range.getProductName()).append("、");
                    // }
                    // description = description + sbAll.substring(0, sbAll.length() - 1);
                    // if(description.length() > 900 ){
                    //     description = description.substring(0,900) + "等";
                    // }
                    // description = description + "以外。";
                    wxMiniCouponRequestMap.put("goods_name", "适用于全场部分商品");
                }else{
                    // description = "1、本券可用于全场商品";
                    wxMiniCouponRequestMap.put("goods_name", "全场商品可用");
                }
                // displayPatternInfoMap.put("description", description);
                displayPatternInfoMap.put("description", rule.getUseRule());
                break;
            case APPOINT_CATEGORY:
                couponRangeList = couponRangeDao.queryByRuleId(rule.getId());
                List<Long> cateList = StreamUtils.toList(couponRangeList, CouponRange::getCategoryId);
                if (CollectionUtils.isNotEmpty(cateList)) {
                    dto = new ProductBaseSearchDTO();
                    dto.setCateIds(cateList);
                    dto.setProductTypeList(Arrays.asList(ProductTypeEnum.SINGLE_PRODUCT.getCode(),ProductTypeEnum.ENTITY_CARD.getCode()));
                    productList = productDao.findByConditionSort(dto);
                    if(CollectionUtils.isEmpty(productList)){
                        throw new BusinessException("该优惠券商品范围失效，请重新选择商品范围！");
                    }
                    // StringBuilder sb5 = new StringBuilder();
                    // for (Product product : productList) {
                    //     sb5.append(product.getProductName()).append("、");
                    // }
                    // limitDescLength(sb5);
                    // displayPatternInfoMap.put("description", "1、本券可用于:" + sb5.substring(0, sb5.length() - 1).toString());
                    displayPatternInfoMap.put("description", rule.getUseRule());
                }

                break;
            case APPOINT_PRODUCT:
                // couponRangeList = couponRangeDao.queryByRuleId(rule.getId());
                // StringBuilder sb6 = new StringBuilder();
                // for (CouponRange range : couponRangeList) {
                //     sb6.append(range.getProductName()).append("、");
                // }
                // limitDescLength(sb6);
                // displayPatternInfoMap.put("description", "1、本券可用于:" + sb6.substring(0, sb6.length() - 1).toString());
                displayPatternInfoMap.put("description", rule.getUseRule());
                break;
            case EXCLUDE_CATEGORY:
                couponRangeList = couponRangeDao.queryByRuleId(rule.getId());
                List<Long> exCateList = StreamUtils.toList(couponRangeList, CouponRange::getCategoryId);
                if (CollectionUtils.isNotEmpty(exCateList)) {
                    dto = new ProductBaseSearchDTO();
                    dto.setCateIds(exCateList);
                    dto.setProductTypeList(Arrays.asList(ProductTypeEnum.SINGLE_PRODUCT.getCode(),ProductTypeEnum.ENTITY_CARD.getCode()));
                    productList = productDao.findByConditionSort(dto);
                    if(CollectionUtils.isEmpty(productList)){
                        throw new BusinessException("该优惠券商品范围失效，请重新选择商品范围！");
                    }
                    // StringBuilder sb7 = new StringBuilder();
                    // for (Product product : productList) {
                    //     sb7.append(product.getProductName()).append("、");
                    // }
                    // limitDescLength(sb7);
                    // wxMiniCouponRequestMap.put("goods_name", "排除商品");
                    // displayPatternInfoMap.put("description", "1、本券不可用于:" + sb7.substring(0, sb7.length() - 1).toString());
                    displayPatternInfoMap.put("description", rule.getUseRule());
                }
                break;
            case EXCLUDE_PRODUCT:
                // couponRangeList = couponRangeDao.queryByRuleId(rule.getId());
                // StringBuilder sb8 = new StringBuilder();
                // for (CouponRange range : couponRangeList) {
                //     sb8.append(range.getProductName()).append("、");
                // }
                // limitDescLength(sb8);
                // wxMiniCouponRequestMap.put("goods_name", "排除商品");
                // displayPatternInfoMap.put("description", "1、本券不可用于:" + sb8.substring(0, sb8.length() - 1).toString());
                displayPatternInfoMap.put("description", rule.getUseRule());
                break;
            case APPOINT_ENTITY_CARD_CATEGORY:
                couponRangeList = couponRangeDao.queryByRuleId(rule.getId());
                List<Long> entityCardCateList = StreamUtils.toList(couponRangeList, CouponRange::getCategoryId);
                if (CollectionUtils.isNotEmpty(entityCardCateList)) {
                    dto = new ProductBaseSearchDTO();
                    dto.setCateIds(entityCardCateList);
                    dto.setProductTypeList(Arrays.asList(ProductTypeEnum.SINGLE_PRODUCT.getCode(),ProductTypeEnum.ENTITY_CARD.getCode()));
                    productList = productDao.findByConditionSort(dto);
                    if(CollectionUtils.isEmpty(productList)){
                        throw new BusinessException("该优惠券商品范围失效，请重新选择商品范围！");
                    }
                    // StringBuilder sb5 = new StringBuilder();
                    // for (Product product : productList) {
                    //     sb5.append(product.getProductName()).append("、");
                    // }
                    // limitDescLength(sb5);
                    // displayPatternInfoMap.put("description", "1、本券可用于:" + sb5.substring(0, sb5.length() - 1).toString());
                    displayPatternInfoMap.put("description", rule.getUseRule());
                }
                break;
            case APPOINT_ENTITY_CARD:
                // couponRangeList = couponRangeDao.queryByRuleId(rule.getId());
                // StringBuilder sb7 = new StringBuilder();
                // for (CouponRange range : couponRangeList) {
                //     sb7.append(range.getProductName()).append("、");
                // }
                // limitDescLength(sb7);
                // displayPatternInfoMap.put("description", "1、本券可用于:" + sb7.substring(0, sb7.length() - 1).toString());
                displayPatternInfoMap.put("description", rule.getUseRule());
                break;
            case EXCLUDE_ENTITY_CARD_CATEGORY:
                couponRangeList = couponRangeDao.queryByRuleId(rule.getId());
                List<Long> exEntityCateList = StreamUtils.toList(couponRangeList, CouponRange::getCategoryId);
                if (CollectionUtils.isNotEmpty(exEntityCateList)) {
                    dto = new ProductBaseSearchDTO();
                    dto.setCateIds(exEntityCateList);
                    dto.setProductTypeList(Arrays.asList(ProductTypeEnum.SINGLE_PRODUCT.getCode(),ProductTypeEnum.ENTITY_CARD.getCode()));
                    productList = productDao.findByConditionSort(dto);
                    if(CollectionUtils.isEmpty(productList)){
                        throw new BusinessException("该优惠券商品范围失效，请重新选择商品范围！");
                    }
                    // StringBuilder sb9 = new StringBuilder();
                    // for (Product product : productList) {
                    //     sb9.append(product.getProductName()).append("、");
                    // }
                    // limitDescLength(sb9);
                    // wxMiniCouponRequestMap.put("goods_name", "排除商品");
                    // displayPatternInfoMap.put("description", "1、本券不可用于:" + sb9.substring(0, sb9.length() - 1).toString());
                    displayPatternInfoMap.put("description", rule.getUseRule());
                }
                break;
            case EXCLUDE_ENTITY_CARD:
                // couponRangeList = couponRangeDao.queryByRuleId(rule.getId());
                // StringBuilder sb10 = new StringBuilder();
                // for (CouponRange range : couponRangeList) {
                //     sb10.append(range.getProductName()).append("、");
                // }
                // limitDescLength(sb10);
                // wxMiniCouponRequestMap.put("goods_name", "排除商品");
                // displayPatternInfoMap.put("description", "1、本券不可用于:" + sb10.substring(0, sb10.length() - 1).toString());
                displayPatternInfoMap.put("description", rule.getUseRule());
                break;
            case ALL_ECARD:
                // wxMiniCouponRequestMap.put("goods_name", "适用于全场电子卡");
                // displayPatternInfoMap.put("description", "1、本券可用于全场电子卡");
                displayPatternInfoMap.put("description", rule.getUseRule());
                break;
            case ALL_ENTITY_CARD:
                // wxMiniCouponRequestMap.put("goods_name", "适用于全场实体卡");
                // displayPatternInfoMap.put("description", "1、本券可用于全场实体卡");
                displayPatternInfoMap.put("description", rule.getUseRule());
                break;
            case ALL_SINGLE_PRODUCT:
                // wxMiniCouponRequestMap.put("goods_name", "适用于全场单品");
                // displayPatternInfoMap.put("description", "1、本券可用于全场单品");
                displayPatternInfoMap.put("description", rule.getUseRule());
                break;
            case INCLUDE_ALL:
                wxMiniCouponRequestMap.put("goods_name", "部分商品可用");
                displayPatternInfoMap.put("description", rule.getUseRule());
                break;
            case EXCLUDE_ALL:
                wxMiniCouponRequestMap.put("goods_name", "部分商品不可用");
                displayPatternInfoMap.put("description", rule.getUseRule());
                break;

        }
        displayPatternInfoMap.put("background_color", "Color070");
        displayPatternInfoMap.put("merchant_name", "认养一头牛订奶卡");
        displayPatternInfoMap.put("merchant_logo_url", "https://wxpaylogo.qpic.cn/wxpaylogo/PiajxSqBRaEIPAeia7Imvtsiaphas7ic6kC8QfAQr3hFoMabfVD8T70uCQ/0");
        wxMiniCouponRequestMap.put("display_pattern_info", displayPatternInfoMap);
        String stokId = StringUtils.EMPTY;
        try {
            log.info("微信小程序创建优惠券 .请求参数  --> wxMiniCouponRequestMap: {}", JSON.toJSONString(wxMiniCouponRequestMap));
            String result = wxHttpClientTemplate.doPost(CREATE_COUPON, JSON.toJSONString(wxMiniCouponRequestMap));
            log.info("微信小程序创建优惠券 .响应参数  --> result: {}", result);
            WxCreateCouponResponse response = JSON.parseObject(result, new TypeReference<WxCreateCouponResponse>() {
            });
            if (Objects.nonNull(response) && StringUtils.isNotBlank(response.getStockId())) {
                stokId = response.getStockId();
            }
        } catch (Exception e) {
            log.error("调微信小程序创建优惠券失败", e);
        }

        return stokId;
    }

    private void limitDescLength(StringBuilder sb) {
        int maxLength = 950;
        if (sb.length() > maxLength) {
            sb.setLength(maxLength); // 截取到限制长度
            sb.replace(sb.length() - 1, sb.length(), "...."); // 使用省略号代替最后一个字符
        }
    }

    @Override
    public void miniUseCoupon(CouponInfo couponInfo) {
        log.info("调微信小程序核销优惠券. request:{}", JSON.toJSONString(couponInfo));
        Map<String, Object> wxMiniCouponRequestMap = new HashMap<>();
        wxMiniCouponRequestMap.put("coupon_code", couponInfo.getCouponCode());
        wxMiniCouponRequestMap.put("stock_id", couponInfo.getStockId());
        wxMiniCouponRequestMap.put("appid", weChatMiniProgramConfig.getAppID());
        wxMiniCouponRequestMap.put("use_time", DateUtil.dateToString(new Date(), DateUtil.SIMPLE_FMT_TIMEZONE));
        wxMiniCouponRequestMap.put("use_request_no", String.valueOf(System.currentTimeMillis()));
        try {
            String result = wxHttpClientTemplate.doPost(USE_COUPON, JSON.toJSONString(wxMiniCouponRequestMap));
            WxUseCouponResponse response = JSON.parseObject(result, new TypeReference<WxUseCouponResponse>() {
            });
            log.info("调微信小程序核销优惠券---result:{}", result);
        } catch (Exception e) {
            log.error("调微信小程序核销优惠券失败", e);
        }
    }

    @Override
    public void miniReturnCoupon(CouponInfo couponInfo) {
        log.info("调微信小程序申请退券. request:{}", JSON.toJSONString(couponInfo));
        Map<String, Object> wxMiniCouponRequestMap = new HashMap<>();
        wxMiniCouponRequestMap.put("coupon_code", couponInfo.getCouponCode());
        wxMiniCouponRequestMap.put("stock_id", couponInfo.getStockId());
        wxMiniCouponRequestMap.put("return_request_no", String.valueOf(System.currentTimeMillis()));
        try {
            String result = wxHttpClientTemplate.doPost(RETURN_COUPON, JSON.toJSONString(wxMiniCouponRequestMap));
            WxReturnCouponResponse response = JSON.parseObject(result, new TypeReference<WxReturnCouponResponse>() {
            });
            log.info("调微信小程序申请退券. response:{}", result);
        } catch (Exception e) {
            log.error("调微信小程序申请退券失败", e);
        }
    }

    @Override
    public void deactivateCoupon(String stockId, String couponCode) {
        log.info("调微信小程序使券失效 . 请求参数 --> stockId:{}, couponCode:{}", stockId, couponCode);
        Map<String, Object> wxMiniCouponRequestMap = new HashMap<>();
        wxMiniCouponRequestMap.put("coupon_code", couponCode);
        wxMiniCouponRequestMap.put("stock_id", stockId);
        wxMiniCouponRequestMap.put("deactivate_request_no", genNoUtil.genCouponDeactivateNo());
        try {
            String result = wxHttpClientTemplate.doPost(DEACTIVATE_COUPON, JSON.toJSONString(wxMiniCouponRequestMap));
            WxReturnCouponResponse response = JSON.parseObject(result, new TypeReference<WxReturnCouponResponse>() {
            });
            log.info("调微信小程序使券失效 . 响应参数 --> response:{}", result);
        } catch (Exception e) {
            log.error("调微信小程序使券失效 . 出错", e);
        }
    }

    @Override
    public JSONObject queryCoupon(String stockId) {
        log.info("查询微信券详情 . 请求参数 --> stockId:{}", stockId);
        try {
            String url = MessageFormat.format(QUERY_COUPON, stockId);
            String result = wxHttpClientTemplate.doGet(url, Maps.newHashMap());
            log.info("查询微信券详情 . 响应参数 --> response:{}", result);
            JSONObject jsonObject = JSON.parseObject(result);
            return jsonObject;
        } catch (Exception e) {
            log.error("查询微信券详情 . 出错", e);
        }
        return null;
    }

    @Override
    public void updateCouponRuleStockId(Long ruleId, String stockId) {
        CouponRule couponRule = new CouponRule();
        couponRule.setId(ruleId);
        couponRule.setStockId(stockId);
        couponRuleDao.updateByIdSelective(couponRule);
    }

    @Override
    public WxBudgetCouponResponse miniBudgetCouponRule(String stokId, Integer maxCoupons, Integer localMaxCoupons) {
        if (StringUtils.isBlank(stokId)) {
            throw new BusinessException("批次号不能为空！");
        }
        if (Objects.isNull(maxCoupons)) {
            throw new BusinessException("目标批次号的最大发放数量不能为空！");
        }

        if (Objects.isNull(localMaxCoupons)) {
            throw new BusinessException("当前批次号的最大发放数量不能为空！");
        }
        Map<String, Object> wxMiniCouponRequestMap = new HashMap<>();
        wxMiniCouponRequestMap.put("current_max_coupons", localMaxCoupons);
        wxMiniCouponRequestMap.put("target_max_coupons", maxCoupons);
        wxMiniCouponRequestMap.put("modify_budget_request_no", String.valueOf(System.currentTimeMillis()));
        WxBudgetCouponResponse response = null;
        try {
            String url = MessageFormat.format(BUDGET_COUPON, stokId);
            String result = wxHttpClientTemplate.doPath(url, JSON.toJSONString(wxMiniCouponRequestMap));
            response = JSON.parseObject(result, new TypeReference<WxBudgetCouponResponse>() {
            });
        } catch (Exception e) {
            log.error("编辑优惠券发放量失败，", e);
        }
        return response;
    }

    @Override
    public void miniUpdateCouponRule(String stokId, String goodsName) {
        if (StringUtils.isBlank(stokId)) {
            throw new BusinessException("批次号不能为空！");
        }
        if(org.apache.commons.lang3.StringUtils.isBlank(goodsName)){
            throw new BusinessException("适用商品范围不能为空！");
        }
        Map<String, Object> wxMiniCouponRequestMap = new HashMap<>();
        if(org.apache.commons.lang3.StringUtils.isNotBlank(goodsName)){
            wxMiniCouponRequestMap.put("goods_name", goodsName);
        }
        wxMiniCouponRequestMap.put("out_request_no", String.valueOf(System.currentTimeMillis()));
        try {
            String url = MessageFormat.format(UPDATE_COUPON, stokId);
            wxHttpClientTemplate.doPath(url, JSON.toJSONString(wxMiniCouponRequestMap));
        } catch (Exception e) {
            log.error("编辑优惠券失败，", e);
            throw new BusinessException("编辑优惠券失败");
        }
    }


    @Override
    public void deleteCouponRule(Long id, String operation) {
        CouponRule rule = couponRuleDao.findById(id);
        Assert.notNull(rule, "优惠券不存在");
        if (!CouponStatusTypeEnum.OFFLINE.getCode().equals(rule.getStatus())) {
            throw new BusinessException("只有已下线才可以删除！");
        }
        if(Objects.equals(BasicFlagEnum.YES.getKey(), rule.getHide())){
            throw new BusinessException("该优惠券已删除！");
        }
        CouponRule update4Po = new CouponRule();
        update4Po.setId(id);
        update4Po.setHide(BasicFlagEnum.YES.getKey());
        update4Po.setUpdateTime(new Date());
        update4Po.setOperation(operation);
        couponRuleDao.updateByIdSelective(update4Po);
    }

    @Override
    public void timingCouponRule(CouponRuleSimpleDTO dto, String operation) {
        CouponRule rule = couponRuleDao.findById(dto.getId());

        if (Objects.isNull(rule)) {
            throw new BusinessException("优惠券规则不存在，请重新选择！");
        }

        if (Objects.isNull(dto.getOfflineTime())) {
            throw new BusinessException("下线时间不能为空!");
        }

        if (!CouponStatusTypeEnum.NOT_ONLINE.getCode().equals(rule.getStatus()) && !CouponStatusTypeEnum.ONLINE.getCode().equals(rule.getStatus())) {
            throw new BusinessException("状态有误，不可设置定时上下线！");
        }

        if (dto.getOfflineTime().after(rule.getEndTime()) || (Objects.nonNull(dto.getOnlineTime()) && dto.getOnlineTime().after(rule.getEndTime()))) {
            throw new BusinessException("定时上下线时间不能晚于优惠券有效时间的结束时间！");
        }

        if (dto.getOfflineTime().before(new Date())) {
            throw new BusinessException("下线时间不能早于当前时间!");
        }

        if (Objects.nonNull(dto.getOfflineTime()) && Objects.nonNull(dto.getOnlineTime()) && dto.getOfflineTime().compareTo(dto.getOnlineTime()) < 0) {
            throw new BusinessException("下线时间必须晚于上线时间!");
        }
        if(Objects.nonNull(rule.getEffectiveTime()) && dto.getOnlineTime().before(rule.getStartTime())){
            throw new BusinessException("N天后生效优惠券，不可提前上线！");
        }


        CouponRule couponRule = new CouponRule();
        couponRule.setId(rule.getId());
        couponRule.setOperation(operation);

        if (CouponStatusTypeEnum.NOT_ONLINE.getCode().equals(rule.getStatus())) {
            couponRule.setOnlineTime(dto.getOnlineTime());
        }

        couponRule.setOfflineTime(dto.getOfflineTime());
        couponRule.setUpdateTime(new Date());
        couponRuleDao.updateByIdSelective(couponRule);
    }

    @Override
    public CouponRule onlineCouponRule(Long id, String operation) {

        CouponRule rule = couponRuleDao.findById(id);
        if (Objects.isNull(rule)) {
            throw new BusinessException("优惠券规则不存在，请重新选择！");
        }
        if (!CouponStatusTypeEnum.NOT_ONLINE.getCode().equals(rule.getStatus())) {
            throw new BusinessException("当前状态有误");
        }
        if(CouponPublicTypeEnum.PUBLIC.getCode().equals(rule.getIsPublic())){
            if(CouponChannelTypeEnum.WECHAT_MINI.getCode().equals(rule.getChannel())){
                CouponRuleListDTO dto = new CouponRuleListDTO();
                dto.setIsPublic(CouponPublicTypeEnum.PUBLIC.getCode());
                dto.setStatus(CouponStatusTypeEnum.ONLINE.getCode());
                dto.setChannel(CouponChannelTypeEnum.WECHAT_MINI.getCode());
                List<CouponRule> ruleList = couponRuleDao.search(dto);

                List<CouponRule> firstJoin = ruleList.stream().filter(couponRule -> CouponReceiveCrowdTypeEnum.FIRST_JOIN.getCode().equals(couponRule.getReceiveCrowd())).collect(Collectors.toList());
                if(CouponReceiveCrowdTypeEnum.FIRST_JOIN.getCode().equals(rule.getReceiveCrowd()) &&
                        CollectionUtils.isNotEmpty(firstJoin) && firstJoin.size() >= 6){
                    throw new BusinessException("[首次入会用户]类型[公开]优惠券，同时在线不可超过6个！");
                }

                if(CollectionUtils.isNotEmpty(ruleList) && ruleList.size() >= 10){
                    throw new BusinessException("[公开]且[认养小程序]渠道的优惠券，同时在线不可超过10个！");
                }
            }else if(CouponChannelTypeEnum.CMCC.getCode().equals(rule.getChannel())){
                CouponRuleListDTO dto = new CouponRuleListDTO();
                dto.setIsPublic(CouponPublicTypeEnum.PUBLIC.getCode());
                dto.setStatus(CouponStatusTypeEnum.ONLINE.getCode());
                dto.setChannel(CouponChannelTypeEnum.CMCC.getCode());
                List<CouponRule> ruleList = couponRuleDao.search(dto);

                if(CollectionUtils.isNotEmpty(ruleList) && ruleList.size() >= 9){
                    throw new BusinessException("[公开]且[移动积分兑换]渠道的优惠券，同时在线不可超过9个！");
                }
            }
        }

        Date now = new Date();
        if (now.after(rule.getEndTime())) {
            throw new BusinessException("已过优惠券有效期，不可上线！");
        }
        if(Objects.nonNull(rule.getEffectiveTime()) && now.before(rule.getStartTime())){
            throw new BusinessException("N天后生效优惠券，不可提前上线！");
        }

        CouponRule couponRule = new CouponRule();
        couponRule.setStatus(CouponStatusTypeEnum.ONLINE.getCode());
        couponRule.setOnlineTime(new Date());
        couponRule.setId(rule.getId());
        couponRule.setOperation(operation);
        couponRuleDao.updateByIdSelective(couponRule);
        return rule;
    }

    @Override
    public void offlineCouponRule(Long id, String operation) {
        CouponRule rule = couponRuleDao.findById(id);
        if (Objects.isNull(rule)) {
            throw new BusinessException("优惠券规则不存在，请重新选择！");
        }
        if (!CouponStatusTypeEnum.ONLINE.getCode().equals(rule.getStatus())) {
            throw new BusinessException("当前状态有误");
        }

        CouponRule couponRule = new CouponRule();
        couponRule.setStatus(CouponStatusTypeEnum.OFFLINE.getCode());
        couponRule.setOfflineTime(new Date());
        couponRule.setId(rule.getId());
        couponRule.setOperation(operation);
        couponRuleDao.updateByIdSelective(couponRule);

    }

    @Override
    public Long countRuleNumByStatus(Long ruleId, List<Integer> status) {
        return couponRuleDao.countRuleNumByStatus(ruleId, status);
    }

    @Override
    public void couponPushRedis(CouponRule rule) {
        Date now = new Date();
        long timeout = rule.getEndTime().getTime() - now.getTime();
        String key = "Coupon:onlineNum:setCouponRule_" + rule.getId();
        redisOperation.setex(key, rule.getIssuedQuantity(), timeout, TimeUnit.MILLISECONDS);
    }

    @Override
    public Integer couponUserRemainingNum(CouponRule couponRule, CustomerUserVO customerUserVO){
        Boolean fristCouponFlag = this.checkFirstCoupon(customerUserVO);
        if(fristCouponFlag && CouponReceiveCrowdTypeEnum.FIRST_JOIN.getCode().equals(couponRule.getReceiveCrowd())){
            return -1;
        }
        //获取会员等级
        this.getUserGrade(customerUserVO);
        if(StringUtils.isBlank(couponRule.getStockId())){
            return -1;
        }
        if (checkCouponEffectiveTime(couponRule)){
            return -1;
        }
        if(!peopleLimitVerify(couponRule, customerUserVO)){
            return -1;
        }
        //剩余可领张数校验
        int remainingNum = remaining(couponRule, customerUserVO.getId());
        if (remainingNum <= 0) {
            return 0;
        }
        if(!Objects.equals(couponRule.getStatus(), CouponStatusTypeEnum.ONLINE.getCode())){
            return 0;
        }
        if(couponRule.getReceiveCount() >= couponRule.getIssuedQuantity()){
            return -1;
        }
        return remainingNum;
    }

    @Override
    public List<CouponCenterListVO> searchCoupon(CustomerUserVO customerUserVO, CouponRuleListDTO couponRuleListDTO, Integer limit) {
        List<CouponRule> couponRuleList = couponRuleDao.findByCondition(couponRuleListDTO);
        //判断是否领过首次入会类型的券
        Boolean fristCouponFlag = this.checkFirstCoupon(customerUserVO);

        //判断已领取数量是否超总数
        List<CouponRule> enableReceivedCouponList = new ArrayList<>();
        for (CouponRule rule : couponRuleList) {
            //优惠券已领张数
            Integer receivedNum = rule.getReceiveCount();
            if(fristCouponFlag && CouponReceiveCrowdTypeEnum.FIRST_JOIN.getCode().equals(rule.getReceiveCrowd())){
                continue;
            }
            if (receivedNum < rule.getIssuedQuantity()) {
                enableReceivedCouponList.add(rule);
            }
        }
        List<CouponCenterListVO> couponCenterList = new ArrayList<>();

        //获取会员等级
        this.getUserGrade(customerUserVO);

        //还可领的优惠券
        for (CouponRule rule : enableReceivedCouponList) {
            if (Objects.isNull(couponRuleListDTO.getId()) && StringUtils.isBlank(rule.getStockId())) {
                continue;
            }
            //过期时间校验
            if (checkCouponEffectiveTime(rule)){
                continue;
            }
            //剩余可领张数校验
            CouponCenterListVO couponCenter = new CouponCenterListVO();
            int remainingNum = remaining(rule, customerUserVO.getId());//剩余可领张数
            if (remainingNum <= 0) {
                continue;
            }
            //根据人群限制校验和封装优惠券列表
            buildCouponListByCrowdType(rule, couponCenter, remainingNum, couponCenterList, customerUserVO);
        }
        //返回条数限制
        return CollectionUtil.getPage(couponCenterList, limit);
    }

    private void buildCouponListByCrowdType(CouponRule rule, CouponCenterListVO couponCenter, int remainingNum, List<CouponCenterListVO> couponCenterList, CustomerUserVO customerUserVO) {
        if(peopleLimitVerify(rule, customerUserVO)){
            setRemainingNumAndCopy(rule, couponCenter, remainingNum, couponCenterList);
        }
    }

    private boolean checkBirthdayUser(CustomerUserVO user, CouponRule rule) {
        try {
            if(!Objects.equals(rule.getBirthdayUser(), BirthdayUserEnum.CURRENT_MONTH.getCode())){
                return false;
            }
            CustomerUser customerUser = customerUserManager.findById(user.getId());
            if(customerUser == null || Objects.isNull(customerUser.getBirthDay())){
                return false;
            }
            String birthday = DateUtil.dateToString(customerUser.getBirthDay(), DateUtil.SIMPLE_YMD);
            if(DateUtil.isDateInCurrentMonth(birthday)){
                return true;
            }
            return false;
        } catch (Exception e) {
            log.error("查询南讯用户 . 出错", e);
            return false;
        }
    }

    @Override
    public CouponMemberDTO searchMemberOnline(CustomerUserVO customerUserVO, CouponRuleListDTO couponRuleListDTO, CouponMemberVO memberVO) {
        List<CouponCenterListVO> couponCenterList = new ArrayList<>();
        //查询上线中的首次入会券
        List<CouponRule> enableReceivedCouponList = couponRuleDao.searchOnline(couponRuleListDTO);

        CouponMemberDTO couponMemberDTO = new CouponMemberDTO();
        couponMemberDTO.setFirstJoinCouponList(enableReceivedCouponList);
        if(Objects.isNull(customerUserVO)){
            couponMemberDTO.setCenterListVOList(BeanUtils.deepListCopy(enableReceivedCouponList,CouponCenterListVO.class));
            return couponMemberDTO;
        }

        //还可领的优惠券
        for (CouponRule rule : enableReceivedCouponList) {

            if (StringUtils.isBlank(rule.getStockId())) {
                continue;
            }
            //校验优惠券有效天数
            if (checkCouponEffectiveTime(rule)){
                continue;
            }

            CouponReceiveCrowdTypeEnum crowdTypeEnum = CouponReceiveCrowdTypeEnum.getEnumByCode(rule.getReceiveCrowd());
            if (null == crowdTypeEnum) {
                continue;
            }
            //用户剩余可领张数
            int remainingNum = remaining(rule, customerUserVO.getId());

            CouponCenterListVO couponCenter = new CouponCenterListVO();
            switch (crowdTypeEnum) {
                case FIRST_JOIN:
                    //判断是否首次入会的用户
                    if (checkFistJoinUser(customerUserVO, rule.getFirstJoinDay())) {
                        PageParams pageParams = new PageParams();
                        pageParams.setPageSize(2);
                        CouponOnsaleProductDTO couponOnsaleProductDTO = this.couponOnsaleProductNum(rule, pageParams);
                        couponCenter.setCouponOnsaleProductNum(couponOnsaleProductDTO.getCouponOnsaleProductNum());
                        couponCenter.setCouponOnsaleProductIds(couponOnsaleProductDTO.getCouponOnsaleProductIds());

                        setRemainingNumAndCopy(rule, couponCenter, remainingNum, couponCenterList);
                    }
                    break;
                default:
                    continue;
            }
        }
        //老用户--不可领，但也展示首次入会的券
        if(CollectionUtils.isEmpty(couponCenterList)
                && CollectionUtils.isNotEmpty(enableReceivedCouponList)){
            memberVO.setOldMemberFlag(true);
            couponMemberDTO.setCenterListVOList(BeanUtils.deepListCopy(enableReceivedCouponList,CouponCenterListVO.class));
            return couponMemberDTO;
        }

        couponMemberDTO.setCenterListVOList(couponCenterList);
        return couponMemberDTO;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void receive(CustomerUserVO customerUserVO, CouponReceiveResponDTO receiveResponDTO) {
        List<CouponRule> couponRuleList = couponRuleDao.findByStockId(receiveResponDTO.getStockId());
        CouponRule couponRule = new CouponRule();
        if (CollectionUtils.isNotEmpty(couponRuleList)) {
            couponRule = couponRuleList.get(0);
        }

        if (!receiveResponDTO.getStockId().equals(couponRule.getStockId())) {
            throw new BusinessException("优惠券批次有误！");
        }

        Long num = redisOperation.incr("Coupon:receive:ruleId:" + couponRule.getId() + "_userId:" + customerUserVO.getId(), 1);
        redisOperation.expire("Coupon:receive:ruleId:" + couponRule.getId() + "_userId:" + customerUserVO.getId(), 2000L);
        if (num >= 2) {
            throw new BusinessException("活动太火爆，请稍后再试");
        }
        //正在编辑发行量
        if (redisOperation.exist("Coupon:update:setCouponRule_" + couponRule.getId())) {
            throw new BusinessException("网络异常，请稍后再试");
        }

        // 幸运大抽奖/移动积分兑换优惠券：已有couponInfo记录，填充couponCode，openId，receiveTime即可
        if(Objects.equals(CouponChannelTypeEnum.LUCKY_DRAW.getCode(), couponRule.getChannel())
            || Objects.equals(CouponChannelTypeEnum.CMCC.getCode(), couponRule.getChannel())
            || Objects.equals(CouponChannelTypeEnum.FULL_REDUCE.getCode(), couponRule.getChannel())
            || Objects.equals(CouponChannelTypeEnum.POINT_MALL.getCode(), couponRule.getChannel())
            || Objects.equals(CouponChannelTypeEnum.FREE_TRIAL.getCode(), couponRule.getChannel())){
            List<CouponInfo> couponInfos = couponInfoDao.findCouponCodeIsNull(customerUserVO.getId(), couponRule.getId());
            CouponInfo couponInfo = null;
            if(CollectionUtils.isNotEmpty(couponInfos)){
                couponInfo = couponInfos.get(0);
                couponInfo.setCouponCode(receiveResponDTO.getCouponCode());
                couponInfo.setOpenId(receiveResponDTO.getOpenId());
                if(Objects.isNull(receiveResponDTO.getSendTime())){
                    couponInfo.setReceiveTime(new Date());
                }else{
                    couponInfo.setReceiveTime(receiveResponDTO.getSendTime());
                }
                couponInfo.setStatus(CouponInfoStatusTypeEnum.RECEIVE.getCode());
                couponInfo.setUpdateTime(new Date());
                couponInfoDao.update(couponInfo);
            }
            redisOperation.del("Coupon:receive:ruleId:" + couponRule.getId() + "_userId:" + customerUserVO.getId());
            log.info("领取成功回调 .  成功领取 --> couponCode: {}, ", receiveResponDTO.getCouponCode());
            return;
        }

        if(Objects.equals(CouponChannelTypeEnum.WECHAT_MINI.getCode(), couponRule.getChannel())){
            List<CouponInfo> couponInfos = couponInfoDao.findCouponCodeIsNull(customerUserVO.getId(), couponRule.getId());
            CouponInfo couponInfo = null;
            if(CollectionUtils.isNotEmpty(couponInfos)){
                couponInfo = couponInfos.get(0);
                couponInfo.setCouponCode(receiveResponDTO.getCouponCode());
                couponInfo.setOpenId(receiveResponDTO.getOpenId());
                if(Objects.isNull(receiveResponDTO.getSendTime())){
                    couponInfo.setReceiveTime(new Date());
                }else{
                    couponInfo.setReceiveTime(receiveResponDTO.getSendTime());
                }
                couponInfo.setStatus(CouponInfoStatusTypeEnum.RECEIVE.getCode());
                couponInfo.setUpdateTime(new Date());
                couponInfoDao.update(couponInfo);
                redisOperation.del("Coupon:receive:ruleId:" + couponRule.getId() + "_userId:" + customerUserVO.getId());
                log.info("领取成功回调 .  成功领取 --> couponCode: {}, ", receiveResponDTO.getCouponCode());
                return;
            }
        }

        //获取会员等级
        getUserGrade(customerUserVO);


        if (!checkUserReceiveCoupon(couponRule, customerUserVO)) {
            throw new BusinessException(ResponseCode.FAILURE, "无法领取!");
        }
        CouponInfo couponInfo = CouponUtil.buildCouponInfo(CouponInfoStatusTypeEnum.RECEIVE, customerUserVO, receiveResponDTO, couponRule);
        if (couponInfoDao.insertNoAspect(couponInfo) == 0) {
            throw new BusinessException("领取失败!");
        }
        //更新优惠券规则已领数量
        int row = couponRuleDao.updateReceiveCount(couponRule.getId(), 1);
        if(row == 0){
            throw new BusinessException("超过领取最大数量");
        }
        redisOperation.del("Coupon:receive:ruleId:" + couponRule.getId() + "_userId:" + customerUserVO.getId());
        log.info("领取成功回调 .  成功领取 --> couponCode: {}, ", receiveResponDTO.getCouponCode());

    }

    @Override
    public boolean checkCouponAllRange(Long ruleId, Integer rangeType, ProductBaseDTO productBaseDTO) {
        Long cardCategoryId = productBaseDTO.getCardCategoryId();
        Long cateId = productBaseDTO.getCateId();
        Long productId = productBaseDTO.getId();

        CouponRangeTypeEnum couponRangeTypeEnum = CouponRangeTypeEnum.getEnumByCode(rangeType);
        List<CouponRange> couponRangeList = new ArrayList<>();
        switch (couponRangeTypeEnum) {
            case APPOINT_CARD_CATEGORY:
                if(Objects.nonNull(ruleId) && Objects.nonNull(cardCategoryId)
                        && ProductTypeEnum.PACKAGE.getCode().equals(productBaseDTO.getProductType())){
                    couponRangeList = couponRangeDao.queryByRuleIdAndProduct(ruleId, cardCategoryId, null);
                    if (CollectionUtils.isNotEmpty(couponRangeList)) {
                        return true;
                    }
                }
                break;
            case APPOINT_CARD:
                if(Objects.nonNull(ruleId) && Objects.nonNull(productId)
                        &&  ProductTypeEnum.PACKAGE.getCode().equals(productBaseDTO.getProductType())){
                    couponRangeList = couponRangeDao.queryByRuleIdAndProduct(ruleId, null, productId);
                    if (CollectionUtils.isNotEmpty(couponRangeList)) {
                        return true;
                    }
                }
                break;
            case EXCLUDE_CARD_CATEGORY:
                if(Objects.nonNull(ruleId) && Objects.nonNull(cardCategoryId)
                        &&  ProductTypeEnum.PACKAGE.getCode().equals(productBaseDTO.getProductType())){
                    couponRangeList = couponRangeDao.queryByRuleIdAndProduct(ruleId, cardCategoryId, null);
                    if (CollectionUtils.isEmpty(couponRangeList)) {
                        return true;
                    }
                }

                break;
            case EXCLUDE_CARD:
                if(Objects.nonNull(ruleId) && Objects.nonNull(productId)
                        &&  ProductTypeEnum.PACKAGE.getCode().equals(productBaseDTO.getProductType())){
                    couponRangeList = couponRangeDao.queryByRuleIdAndProduct(ruleId, null, productId);
                    if (CollectionUtils.isEmpty(couponRangeList)) {
                        return true;
                    }
                }
                break;
            case APPOINT_CATEGORY:
                if(Objects.nonNull(ruleId) && Objects.nonNull(cateId)
                && ProductTypeEnum.SINGLE_PRODUCT.getCode().equals(productBaseDTO.getProductType())){
                    couponRangeList = couponRangeDao.queryByRuleIdAndProduct(ruleId, cateId, null);
                    if (CollectionUtils.isNotEmpty(couponRangeList)) {
                        return true;
                    }
                }
                break;
            case APPOINT_PRODUCT:
                if(Objects.nonNull(ruleId) && Objects.nonNull(productId)
                        && ProductTypeEnum.SINGLE_PRODUCT.getCode().equals(productBaseDTO.getProductType())){
                    couponRangeList = couponRangeDao.queryByRuleIdAndProduct(ruleId, null, productId);
                    if (CollectionUtils.isNotEmpty(couponRangeList)) {
                        return true;
                    }
                }
                break;
            case EXCLUDE_CATEGORY:
                if(Objects.nonNull(ruleId) && Objects.nonNull(cateId)
                        && ProductTypeEnum.SINGLE_PRODUCT.getCode().equals(productBaseDTO.getProductType())){
                    couponRangeList = couponRangeDao.queryByRuleIdAndProduct(ruleId, cateId, null);
                    if (CollectionUtils.isEmpty(couponRangeList)) {
                        return true;
                    }
                }
                break;
            case EXCLUDE_PRODUCT:
                if(Objects.nonNull(ruleId) && Objects.nonNull(productId)
                        && ProductTypeEnum.SINGLE_PRODUCT.getCode().equals(productBaseDTO.getProductType())){
                    couponRangeList = couponRangeDao.queryByRuleIdAndProduct(ruleId, null, productId);
                    if (CollectionUtils.isEmpty(couponRangeList)) {
                        return true;
                    }
                }
                break;
            case APPOINT_ENTITY_CARD_CATEGORY:
                if(Objects.nonNull(ruleId) && Objects.nonNull(cateId)
                        && ProductTypeEnum.ENTITY_CARD.getCode().equals(productBaseDTO.getProductType())){
                    couponRangeList = couponRangeDao.queryByRuleIdAndProduct(ruleId, cateId, null);
                    if (CollectionUtils.isNotEmpty(couponRangeList)) {
                        return true;
                    }
                }
                break;
            case APPOINT_ENTITY_CARD:
                if(Objects.nonNull(ruleId) && Objects.nonNull(productId)
                        && ProductTypeEnum.ENTITY_CARD.getCode().equals(productBaseDTO.getProductType())){
                    couponRangeList = couponRangeDao.queryByRuleIdAndProduct(ruleId, null, productId);
                    if (CollectionUtils.isNotEmpty(couponRangeList)) {
                        return true;
                    }
                }
                break;
            case EXCLUDE_ENTITY_CARD_CATEGORY:
                if(Objects.nonNull(ruleId) && Objects.nonNull(cateId)
                        && ProductTypeEnum.ENTITY_CARD.getCode().equals(productBaseDTO.getProductType())){
                    couponRangeList = couponRangeDao.queryByRuleIdAndProduct(ruleId, cateId, null);
                    if (CollectionUtils.isEmpty(couponRangeList)) {
                        return true;
                    }
                }
                break;
            case EXCLUDE_ENTITY_CARD:
                if(Objects.nonNull(ruleId) && Objects.nonNull(productId)
                        && ProductTypeEnum.ENTITY_CARD.getCode().equals(productBaseDTO.getProductType())){
                    couponRangeList = couponRangeDao.queryByRuleIdAndProduct(ruleId, null, productId);
                    if (CollectionUtils.isEmpty(couponRangeList)) {
                        return true;
                    }
                }
                break;
            case ALL_ECARD:
                if(ProductTypeEnum.PACKAGE.getCode().equals(productBaseDTO.getProductType())){
                    return true;
                }
                break;
            case ALL_ENTITY_CARD:
                if(ProductTypeEnum.ENTITY_CARD.getCode().equals(productBaseDTO.getProductType())){
                    return true;
                }
                break;
            case ALL_SINGLE_PRODUCT:
                if(ProductTypeEnum.SINGLE_PRODUCT.getCode().equals(productBaseDTO.getProductType())){
                    return true;
                }
                break;
            case ALL:
                if(Objects.nonNull(ruleId) && Objects.nonNull(productId)) {
                    couponRangeList = couponRangeDao.queryByRuleIdAndProduct(ruleId, null, productId);
                    if (CollectionUtils.isNotEmpty(couponRangeList)) {
                        return false;
                    }
                }
                return true;
            case INCLUDE_ALL:
                if(Objects.nonNull(ruleId) && Objects.nonNull(productId)) {
                    couponRangeList = couponRangeDao.queryByRuleIdAndProduct(ruleId, null, productId);
                    if (CollectionUtils.isNotEmpty(couponRangeList)) {
                        return true;
                    }
                }
                break;
            case EXCLUDE_ALL:
                if(Objects.nonNull(ruleId) && Objects.nonNull(productId)){
                    couponRangeList = couponRangeDao.queryByRuleIdAndProduct(ruleId, null, productId);
                    if (CollectionUtils.isEmpty(couponRangeList)) {
                        return true;
                    }
                }
                break;
            default:
                return false;
        }
        return false;
    }

    @Override
    public List<CouponInfo> queryByUserIdAndStatus(Long userId, List<Integer> status) {
        return couponInfoDao.queryByUserIdAndStatus(userId, status);
    }

    @Override
    public List<CouponInfo> queryMyEnableCoupon(Long userId) {
        return couponInfoDao.queryEnableCoupon(userId);
    }

    @Override
    public int queryMyEnableCouponCount(Long userId) {
        return couponInfoDao.queryEnableCouponCount(userId);
    }

    @Override
    public List<CouponInfo> queryMyEnableProductCoupon(Long userId) {
        return couponInfoDao.queryEnableProductCoupon(userId);
    }

    @Override
    public int queryMyEnableCouponCountForB(Long id) {
        return couponInfoDao.queryEnableCouponCountForB(id);
    }
    @Override
    public List<CouponInfo> queryMyEnableGiftCoupon(Long userId) {
        return couponInfoDao.queryEnableGiftCoupon(userId);
    }

    @Override
    public List<CouponInfo> queryMyEnableCouponExclude(Long userId) {
        return couponInfoDao.queryEnableCouponExclude(userId);
    }

    @Override
    public List<CouponInfo> queryMyEnableCouponExclude(Long userId, Integer type) {
        return couponInfoDao.queryEnableCouponExclude(userId, type);
    }

    @Override
    public int updateExpire(CouponInfo info) {
        List<CouponInfo> couponInfos = couponInfoDao.queryExpireCoupon(new Date());
        if(CollectionUtils.isEmpty(couponInfos)){
            return 0;
        }
        couponInfoDao.updateByIds(StreamUtils.toList(couponInfos, CouponInfo::getId), info);
        return couponInfos.size();
    }

    @Override
    public List<CouponRule> queryCouponRuleList(List<Long> ruleIds) {
        return couponRuleDao.findByIdList(ruleIds);
    }

    @Override
    public void insertCouponError(CouponReceiveResponDTO couponReceiveResponDTO) {
        CouponCallbackLog couponCallbackLog = new CouponCallbackLog();
        couponCallbackLog.setCode(couponReceiveResponDTO.getCode());
        couponCallbackLog.setMessage(couponReceiveResponDTO.getMessage());
        couponCallbackLog.setStockId(couponReceiveResponDTO.getStockId());
        couponCallbackLogDao.insert(couponCallbackLog);
    }

    @Override
    public void insertMiniCallbackError(CouponReceiveResponDTO couponReceiveResponDTO) {
        CouponMiniCallbackLog couponCallbackLog = new CouponMiniCallbackLog();
        couponCallbackLog.setCouponCode(couponReceiveResponDTO.getCouponCode());
        couponCallbackLog.setMessage(couponReceiveResponDTO.getMessage());
        couponCallbackLog.setStockId(couponReceiveResponDTO.getStockId());
        couponCallbackLog.setOpenId(couponReceiveResponDTO.getOpenId());
        couponCallbackLog.setSendTime(couponReceiveResponDTO.getSendTime());
        couponMiniCallbackLogDao.insert(couponCallbackLog);
    }

    @Override
    public CouponInfo findUserCouponById(Long userId, Long couponId) {
        List<CouponInfo> couponInfoList = couponInfoDao.findUserbyId(userId, couponId);
        if (CollectionUtils.isNotEmpty(couponInfoList)) {
            return couponInfoList.get(0);
        }
        return null;
    }

    @Override
    public CouponInfo findUserCouponByRuleId(Long userId, Long ruleId) {
        List<CouponInfo> couponInfoList = couponInfoMapper.findUserCouponByRuleId(userId, ruleId);
        if (CollectionUtils.isNotEmpty(couponInfoList)) {
            return couponInfoList.get(0);
        }
        return null;
    }

    @Override
    public int updateCouponUsedById(Long userId, Long couponId, String orderNo) {
        CouponInfo couponInfo = new CouponInfo();
        couponInfo.setUseTime(new Date());
        couponInfo.setStatus(CouponInfoStatusTypeEnum.USED.getCode());
        couponInfo.setOrderNo(orderNo);
        return couponInfoDao.updateCouponById(couponInfo, userId, couponId, CouponInfoStatusTypeEnum.RECEIVE.getCode());
    }

    @Override
    public int updateCouponCancelByOrderNo(String orderNo, Integer type) {
        return couponInfoDao.updateCouponCancelByOrderNo(orderNo,type);
    }

    private boolean peopleLimitVerify(CouponRule rule, CustomerUserVO user) {
        CouponReceiveCrowdTypeEnum crowdTypeEnum = CouponReceiveCrowdTypeEnum.getEnumByCode(rule.getReceiveCrowd());
        if (null == crowdTypeEnum) {
            log.error("人群限制类型有误 CouponRule:{}, CustomerUserVO:{}", JSON.toJSONString(rule), JSON.toJSONString(user));
            return false;
        }
        switch (crowdTypeEnum) {
            case NO_LIMIT:
                return true;
            case NEW_USER:
                if (checkNewUserV2(user)) {
                    return true;
                }
                break;
            case RENEWAL_USER:
                List<Card> cardList = cardDao.findByUserWithInUse(user.getId());
                if (rule.getRenewalType().equals(0)) {
                    for (Card card : cardList) {
                        if (card.getRemainingCount() <= rule.getRemainingCount()) {
                            return true;
                        }
                    }
                } else if (rule.getRenewalType().equals(1)) {
                    Integer remainingCountAll = 0;
                    Calendar calendar = Calendar.getInstance();
                    calendar.add(Calendar.DATE, 0 - rule.getGraceDay());
                    List<Card> beforeGraceDayCards = cardList.stream().filter(card -> card.getReceiveTime().before(calendar.getTime())).collect(Collectors.toList());
                    //有效期内买的卡，有效期之前从未买过的客户
                    if (cardList.size() > 0 && CollectionUtils.isEmpty(beforeGraceDayCards)) {
                        for (Card card : cardList) {
                            remainingCountAll += card.getRemainingCount();
                        }
                    } else if (CollectionUtils.isNotEmpty(beforeGraceDayCards)) {
                        for (Card card : beforeGraceDayCards) {
                            remainingCountAll += card.getRemainingCount();
                        }
                    }
                    if (remainingCountAll <= rule.getRemainingCount()) {
                        return true;
                    }
                }
                break;
            case MEBER_LEVEL:
                // 会员
                if(StringUtils.isNotBlank(rule.getGrade()) && Objects.nonNull(user.getGrade())){
                    List<String> gradeList = Arrays.asList(rule.getGrade().split(","));
                    for (String grade:gradeList) {
                        if(grade.equals(String.valueOf(user.getGrade()))){
                            // 当月用户判断
                            if(Objects.equals(rule.getBirthdayUser(), BirthdayUserEnum.CURRENT_MONTH.getCode())){
                                if(checkBirthdayUser(user, rule)){
                                    return true;
                                }
                            }else{
                                return true;
                            }
                        }
                    }
                }
                break;
            case PRIVILEGED_USER:
                if (checkPrivilegeUser(rule.getId(), user.getId())) {
                    return true;
                }
                break;
            case FIRST_JOIN:
                if (checkFistJoinUser(user, rule.getFirstJoinDay())) {
                    return true;
                }
                break;
            case LABEL:
                if(checkLabel(user, rule.getLabelId())){
                    return true;
                }
                break;
            default:
                log.error("人群限制类型有误 CouponRule:{}, CustomerUserVO:{}", JSON.toJSONString(rule), JSON.toJSONString(user));
                return false;
        }
        return false;
    }

    private boolean checkUserReceiveCoupon(CouponRule rule, CustomerUserVO customerUserVO) {
        if(StringUtils.isBlank(rule.getStockId())){
            return false;
        }
        int remainingNum = remaining(rule, customerUserVO.getId());//剩余可领张数
        if (remainingNum <= 0) {
            throw new BusinessException(ResponseCode.BUSINESS_ERROR_IGNORE, "领取数量已达上限!");
        }

        return peopleLimitVerify(rule, customerUserVO);
    }

    private void setRemainingNumAndCopy(CouponRule rule, CouponCenterListVO couponCenter, int remainingNum, List<CouponCenterListVO> couponCenterList) {
        BeanUtils.copy(rule, couponCenter);
        couponCenter.setRemainingNum(remainingNum);
        couponCenterList.add(couponCenter);
    }

    @Override
    public int remaining(CouponRule rule, Long userId) {
        int receivedNum = couponInfoDao.countReceivedByUserIdAndRuleId(rule.getId(), userId);
        int remainingNum = rule.getReceiveNum() - receivedNum;
        if (remainingNum <= 0) {
            return 0;
        } else {
            return remainingNum;
        }
    }


    @Override
    public boolean isReceive(Long userId, CouponReceiveResponDTO receiveResponDTO) {
        log.info("领取成功回调 .  判断是否领取 --> userId:{}", userId);
        List<CouponRule> couponRuleList = couponRuleDao.findByStockId(receiveResponDTO.getStockId());
        CouponRule couponRule = new CouponRule();
        if (CollectionUtils.isNotEmpty(couponRuleList)) {
            couponRule = couponRuleList.get(0);
        }
        if (!receiveResponDTO.getStockId().equals(couponRule.getStockId())) {
            throw new BusinessException("优惠券批次有误！");
        }

        List<CouponInfo> couponInfoList = couponInfoDao.findCouponByCouponCode(userId, couponRule.getId(), receiveResponDTO.getCouponCode());
        log.info("领取成功回调 .  判断是否领取 --> couponInfoList:{}", JSON.toJSONString(couponInfoList));
        if (CollectionUtils.isEmpty(couponInfoList)) {
            return false;
        } else {
            CouponInfo couponInfo = couponInfoList.get(0);
            if(!Objects.equals(couponInfo.getUserId(), userId)){
                log.info("领取成功回调 .  领取userId不一致 --> couponUserId:{}， userId：{}", couponInfo.getUserId(), userId);
            }
            return true;
        }
    }

    @Override
    public CouponInfo receiveCallback(CustomerUserVO customerUserVO, CouponReceiveResponDTO receiveResponDTO, Date sendTime) {
        List<CouponRule> couponRuleList = couponRuleDao.findByStockId(receiveResponDTO.getStockId());
        CouponRule couponRule = new CouponRule();
        if (CollectionUtils.isNotEmpty(couponRuleList)) {
            couponRule = couponRuleList.get(0);
        }
        if (!receiveResponDTO.getStockId().equals(couponRule.getStockId())) {
            throw new BusinessException("优惠券批次有误！");
        }
        Long num = redisOperation.incr("Coupon:receive:ruleId:" + couponRule.getId() + "_userId:" + customerUserVO.getId(), 1);
        redisOperation.expire("Coupon:receive:ruleId:" + couponRule.getId() + "_userId:" + customerUserVO.getId(), 2000L);
        if (num >= 2) {
            throw new BusinessException("活动太火爆，请稍后再试");
        }
        //获取会员等级
        this.getUserGrade(customerUserVO);

        if (!checkUserReceiveCoupon(couponRule, customerUserVO)) {
            throw new BusinessException("无法领取!");
        }
        // 幸运大抽奖/移动积分兑换优惠券：已有couponInfo记录，填充couponCode，openId，receiveTime即可
        if(Objects.equals(CouponChannelTypeEnum.LUCKY_DRAW.getCode(), couponRule.getChannel())
                || Objects.equals(CouponChannelTypeEnum.CMCC.getCode(), couponRule.getChannel())
                || Objects.equals(CouponChannelTypeEnum.FULL_REDUCE.getCode(), couponRule.getChannel())
                || Objects.equals(CouponChannelTypeEnum.POINT_MALL.getCode(), couponRule.getChannel())
                || Objects.equals(CouponChannelTypeEnum.FREE_TRIAL.getCode(), couponRule.getChannel())){
            List<CouponInfo> couponInfos = couponInfoDao.findCouponCodeIsNull(customerUserVO.getId(), couponRule.getId());
            CouponInfo couponInfo = null;
            if(CollectionUtils.isNotEmpty(couponInfos)){
                couponInfo = couponInfos.get(0);
                couponInfo.setCouponCode(receiveResponDTO.getCouponCode());
                couponInfo.setOpenId(receiveResponDTO.getOpenId());
                couponInfo.setReceiveTime(sendTime);
                couponInfo.setStatus(CouponInfoStatusTypeEnum.RECEIVE.getCode());
                couponInfo.setUpdateTime(new Date());
                couponInfoDao.update(couponInfo);
            }
            redisOperation.del("Coupon:receive:ruleId:" + couponRule.getId() + "_userId:" + customerUserVO.getId());
            log.info("领取成功回调 .  成功领取 --> couponCode: {}, ", receiveResponDTO.getCouponCode());
            return couponInfo;
        }

        if(Objects.equals(CouponChannelTypeEnum.WECHAT_MINI.getCode(), couponRule.getChannel())){
            List<CouponInfo> couponInfos = couponInfoDao.findCouponCodeIsNull(customerUserVO.getId(), couponRule.getId());
            CouponInfo couponInfo = null;
            if(CollectionUtils.isNotEmpty(couponInfos)){
                couponInfo = couponInfos.get(0);
                couponInfo.setCouponCode(receiveResponDTO.getCouponCode());
                couponInfo.setOpenId(receiveResponDTO.getOpenId());
                couponInfo.setReceiveTime(sendTime);
                couponInfo.setStatus(CouponInfoStatusTypeEnum.RECEIVE.getCode());
                couponInfo.setUpdateTime(new Date());
                couponInfoDao.update(couponInfo);
                redisOperation.del("Coupon:receive:ruleId:" + couponRule.getId() + "_userId:" + customerUserVO.getId());
                log.info("领取成功回调 .  成功领取 --> couponCode: {}, ", receiveResponDTO.getCouponCode());
                return couponInfo;
            }
        }

        CouponInfo couponInfo = new CouponInfo();
        couponInfo.setType(CouponInfoTypeEnum.PRICE_COUPON.getCode());
        if(CouponTypeEnum.GIFT_COUPON.getCode().equals(couponRule.getCouponType())){
            couponInfo.setType(CouponInfoTypeEnum.GIFT_COUPON.getCode());
        }
        couponInfo.setRuleId(couponRule.getId());
        couponInfo.setStockId(couponRule.getStockId());
        couponInfo.setUserId(customerUserVO.getId());
        couponInfo.setReceiveTime(sendTime);
        couponInfo.setCreateTime(new Date());
        couponInfo.setStatus(CouponInfoStatusTypeEnum.RECEIVE.getCode());
        couponInfo.setCouponCode(receiveResponDTO.getCouponCode());
        couponInfo.setCouponRange(couponRule.getCouponRange());
        if (Objects.isNull(couponRule.getEffectiveTime())) {
            couponInfo.setStartTime(couponRule.getStartTime());
            couponInfo.setEndTime(couponRule.getEndTime());
        } else {
            if (sendTime.before(couponRule.getStartTime())) {
                sendTime = couponRule.getStartTime();
            }
            CouponUtil.computeTime(couponInfo, couponRule, sendTime);
            if (couponInfo.getStartTime().after(couponRule.getEndTime())) {
                couponInfo.setStatus(CouponInfoStatusTypeEnum.EXPIRE.getCode());
            } else if (couponInfo.getEndTime().after(couponRule.getEndTime())) {
                couponInfo.setEndTime(couponRule.getEndTime());
            }
        }

        if (couponInfoDao.insertNoAspect(couponInfo) == 0) {
            throw new BusinessException("领取失败!");
        }
        //更新优惠券规则已领数量
        int row = couponRuleDao.updateReceiveCount(couponRule.getId(), 1);
        if(row == 0){
            throw new BusinessException("超过领取最大数量");
        }
        redisOperation.del("Coupon:receive:ruleId:" + couponRule.getId() + "_userId:" + customerUserVO.getId());
        log.info("领取成功回调 .  成功领取 --> couponCode: {}, ", receiveResponDTO.getCouponCode());
        return couponInfo;
    }

    @Override
    public List<CouponCenterListVO> enableReceivedCoupon(List<CouponRule> enableReceivedCouponList, CustomerUserVO customerUserVO) {

        //判断是否领过首次入会类型的券
        Boolean fristCouponFlag = this.checkFirstCoupon(customerUserVO);

        List<CouponCenterListVO> couponCenterList = new ArrayList<>();
        List<Long> ruleIdList = StreamUtils.convertDistinct(enableReceivedCouponList, CouponRule::getId, Long::compareTo);
        if(CollectionUtils.isEmpty(ruleIdList)){
            return couponCenterList;
        }

        List<CouponReceiveNumDTO> couponUserReceiveNumList = couponInfoDao.selectCouponReceiveNum(ruleIdList, customerUserVO.getId());
        Map<Long, Long> couponUserReceiveNumMap = StreamUtils.toMap(couponUserReceiveNumList, CouponReceiveNumDTO::getRuleId, CouponReceiveNumDTO::getNum);

/*        List<CouponReceiveNumDTO> couponReceiveNumList = couponInfoDao.selectCouponReceiveNum(ruleIdList);
        Map<Long, Long> couponReceiveNumMap = StreamUtils.toMap(couponReceiveNumList, CouponReceiveNumDTO::getRuleId, CouponReceiveNumDTO::getNum);*/

        // 获取会员等级
        this.getUserGrade(customerUserVO);

        for (CouponRule rule : enableReceivedCouponList) {
            //用户剩余可领张数
            int userReceivedNum = Objects.isNull(couponUserReceiveNumMap.get(rule.getId())) ? 0 : couponUserReceiveNumMap.get(rule.getId()).intValue();
            int remainingNum = rule.getReceiveNum() - userReceivedNum <= 0 ? 0 : rule.getReceiveNum() - userReceivedNum;
            rule.setUserRemainingNum(remainingNum);
/*            //优惠券已领总量
            Integer sumReceivedNum = Objects.isNull(couponReceiveNumMap.get(rule.getId())) ? 0 : couponReceiveNumMap.get(rule.getId()).intValue();
            rule.setSumReceivedNum(sumReceivedNum);*/
            if (StringUtils.isBlank(rule.getStockId())) {
                continue;
            }
            //时间校验
            if (this.checkCouponEffectiveTime(rule)){
                continue;
            }
            // 首次入会校验
            if(CouponReceiveCrowdTypeEnum.FIRST_JOIN.getCode().equals(rule.getReceiveCrowd()) && fristCouponFlag){
                continue;
            }
            if (rule.getReceiveCount() >= rule.getIssuedQuantity()) {
                continue;
            }
            if(remainingNum <= 0){
                continue;
            }
            //根据人群限制校验和封装优惠券列表
            CouponCenterListVO couponCenter = new CouponCenterListVO();
            buildCouponListByCrowdType(rule, couponCenter, remainingNum, couponCenterList, customerUserVO);
        }
        return couponCenterList;
    }

    private boolean checkCouponEffectiveTime(CouponRule rule) {
        if (Objects.nonNull(rule.getEffectiveTime())) {
            CouponInfo info = new CouponInfo();
            CouponUtil.computeTime(info, rule, new Date());
            if (info.getStartTime().after(rule.getEndTime())) {
                return true;
            }
        }
        return false;
    }

    @Override
    public boolean checkCouponReceiveFirstJoin(CustomerUserVO customerUserVO) {
        if(Objects.isNull(customerUserVO)){
            return false;
        }
        List<CouponInfo> couponInfoList = couponInfoDao.findByUserId(customerUserVO.getId());
        if(CollectionUtils.isEmpty(couponInfoList)){
            return true;
        }
        List<Long> ruleIdList = couponInfoList.stream().map(CouponInfo::getRuleId).distinct().collect(Collectors.toList());
        List<CouponRule> ruleList = couponRuleDao.findByIdList(ruleIdList);
        List<CouponRule> firstJoinCoupon = ruleList.stream().filter(rule -> rule.getReceiveCrowd().equals(CouponReceiveCrowdTypeEnum.FIRST_JOIN.getCode())).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(firstJoinCoupon)){
            return false;
        }
        return true;
    }

    @Override
    public CouponOnsaleProductDTO couponOnsaleProductNum(CouponRule rule, PageParams pageParams) {
        List<Long> couponRangeProductIdList = this.couponRangeProductIds(rule);
        CouponOnsaleProductDTO couponOnsaleProductDTO = new CouponOnsaleProductDTO();
        couponOnsaleProductDTO.setCouponOnsaleProductNum(0);
        if(CollectionUtils.isEmpty(couponRangeProductIdList)){
            return couponOnsaleProductDTO;
        }
        ProductBaseSearchDTO dto = new ProductBaseSearchDTO();
        dto.setPageNum(pageParams.getPageNum());
        dto.setPageSize(pageParams.getPageSize());
        dto.setProductIds(couponRangeProductIdList);
        if(!CouponPublicTypeEnum.PRIVATE.getCode().equals(rule.getIsPublic())){
            dto.setEnableShow(ProductShowEnum.ENABLE.getCode());
        }
        dto.setProductTypeList(Arrays.asList(ProductTypeEnum.PACKAGE.getCode(),ProductTypeEnum.SINGLE_PRODUCT.getCode(),ProductTypeEnum.ENTITY_CARD.getCode()));
        dto.addSaleStatus(ProductSaleStatusEnum.ON_SHELVES.getCode());
        List<Long> ids = productManager.searchProductIds(dto);
        if(CollectionUtils.isNotEmpty(ids)){
            couponOnsaleProductDTO.setCouponOnsaleProductNum(ids.size());
            couponOnsaleProductDTO.setCouponOnsaleProductIds(ids);
            return couponOnsaleProductDTO;
        }
        return couponOnsaleProductDTO;
    }

    @Override
    public List<Long> couponRangeProductIds(CouponRule rule) {
        List<CouponRange> couponRangeList = couponRangeDao.queryByRuleId(rule.getId());
        List<Long> categoryIdList = StreamUtils.toList(couponRangeList, CouponRange::getCategoryId);
        List<Long> productIdList = StreamUtils.toList(couponRangeList, CouponRange::getProductId);
        List<CouponRangeVO> couponRangeVOList = new ArrayList<>();
        List<Long> couponRangeProductIdList = new ArrayList<>();
        if (CouponRangeTypeEnum.APPOINT_CARD_CATEGORY.getCode().equals(rule.getCouponRange())) {
            couponRangeVOList = couponRangeManager.queryCouponRangeCategoryToProduct(rule.getId(), false);
        }else if(CouponRangeTypeEnum.EXCLUDE_CARD_CATEGORY.getCode().equals(rule.getCouponRange())){
            couponRangeVOList = couponRangeManager.queryCouponRangeCategoryToProduct(rule.getId(), true);
        }else if(CouponRangeTypeEnum.EXCLUDE_CARD.getCode().equals(rule.getCouponRange())){
            couponRangeVOList = couponRangeManager.queryCouponRangeProductByProductId(productIdList, ProductTypeEnum.PACKAGE.getCode(), true);
        }else if(CouponRangeTypeEnum.EXCLUDE_PRODUCT.getCode().equals(rule.getCouponRange())){
            couponRangeVOList = couponRangeManager.queryCouponRangeProductByProductId(productIdList, ProductTypeEnum.SINGLE_PRODUCT.getCode(), true);
        }else if(CouponRangeTypeEnum.EXCLUDE_ENTITY_CARD.getCode().equals(rule.getCouponRange())){
            couponRangeVOList = couponRangeManager.queryCouponRangeProductByProductId(productIdList, ProductTypeEnum.ENTITY_CARD.getCode(), true);
        }else if (CouponRangeTypeEnum.APPOINT_CARD.getCode().equals(rule.getCouponRange())){
            couponRangeProductIdList = productIdList;
        }else if(CouponRangeTypeEnum.APPOINT_PRODUCT.getCode().equals(rule.getCouponRange())){
            couponRangeProductIdList = productIdList;
        }else if(CouponRangeTypeEnum.APPOINT_ENTITY_CARD.getCode().equals(rule.getCouponRange())){
            couponRangeProductIdList = productIdList;
        }else if(CouponRangeTypeEnum.EXCLUDE_CATEGORY.getCode().equals(rule.getCouponRange())){
            couponRangeVOList = couponRangeManager.queryCouponRangeProductByCateId(categoryIdList, ProductTypeEnum.SINGLE_PRODUCT.getCode(), true);
        }else if(CouponRangeTypeEnum.APPOINT_CATEGORY.getCode().equals(rule.getCouponRange())){
            couponRangeVOList = couponRangeManager.queryCouponRangeProductByCateId(categoryIdList, ProductTypeEnum.SINGLE_PRODUCT.getCode(), false);
        }else if(CouponRangeTypeEnum.APPOINT_ENTITY_CARD_CATEGORY.getCode().equals(rule.getCouponRange())){
            couponRangeVOList = couponRangeManager.queryCouponRangeProductByCateId(categoryIdList, ProductTypeEnum.ENTITY_CARD.getCode(), false);
        }else if(CouponRangeTypeEnum.EXCLUDE_ENTITY_CARD_CATEGORY.getCode().equals(rule.getCouponRange())){
            couponRangeVOList = couponRangeManager.queryCouponRangeProductByCateId(categoryIdList, ProductTypeEnum.ENTITY_CARD.getCode(), true);
        }else if(CouponRangeTypeEnum.ALL_ECARD.getCode().equals(rule.getCouponRange())){
            couponRangeVOList = couponRangeManager.queryCouponRangeProductByProductId(null, ProductTypeEnum.PACKAGE.getCode(), true);
        }else if(CouponRangeTypeEnum.ALL_ENTITY_CARD.getCode().equals(rule.getCouponRange())){
            couponRangeVOList = couponRangeManager.queryCouponRangeProductByProductId(null, ProductTypeEnum.ENTITY_CARD.getCode(), true);
        }else if(CouponRangeTypeEnum.ALL_SINGLE_PRODUCT.getCode().equals(rule.getCouponRange())){
            couponRangeVOList = couponRangeManager.queryCouponRangeProductByProductId(null, ProductTypeEnum.SINGLE_PRODUCT.getCode(), true);
        }else if(CouponRangeTypeEnum.ALL.getCode().equals(rule.getCouponRange())){
            couponRangeVOList = couponRangeManager.queryCouponRangeProductByProductId(productIdList, null, true);
        }else if(CouponRangeTypeEnum.INCLUDE_ALL.getCode().equals(rule.getCouponRange())){
            couponRangeVOList = couponRangeManager.queryCouponRangeProductByProductId(productIdList, null, false);
        }else if(CouponRangeTypeEnum.EXCLUDE_ALL.getCode().equals(rule.getCouponRange())){
            couponRangeVOList = couponRangeManager.queryCouponRangeProductByProductId(productIdList, null, true);
        }
        if(CollectionUtils.isNotEmpty(couponRangeVOList) && CollectionUtils.isEmpty(couponRangeProductIdList)){
            couponRangeProductIdList = StreamUtils.toList(couponRangeVOList, CouponRangeVO::getProductId);
        }
        return couponRangeProductIdList;
    }

    @Override
    @Async
    public void createPartner(Long id, String stokId) {
        // 因为创建商家券有大概30s延迟
        try {
            Thread.sleep(45000);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        CouponRule rule = couponRuleDao.findById(id);
        if(!CouponChannelTypeEnum.WECHAT_COUPON.getCode().equals(rule.getChannel())){
            throw new BusinessException("领券渠道不是微信支付有优惠，不能建立合作关系！");
        }
        WeChatPartnerReq partnerDTO = WeChatPartnerReq.buildPartnerDTO(stokId, weChatMiniProgramConfig.getMchPartnerId());
        try {
            Map<String, String> headerMap = Maps.newHashMap();
            headerMap.put("Idempotency-Key", String.valueOf(id));
            log.info("创建合作关系 . 请求参数 --> req:{}", JSON.toJSONString(partnerDTO));
            String result = wxHttpClientTemplate.doPostWithHeader(CREATE_PARTNER, JSON.toJSONString(partnerDTO), headerMap);
            log.info("创建合作关系 . 相应参数 --> resp:{}", JSON.toJSONString(result));
            WeChatPartnerResp response = JSON.parseObject(result, new TypeReference<WeChatPartnerResp>() {});
            if (Objects.nonNull(response) && "ESTABLISHED".equals(response.getState())) {
                log.info("创建合作关系 . 合作建立成功");
            }
        } catch (Exception e) {
            log.error("创建合作关系 . 合作建立出错 --> id: {}, stokId: {}",id, stokId, e);
            throw new BusinessException("创建合作关系 . 合作建立出错:" + e.getMessage());
        }
    }

    @Override
    public CouponRule findByStockId(String stockId) {
        List<CouponRule> couponRules = couponRuleDao.findByStockId(stockId);
        if(CollectionUtils.isEmpty(couponRules) || couponRules.size() > 1){
            log.info("查询规则出错 . 查询结果 --> couponRules:{}", JSON.toJSONString(couponRules));
            throw new BusinessException("查询规则出错");
        }
        return couponRules.get(0);
    }

    @Override
    public void queryPartner(Map<String, String> params) {
        try {
            log.info("查询合作关系 . 请求参数 --> req:{}", JSON.toJSONString(params));
            String result = wxHttpClientTemplate.doGet(QUERY_PARTNER, params);
            log.info("查询合作关系 . 响应参数 --> resp:{}", result);
        } catch (Exception e) {
            log.error("查询合作关系 . 查询建立出错 --> params: {}", JSON.toJSONString(params), e);
        }
    }

    @Override
    public List<CouponRule> queryEnableReceiveAndReceivedCoupon(CouponRuleListDTO ruleDto, CustomerUserVO user) {
        List<CouponRule> couponRuleList = new ArrayList<>();
        if(Objects.isNull(user)){
            //未登录- 展示没有限制的券
            ruleDto.setReceiveCrowd(CouponReceiveCrowdTypeEnum.NO_LIMIT.getCode());
            couponRuleList = this.couponRuleList(ruleDto);
        }else{
            //可领券
            List<CouponRule> couponList = this.couponRuleList(ruleDto);
            List<CouponCenterListVO> couponCenterListVOList  = this.enableReceivedCoupon(couponList, user);
            couponRuleList = BeanUtils.copyList(couponCenterListVOList,CouponRule::new);
            List<Long> enableReceivedRuleIds = couponRuleList.stream().map(CouponRule::getId).collect(Collectors.toList());

            //已领券
            List<CouponInfo> receivedCouponList = this.queryMyEnableProductCoupon(user.getId());
            List<Long> ruleIds = receivedCouponList.stream().map(CouponInfo::getRuleId).distinct().collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(ruleIds)){
                ruleIds.removeAll(enableReceivedRuleIds);
                if(CollectionUtils.isNotEmpty(ruleIds)){
                    List<CouponRule> receivedCouponRuleList  = this.queryCouponRuleList(ruleIds);
                    couponRuleList.addAll(receivedCouponRuleList);
                }
            }
        }
        return couponRuleList;
    }

    @Override
    public List<CouponRule> queryReceivedCoupon(CouponRuleListDTO ruleDto, CustomerUserVO user) {
        List<CouponRule> couponRuleList = new ArrayList<>();
        if(Objects.isNull(user)){
            //未登录- 展示没有限制的券
            return Lists.newArrayList();
        }else{
            //已领券
            List<CouponInfo> receivedCouponList = this.queryMyEnableProductCoupon(user.getId());
            // receivedCouponList 按endTime从大到小排序
            receivedCouponList.sort(Comparator.comparing(CouponInfo::getEndTime).reversed());
            List<Long> ruleIds = receivedCouponList.stream().map(CouponInfo::getRuleId).distinct().collect(Collectors.toList());
            Map<Long, CouponInfo> couponInfoMap = StreamUtils.toMap(receivedCouponList, CouponInfo::getRuleId);
            if(CollectionUtils.isNotEmpty(ruleIds)){
                if(CollectionUtils.isNotEmpty(ruleIds)){
                    List<CouponRule> receivedCouponRuleList  = this.queryCouponRuleList(ruleIds);
                    receivedCouponRuleList.forEach(data -> {
                        CouponInfo couponInfo = couponInfoMap.get(data.getId());
                        if(Objects.nonNull(couponInfo)){
                            data.setEndTime(couponInfo.getEndTime());
                        }
                    });
                    couponRuleList.addAll(receivedCouponRuleList);
                }
            }
        }
        return couponRuleList;
    }

    @Override
    public List<CouponRule> queryReceivedGiftCoupon(CustomerUserVO user) {
        List<CouponRule> couponRuleList = new ArrayList<>();
        if(Objects.isNull(user)){
            //未登录- 展示没有限制的券
            return Lists.newArrayList();
        }else{
            //已领券
            List<CouponInfo> receivedCouponList = this.queryMyEnableGiftCoupon(user.getId());
            // 使用Stream
            StreamUtils.toMap(receivedCouponList, CouponInfo::getRuleId, CouponInfo::getRuleId);
            Map<Long, List<CouponInfo>> couponInfoMap = StreamUtils.group(receivedCouponList, CouponInfo::getRuleId);
            List<Long> ruleIds = receivedCouponList.stream().map(CouponInfo::getRuleId).distinct().collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(ruleIds)){
                if(CollectionUtils.isNotEmpty(ruleIds)){
                    List<CouponRule> receivedCouponRuleList  = this.queryCouponRuleList(ruleIds);
                    for (CouponRule couponRule : receivedCouponRuleList) {
                        couponRule.setCouponInfoList(couponInfoMap.get(couponRule.getId()));
                    }
                    couponRuleList.addAll(receivedCouponRuleList);
                }
            }
        }
        return couponRuleList;
    }

    @Override
    public ProductCouponDTO queryEnableReceiveAndReceivedCouponDTO(CouponRuleListDTO ruleDto, CustomerUserVO user) {
        ProductCouponDTO productCouponDTO = new ProductCouponDTO();
        List<CouponRule> couponRuleList = new ArrayList<>();
        if(Objects.isNull(user)){
            //未登录- 展示没有限制的券
            ruleDto.setReceiveCrowd(CouponReceiveCrowdTypeEnum.NO_LIMIT.getCode());
            couponRuleList = this.couponRuleList(ruleDto);
            productCouponDTO.setReceiveCouponRuleList(couponRuleList);
        }else{
            //可领券
            List<CouponRule> couponList = this.couponRuleList(ruleDto);
            List<CouponCenterListVO> couponCenterListVOList  = this.enableReceivedCoupon(couponList, user);
            couponRuleList = BeanUtils.copyList(couponCenterListVOList, CouponRule::new);
            List<CouponRule> receiveCouponRuleList = BeanUtils.deepListCopy(couponCenterListVOList, CouponRule.class);
            productCouponDTO.setReceiveCouponRuleList(receiveCouponRuleList);
            List<Long> enableReceivedRuleIds = couponRuleList.stream().map(CouponRule::getId).collect(Collectors.toList());


            //已领券
            List<CouponInfo> receivedCouponList = this.queryMyEnableProductCoupon(user.getId());
            List<Long> ruleIds = receivedCouponList.stream().map(CouponInfo::getRuleId).distinct().collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(ruleIds)){
                ruleIds.removeAll(enableReceivedRuleIds);
                if(CollectionUtils.isNotEmpty(ruleIds)){
                    List<CouponRule> receivedCouponRuleList  = this.queryCouponRuleList(ruleIds);
                    couponRuleList.addAll(receivedCouponRuleList);
                }
            }
        }
        productCouponDTO.setCouponRuleList(couponRuleList);
        return productCouponDTO;
    }

    @Override
    public void setCallbackUrl() {
        Map<String, Object> wxMiniCouponRequestMap = new HashMap<>();
        wxMiniCouponRequestMap.put("mchid", weChatMiniProgramConfig.getMchID());
        wxMiniCouponRequestMap.put("notify_url", propertiesConfig.getCouponNotifyUrl());
        try {
            String result = wxHttpClientTemplate.doPost(CALLBACKS, JSON.toJSONString(wxMiniCouponRequestMap));
            log.info("设置商家券事件通知地址返回参数：", result);
        } catch (Exception e) {
            log.error("设置商家券事件通知地址失败", e);
        }
    }

    private boolean checkPrivilegeUser(Long ruleId, Long userId) {
        List<PrivilegeItemUser> privilegeItemUserList = couponPrivilegeManager.queryUserOfPrivilegeByRuleId(ruleId);
        return privilegeItemUserList.stream().anyMatch(a -> userId.equals(a.getValueId()));
    }

    private boolean checkNewUser(CustomerUserVO customerUserVO, Integer rookieDay) {
        Date nowTime = new Date();
        Calendar newUserValidityTime = Calendar.getInstance();

        if(Objects.isNull(customerUserVO.getFirstLoginTime())){
            if(StringUtils.isNotBlank(customerUserVO.getPhone())){
                CustomerUserPageDTO dto = new CustomerUserPageDTO();
                dto.setPhone(customerUserVO.getPhone());
                dto.setLogOff(UserLogOffEnum.UNREGISTERED.getCode());
                List<CustomerUserVO> customerUserVos = customerUserManager.findAllByCondition(dto);
                if (CollectionUtils.isNotEmpty(customerUserVos)) {
                    CustomerUserVO customerUser = customerUserVos.get(0);
                    if(Objects.nonNull(customerUser)){
                        if(Objects.nonNull(customerUser.getFirstLoginTime())){
                            customerUserVO.setFirstLoginTime(customerUser.getFirstLoginTime());
                        }else{
                            return true;
                        }
                    }else{
                        return false;
                    }
                }else{
                    return false;
                }
            }else{
                return false;
            }
        }

        newUserValidityTime.setTime(customerUserVO.getFirstLoginTime());
        newUserValidityTime.add(Calendar.DATE, rookieDay);
        if (newUserValidityTime.getTime().compareTo(nowTime) >= 0) {
            return true;
        }
        return false;
    }

    private boolean checkNewUserV2(CustomerUserVO user) {
        CustomerUserLabel userLabel = customerUserLabelDao.findOneByUserId(user.getId());
        if(Objects.isNull(userLabel) || Objects.equals(userLabel.getIsNewUser(), BasicFlagEnum.NO.getKey())){
            return false;
        }else{
            return true;
        }
    }

    /** 校验是否首次入会的用户-根据joinTime
     *
     * @param customerUserVO
     * @param firstJoinDay
     * @return
     */
    private boolean checkFistJoinUser(CustomerUserVO customerUserVO, Integer firstJoinDay) {
        Date nowTime = new Date();
        Calendar firstJoinUserValidityTime = Calendar.getInstance();

        if(Objects.isNull(customerUserVO.getJoinTime())){
            if(StringUtils.isNotBlank(customerUserVO.getPhone())){
                CustomerUserPageDTO dto =new CustomerUserPageDTO();
                dto.setPhone(customerUserVO.getPhone());
                dto.setLogOff(UserLogOffEnum.UNREGISTERED.getCode());
                List<CustomerUserVO> customerUserVos = customerUserManager.findAllByCondition(dto);
                if (CollectionUtils.isNotEmpty(customerUserVos)) {
                    CustomerUserVO customerUser = customerUserVos.get(0);
                    if(Objects.nonNull(customerUser)){
                        if(Objects.nonNull(customerUser.getJoinTime())){
                            customerUserVO.setJoinTime(customerUser.getJoinTime());
                        }else{
                            return false;
                        }
                    }else{
                        return false;
                    }
                }else{
                    return false;
                }
            }else{
                return false;
            }
        }

        firstJoinUserValidityTime.setTime(customerUserVO.getJoinTime());
        firstJoinUserValidityTime.add(Calendar.DATE, firstJoinDay);
        if (firstJoinUserValidityTime.getTime().compareTo(nowTime) >= 0) {
            return true;
        }
        return false;
    }

    private boolean checkLabel(CustomerUserVO customerUserVO, String labelId) {
        if(StringUtils.isNotBlank(labelId)){
            List<Long> labelIds = Arrays.stream(labelId.split(","))
                    .map(Long::valueOf)
                    .collect(Collectors.toList());
            for (Long id : labelIds) {
                if(peopleManager.containsPeople(id, customerUserVO.getId())){
                    return true;
                }
            }
        }
        return false;
    }

    @Override
    public ShopCartCouponVO getCouponItemDetailPrice(List<ShopCartItemInfoDTO> itemInfoDTOList, CouponRuleSimpleDTO rule, List<CouponRange> couponRangeList) {

        ShopCartCouponVO shopCartCouponVO = new ShopCartCouponVO();

        ShopCartCouponDTO shopCartCouponDTO = CouponUtil.getShopCartBestPrice(itemInfoDTOList, rule, couponRangeList, 2);

        BigDecimal shopCartSumSalePrice = itemInfoDTOList.stream().map(ShopCartItemInfoDTO::getSumSalePrice).reduce(BigDecimal.ZERO,BigDecimal::add);
        BigDecimal shopCartSumListPrice = itemInfoDTOList.stream().map(ShopCartItemInfoDTO::getSumListPrice).reduce(BigDecimal.ZERO,BigDecimal::add);
        shopCartCouponVO.setProductDiscount(shopCartSumListPrice.subtract(shopCartSumSalePrice));
        shopCartCouponVO.setTotalAmount(shopCartSumSalePrice);
        shopCartCouponVO.setProductAmount(shopCartSumListPrice);

        if(Objects.isNull(shopCartCouponDTO)){
            shopCartCouponVO.setCouponDiscount(BigDecimal.ZERO);
            shopCartCouponVO.setRealAmount(shopCartSumSalePrice);
            shopCartCouponVO.setTotalDiscount(shopCartCouponVO.getProductDiscount());
            return shopCartCouponVO;
        }

        //优惠券优惠的金额
        BigDecimal couponDiscount = shopCartCouponDTO.getCouponAmount();
        List<ShopCartItemCouponPriceVO> itemCouponPriceVOList = new ArrayList<>();
        int sumCount = shopCartCouponDTO.getShopCartItemInfoList().size();
        int count = 0;
        BigDecimal addCouponDiscount = BigDecimal.ZERO;
        BigDecimal realCouponAmount = BigDecimal.ZERO;
        BigDecimal realCouponDiscount = BigDecimal.ZERO;
        BigDecimal shopCartCouponSumSalePrice = shopCartCouponDTO.getShopCartItemInfoList().stream().map(ShopCartItemInfoDTO::getSumSalePrice).reduce(BigDecimal.ZERO,BigDecimal::add);
        for (ShopCartItemInfoDTO shopCartItemInfoDTO : shopCartCouponDTO.getShopCartItemInfoList()) {
            ShopCartItemCouponPriceVO shopCartItemCouponPriceVO = new ShopCartItemCouponPriceVO();
            shopCartItemCouponPriceVO.setShopCartId(shopCartItemInfoDTO.getId());
            shopCartItemCouponPriceVO.setSalePrice(shopCartItemInfoDTO.getSalePrice());
            shopCartItemCouponPriceVO.setDiscountActivityId(shopCartItemInfoDTO.getDiscountActivityId());
            if(shopCartItemInfoDTO.getSplitCouponFlag().equals(1)){
                shopCartItemCouponPriceVO.setCouponPrice(null);
                shopCartItemCouponPriceVO.setRealPrice(shopCartItemInfoDTO.getSumSalePrice());
            }else{
                count ++;
                shopCartCouponSumSalePrice = shopCartCouponDTO.getShopCartItemInfoList().stream().filter(x-> !x.getSplitCouponFlag().equals(1)).map(ShopCartItemInfoDTO::getSumSalePrice).reduce(BigDecimal.ZERO,BigDecimal::add);
                sumCount = shopCartCouponDTO.getShopCartItemInfoList().stream().filter(x-> !x.getSplitCouponFlag().equals(1)).collect(Collectors.toList()).size();

                BigDecimal percentage = shopCartItemInfoDTO.getSumSalePrice().divide(shopCartCouponSumSalePrice,2, BigDecimal.ROUND_DOWN);
                BigDecimal couponPrice = shopCartItemInfoDTO.getSalePrice();
                if(count == sumCount){
                    couponPrice = couponDiscount.subtract(addCouponDiscount);
                }else{
                    couponPrice = percentage.multiply(couponDiscount).setScale( 2, BigDecimal.ROUND_HALF_UP);
                }
                addCouponDiscount = addCouponDiscount.add(couponPrice);
                if(Objects.nonNull(shopCartItemInfoDTO.getCycleId()) && shopCartItemInfoDTO.getCycleId() > 0){
                    //周期购
                    BigDecimal singleCouponPrice = shopCartItemInfoDTO.getSumSalePrice().subtract(couponPrice);
                    shopCartItemCouponPriceVO.setCouponPrice(singleCouponPrice.compareTo(BigDecimal.ZERO) <= 0 ? new BigDecimal("0.01") : singleCouponPrice);
                    shopCartItemCouponPriceVO.setRealPrice(shopCartItemCouponPriceVO.getCouponPrice());
                }else{
                    //非周期购
                    BigDecimal realPrice = shopCartItemInfoDTO.getSumSalePrice().subtract(couponPrice);
                    BigDecimal singleCouponPrice = realPrice.divide(BigDecimal.valueOf(shopCartItemInfoDTO.getCount()), 2, BigDecimal.ROUND_UP);
                    shopCartItemCouponPriceVO.setCouponPrice(singleCouponPrice.compareTo(BigDecimal.ZERO) <= 0 ? new BigDecimal("0.01") : singleCouponPrice);
                    shopCartItemCouponPriceVO.setRealPrice(realPrice.compareTo(BigDecimal.ZERO) <= 0 ? new BigDecimal("0.01") : realPrice);
                }
            }
            shopCartItemCouponPriceVO.setCount(shopCartItemInfoDTO.getCount());
            itemCouponPriceVOList.add(shopCartItemCouponPriceVO);
            realCouponAmount = realCouponAmount.add(shopCartItemCouponPriceVO.getRealPrice());
            //实际优惠券的优惠金额 = 券前应付价-实付价
            realCouponDiscount = shopCartCouponDTO.getTotalAmount().subtract(realCouponAmount);
        }
        shopCartCouponVO.setOnlyCouponAmount(shopCartCouponDTO.getOnlyCouponAmount());
        shopCartCouponVO.setRuleId(shopCartCouponDTO.getRuleId());
        shopCartCouponVO.setItemCouponPriceVOList(itemCouponPriceVOList);
        shopCartCouponVO.setCouponDiscount(realCouponDiscount);
        shopCartCouponVO.setRealAmount(shopCartCouponDTO.getOnlyNoCouponAmount().add(realCouponAmount));
        shopCartCouponVO.setTotalDiscount(realCouponDiscount.add(shopCartCouponVO.getProductDiscount()));

        return shopCartCouponVO;
    }

    @Override
    public Long couponNum(CustomerUserVO customerUserVO, CouponRuleListDTO dto) {
        //查询符合条件的所有上线的优惠券
        List<CouponRule> couponRuleList = couponRuleDao.searchOnline(dto);
        //判断是否领过首次入会类型的券
        Boolean fristCouponFlag = checkFirstCoupon(customerUserVO);

        List<CouponRule> enableReceivedCouponList = new ArrayList<>();
        Map<Long, Integer> realRemainingNumMap = new HashMap<>();
        for (CouponRule rule : couponRuleList) {
            //优惠券已领张数
            Integer receivedNum = rule.getReceiveCount();

            if(fristCouponFlag && CouponReceiveCrowdTypeEnum.FIRST_JOIN.getCode().equals(rule.getReceiveCrowd())){
                continue;
            }
            if (receivedNum < rule.getIssuedQuantity()) {
                Integer remainingNum = rule.getIssuedQuantity() - receivedNum ;
                if(remainingNum < rule.getReceiveNum()){
                    realRemainingNumMap.put(rule.getId(), remainingNum);
                }
                enableReceivedCouponList.add(rule);
            }
        }

        //获取会员等级
        getUserGrade(customerUserVO);

        List<Long> couponRuleIds = StreamUtils.convert(couponRuleList, CouponRule::getId);
        if(CollectionUtils.isEmpty(couponRuleIds)){
            return 0L;
        }
        List<CouponReceiveNumDTO> couponReceiveNumList = couponInfoDao.selectCouponReceiveNum(couponRuleIds, customerUserVO.getId());
        Map<Long, Long> couponReceiveNumMap = StreamUtils.toMap(couponReceiveNumList, CouponReceiveNumDTO::getRuleId, CouponReceiveNumDTO::getNum);

        Long couponReceiveNum = 0L;
        //还可领的优惠券
        for (CouponRule rule : enableReceivedCouponList) {
            if (StringUtils.isBlank(rule.getStockId())) {
                continue;
            }
            //校验有效天数
            if (checkCouponEffectiveTime(rule)){
                continue;
            }

            //计算优惠券剩余可领张数
            Long receivedNum = couponReceiveNumMap.get(rule.getId());
            if(Objects.isNull(receivedNum)){
                receivedNum = 0L;
            }
            int remainingNum = rule.getReceiveNum() - receivedNum.intValue();
            Integer realRemainingNum = realRemainingNumMap.get(rule.getId());
            if(Objects.nonNull(realRemainingNum)){
                //实际剩余数小于计划可领数
                if(realRemainingNum < remainingNum){
                    remainingNum = realRemainingNum;
                }
            }
            if (remainingNum <= 0) {
                continue;
            }

            //移动兑换积分的券
            if(CouponChannelTypeEnum.CMCC.getCode().equals(rule.getChannel())
                    || CouponChannelTypeEnum.FULL_REDUCE.getCode().equals(rule.getChannel())){
                List<CouponInfo> couponInfoList = couponInfoDao.findNotReceiveByRuleId(customerUserVO.getId(), rule.getId());
                if(CollectionUtils.isEmpty(couponInfoList)){
                    continue;
                }else{
                    remainingNum = couponInfoList.size();
                }
            }
            if(peopleLimitVerify(rule, customerUserVO)){
                couponReceiveNum = couponReceiveNum + remainingNum;
            }
        }
        return couponReceiveNum;
    }

    @Override
    public List<CouponRule> findByIdList(List<Long> ids) {
        return couponRuleDao.findByIdList(ids);
    }

    @Override
    public List<CouponRule> findByIds(List<Long> ids) {
        return couponRuleDao.findByIds(ids);
    }

    @Override
    public void insert(CouponInfo couponInfo) {
        couponInfoDao.insert(couponInfo);
    }

    @Override
    public void statsCount(List<Long> ids) {
        List<CouponRule> couponRuleList = couponRuleDao.findByIds(ids);
        for (CouponRule rule : couponRuleList) {
            updateCount(rule);
        }
    }
    @Override
    public int updateReceiveCount(Long id, Integer count){
        return couponRuleDao.updateReceiveCount(id, count);
    }

    @Override
    public List<CouponRule> findNeddCount() {
        return couponInfoDao.findNeddCount();
    }

    /**
     * 更新优惠券领用数量
     * @param couponRule
     */
    @Override
    public void updateCount(CouponRule couponRule) {
        RLock lock = redissonClient.getLock("couponRuleLock:" + couponRule.getId());
        if (Objects.isNull(lock)) {
            log.error("getLockException");
            return;
        }
        try{
            if (lock.tryLock(5, TimeUnit.SECONDS)) {
                // 已领用数量
                Long receivedNum = couponRuleDao.countRuleNumByStatus(couponRule.getId(), Arrays.asList(
                        CouponInfoStatusTypeEnum.NOT_RECEIVE.getCode(),
                        CouponInfoStatusTypeEnum.RECEIVE.getCode(),
                        CouponInfoStatusTypeEnum.USED.getCode(),
                        CouponInfoStatusTypeEnum.EXPIRE.getCode(),
                        CouponInfoStatusTypeEnum.VOIDED.getCode(),
                        CouponInfoStatusTypeEnum.OCCUPY.getCode()
                ));
                // 已使用数量
                Long useNum = couponRuleDao.countRuleNumByStatus(couponRule.getId(), Arrays.asList(
                        CouponInfoStatusTypeEnum.USED.getCode()
                ));
                couponRule.setReceiveCount(receivedNum.intValue());
                couponRule.setUseCount(useNum.intValue());
                couponRuleDao.updateByIdSelective(couponRule);
            }
        }catch (Exception e){
            log.error("优惠券数据计算处理失败. ", e);
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    @Override
    public void updateStats(CouponRule couponRule) {
    }

    private Boolean checkFirstCoupon(CustomerUserVO customerUserVO) {
        String cacheKey = "CustomerCache:firstJoinCoupon:userId:" + customerUserVO.getId();
        Boolean fristCouponFlag  = redisOperation.get(cacheKey);
        if(Objects.isNull(fristCouponFlag)){
            fristCouponFlag = false;
            int firstJoinCouponNum = couponInfoDao.countUserCouponByCrowd(customerUserVO.getId(), CouponReceiveCrowdTypeEnum.FIRST_JOIN.getCode());
            if(firstJoinCouponNum > 0){
                fristCouponFlag = true;
            }
            redisOperation.setnx(cacheKey, fristCouponFlag, 7L, TimeUnit.DAYS);
        }
        return fristCouponFlag;
    }

    private void getUserGrade(CustomerUserVO customerUserVO) {
        //获取会员等级
        CustomerUser userFromDb = customerUserManager.findById(customerUserVO.getId());
        if(Objects.nonNull(userFromDb)){
            customerUserVO.setGrade(userFromDb.getGrade());
        }
    }


}
