package com.hengtiansoft.order.manager.impl;

import com.alibaba.excel.EasyExcel;
import com.hengtiansoft.common.entity.exception.BusinessException;
import com.hengtiansoft.common.entity.vo.PageVO;
import com.hengtiansoft.common.enumeration.BasicFlagEnum;
import com.hengtiansoft.common.enumeration.DataExportStatusEnum;
import com.hengtiansoft.common.util.BeanUtils;
import com.hengtiansoft.common.util.PageUtils;
import com.hengtiansoft.common.util.StreamUtils;
import com.hengtiansoft.item.dao.CardDao;
import com.hengtiansoft.item.entity.dto.SkuProductBaseDTO;
import com.hengtiansoft.item.entity.po.Card;
import com.hengtiansoft.item.enumeration.CardStatusEnum;
import com.hengtiansoft.item.interfaces.SkuManager;
import com.hengtiansoft.order.adapter.OrderSyncAdapter;
import com.hengtiansoft.order.dao.*;
import com.hengtiansoft.order.entity.dto.OrderAfterExportDTO;
import com.hengtiansoft.order.entity.dto.OrderAfterSaleDTO;
import com.hengtiansoft.order.entity.dto.OrderAfterSaleListDTO;
import com.hengtiansoft.order.entity.dto.OrderAfterSkuDTO;
import com.hengtiansoft.order.entity.po.*;
import com.hengtiansoft.order.entity.vo.*;
import com.hengtiansoft.order.enums.*;
import com.hengtiansoft.order.manager.*;
import com.hengtiansoft.order.util.OrderAfterSaleUtil;
import com.hengtiansoft.pay.entity.dto.WeChatRefundDTO;
import com.hengtiansoft.pay.interfaces.PayManager;
import com.hengtiansoft.privilege.dao.FullReduceRecordDao;
import com.hengtiansoft.privilege.entity.po.FullReduceRecord;
import com.hengtiansoft.privilege.enums.FullRuleRangeTypeEnum;
import com.hengtiansoft.thirdpart.entity.dto.youshu.YoushuReturnOrderAddDetailReq;
import com.hengtiansoft.thirdpart.entity.vo.kdniao.KdniaoExpress;
import com.hengtiansoft.thirdpart.entity.vo.kdniao.KdniaoTrace;
import com.hengtiansoft.thirdpart.util.AliyunOSSUtils;
import com.hengtiansoft.thirdpart.util.ExcelFillCellMergeStrategy;
import com.hengtiansoft.thirdpart.util.KdniaoTrackQueryAPI;
import com.hengtiansoft.thirdpart.util.YoushuUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.github.pagehelper.page.PageMethod.startPage;

/**
 * <AUTHOR>
 **/
@Component
@Slf4j
public class OrderAfterSaleManagerImpl implements OrderAfterSaleManager {

    @Autowired
    private OrderAfterSaleDao orderAfterSaleDao;
    @Autowired
    private OrderAfterSaleSkuDao orderAfterSaleSkuDao;
    @Autowired
    private OrderPayDao orderPayDao;
    @Autowired
    private PayManager payManager;
    @Autowired
    private KdniaoTrackQueryAPI kdniaoTrackQueryAPI;
    @Autowired
    private OrderGiftManager orderGiftManager;
    @Autowired
    private OrderAfterSaleManager orderAfterSaleManager;
    @Autowired
    private MilkDispatchPlanDao milkDispatchPlanDao;
    @Autowired
    private OrderSkuDao orderSkuDao;
    @Autowired
    private OrderInfoDao orderInfoDao;
    @Autowired
    private OrderSyncAdapter orderSyncAdapter;
    @Autowired
    private CardDao cardDao;
    @Autowired
    private YoushuUtil youshuUtil;
    @Autowired
    private SkuManager skuManager;
    @Autowired
    private OrderSkuCardDao orderSkuCardDao;
    @Resource
    private OrderSkuManager orderSkuManager;
    @Resource
    private AliyunOSSUtils aliyunOssUtils;
    @Resource
    private DataExportTaskDao dataExportTaskDao;
    @Resource
    private FullReduceRecordDao fullReduceRecordDao;
    @Resource
    private CouponRuleManager couponRuleManager;
    @Resource
    private OrderManager orderManager;

    @Resource
    private PtOrderManager ptOrderManager;

    @Resource
    private PtSubOrderManager ptSubOrderManager;

    @Override
    public PageVO<OrderAfterSaleListVO> findOrderAfterSaleList(OrderAfterSaleListDTO dto) {
        startPage(dto.getPageNum(), dto.getPageSize());
        List<OrderAfterSaleListVO> orderAfterSaleListVOS = orderAfterSaleDao.findOrderAfterSaleListV2(dto);

        if(CollectionUtils.isEmpty(orderAfterSaleListVOS)){
            return PageUtils.emptyPage(dto);
        }
        //售后单明细
        List<String> orderAfterNoList = StreamUtils.convert(orderAfterSaleListVOS, OrderAfterSaleListVO::getOrderAfterNo);
        List<OrderAfterSaleSku> orderAfterSaleSkuList = orderAfterSaleSkuDao.findByOrderAfterNoList(orderAfterNoList);
        Map<String, List<OrderAfterSaleSku>> orderAfterSaleSkuMap = StreamUtils.group(orderAfterSaleSkuList, OrderAfterSaleSku::getOrderAfterNo);

        List<String> orderNoList = StreamUtils.convert(orderAfterSaleSkuList, OrderAfterSaleSku::getOrderNo);
        List<OrderSku> orderSkuList = orderSkuDao.findByOrderNoList(orderNoList);
        Map<String, List<OrderSku>> orderSkuMap = StreamUtils.group(orderSkuList, OrderSku::getOrderNo);
        List<OrderInfo> orderInfoList = orderInfoDao.findByOrderNos(orderNoList);
        Map<String, OrderInfo> orderInfoMap = StreamUtils.toMap(orderInfoList, OrderInfo::getOrderNo);

        List<String> cardNumberList = orderAfterSaleSkuList.stream().filter(x -> StringUtils.isNotBlank(x.getCardNumber()))
                .map(OrderAfterSaleSku::getCardNumber).collect(Collectors.toList());
        Map<String, Card> cardMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(cardNumberList)){
            List<Card> cardList = cardDao.selectByNumberList(cardNumberList);
            cardMap = StreamUtils.toMap(cardList, card -> card.getCardNumber().toUpperCase());
        }
        // 如果存在配送计划的售后单 查询关联的奶卡订单信息做填充
        setPlanAfterOrder(orderAfterSaleListVOS);

        Map<String, Card> finalCardMap = cardMap;
        return PageUtils.convert(orderAfterSaleListVOS, data -> {
            OrderAfterSaleListVO orderAfterSaleListVO = new OrderAfterSaleListVO();
            BeanUtils.copy(data, orderAfterSaleListVO);
            //主品订单信息
            OrderInfo order = orderInfoMap.get(data.getOrderNo());
            if(null != order){
                orderAfterSaleListVO.setOrderStatus(order.getOrderStatus());
                orderAfterSaleListVO.setOrderType(order.getOrderType());
                orderAfterSaleListVO.setProductType(order.getProductType());
            }
            List<OrderAfterSaleSku> orderAfterSaleSkus = orderAfterSaleSkuMap.get(data.getOrderAfterNo());
            if(CollectionUtils.isNotEmpty(orderAfterSaleSkus)){
                //申请售后商品总数量
                Integer refundCount = StreamUtils.mapToInt(orderAfterSaleSkus, OrderAfterSaleSku::getCount).sum();
                orderAfterSaleListVO.setCount(refundCount);
                //售后明细
                List<OrderAfterSaleSku> mergeAfterSaleSkuList = OrderAfterSaleUtil.mergeBySkuCode(orderAfterSaleSkus);
                List<OrderAfterSaleSkuListVO> skuList = new ArrayList<>();
                for (OrderAfterSaleSku afterSaleSku: mergeAfterSaleSkuList) {
                    List<OrderSku> orderSkus = orderSkuMap.get(afterSaleSku.getOrderNo());
                    OrderSku firstOrderSku = StreamUtils.getFirst(orderSkus);
                    OrderInfo orderInfo = orderInfoMap.get(firstOrderSku.getOrderNo());
                    int count = StreamUtils.mapToInt(orderSkus, OrderSku::getCount).sum();

                    OrderAfterSaleSkuListVO saleSkuListVO = new OrderAfterSaleSkuListVO();
                    saleSkuListVO.setCount(afterSaleSku.getCount());
                    saleSkuListVO.setProductName(firstOrderSku.getProductName());
                    saleSkuListVO.setSkuDesc(firstOrderSku.getSkuDesc());
                    saleSkuListVO.setSkuPicUrl(firstOrderSku.getSkuPicUrl());
                    saleSkuListVO.setUnitSalePrice(firstOrderSku.getTotalAmount().divide(BigDecimal.valueOf(count), 2, BigDecimal.ROUND_HALF_UP));
                    saleSkuListVO.setOrderType(orderInfo.getOrderType());
                    saleSkuListVO.setIsGift(afterSaleSku.getIsGift());
                    saleSkuListVO.setProductType(orderInfo.getProductType());
                    saleSkuListVO.setRealAmount(orderInfo.getRealAmount());

                    saleSkuListVO.setProductTypeDesc("-");
                    saleSkuListVO.setBuyTypeDesc("-");
                    if(!BasicFlagEnum.YES.getKey().equals(afterSaleSku.getIsGift())){
                        saleSkuListVO.setProductTypeDesc(OrderProductTypeEnum.getDescByCode(orderInfo.getProductType()));
                        saleSkuListVO.setBuyTypeDesc(OrderTypeEnum.getBuyTypeDesc(orderInfo.getOrderType()));
                    }
                    skuList.add(saleSkuListVO);

                    //奶卡信息
                    List<CardSimpleVO> cardSimpleList = new ArrayList<>();
                    if(StringUtils.isNotBlank(afterSaleSku.getCardNumber())){
                        String[] cardNumbers = StringUtils.split(afterSaleSku.getCardNumber(), ",");
                        if(null != cardNumbers){
                            for (String cardNumber: cardNumbers) {
                                if(StringUtils.isBlank(cardNumber)){
                                    continue;
                                }
                                CardSimpleVO cardSimpleVO = new CardSimpleVO();
                                Card card = finalCardMap.get(cardNumber.toUpperCase());
                                if(Objects.isNull(card)){
                                    continue;
                                }
                                cardSimpleVO.setCardNumber(card.getCardNumber());
                                cardSimpleVO.setCardStatus(card.getCardStatus());
                                cardSimpleVO.setCardStatusDesc(CardStatusEnum.getDescByCode(card.getCardStatus()));
                                cardSimpleList.add(cardSimpleVO);
                            }
                        }
                    }
                    saleSkuListVO.setCardList(cardSimpleList);
                    orderAfterSaleListVO.setSkuList(skuList);
                    orderAfterSaleListVO.setRealAmount(StreamUtils.mapReduce(skuList, OrderAfterSaleSkuListVO::getRealAmount, BigDecimal::add));
                }
            }

            if(CommonOrderTypeEnum.PLAN.getCode().equals(orderAfterSaleListVO.getOrderAfterType())){
                orderAfterSaleListVO.setRealAmount(BigDecimal.ZERO);
                orderAfterSaleListVO.setTotalAmount(BigDecimal.ZERO);
                orderAfterSaleListVO.setRefundAmount(BigDecimal.ZERO);
                orderAfterSaleListVO.setUnitSalePrice(BigDecimal.ZERO);
            }

            List<OrderGiftCouponVO> giftCouponList = orderAfterSaleManager.buildGiftCouponVO(data.getFullReduceRecordId());
            orderAfterSaleListVO.setGiftCouponList(giftCouponList);
            orderAfterSaleListVO.setAfterSaleStatusDesc(OrderAfterStatusEnum.getDescByCode(orderAfterSaleListVO.getAfterSaleStatus()));
            orderAfterSaleListVO.setAfterSaleTypeDesc(OrderAfterSaleTypeEnum.getDescByCode(orderAfterSaleListVO.getAfterSaleType()));
            orderAfterSaleListVO.setOrderTypeDesc(OrderTypeEnum.getDescByCode(orderAfterSaleListVO.getOrderType()));
            orderAfterSaleListVO.setPushFlagDesc(PushFlagEnum.getDescByCode(orderAfterSaleListVO.getPushFlag()));

            //售后单关联赠品信息
            OrderInfo orderInfo = orderInfoDao.findByOrderNo(data.getOrderNo());
            String useCouponGiftOrderNo = orderInfo.getOrderNo();
            if(OrderParentTypeEnum.CHILD.getCode().equals(orderInfo.getParentType())
                    && StringUtils.isNotBlank(orderInfo.getOrderParentNo())){
                OrderInfo parentOrderInfo = orderInfoDao.findByOrderNo(orderInfo.getOrderParentNo());
                useCouponGiftOrderNo = parentOrderInfo.getOrderNo();
            }
            List<OrderGift> orderGiftList = orderGiftManager.selectByOrderNo(useCouponGiftOrderNo);
            if(CollectionUtils.isNotEmpty(orderGiftList)){
                orderAfterSaleListVO.setOrderGiftList(BeanUtils.copyList(orderGiftList, OrderGiftVO::new));
            }
            return orderAfterSaleListVO;
        });
    }

    @Override
    public OrderAfterSaleDTO findByAfterSaleNo(String afterSaleNo) {
        OrderAfterSale orderAfterSale = orderAfterSaleDao.findByOrderAfterNo(afterSaleNo);
        return BeanUtils.copy(orderAfterSale, OrderAfterSaleDTO::new);
    }

    @Override
    public List<OrderAfterSkuDTO> findAfterSkuByAfsNo(String afsNo) {
        List<OrderAfterSaleSku> orderAfterNo = orderAfterSaleSkuDao.findByOrderAfterNo(afsNo);
        return BeanUtils.copyList(orderAfterNo, OrderAfterSkuDTO::new);
    }

    @Override
    public void refund(OrderInfo orderInfo, OrderAfterSaleDTO orderAfterSale) {
        // 进来的只有可能子订单和单独订单，父订单进来直接报错
        OrderParentTypeEnum parentTypeEnum = OrderParentTypeEnum.getEnum(orderInfo.getParentType());
        OrderInfo executeOrderInfo;
        if(OrderParentTypeEnum.SIGNAL.equals(parentTypeEnum)){
            executeOrderInfo = orderInfo;
        }else if(OrderParentTypeEnum.CHILD.equals(parentTypeEnum)){
            executeOrderInfo = orderInfoDao.findByOrderNo(orderInfo.getOrderParentNo());
        }else{
            // 父订单进来直接报错
            throw new BusinessException("订单类型有误");
        }
        OrderPay orderPay = orderPayDao.findByOrderPayNo(executeOrderInfo.getOrderNo(), executeOrderInfo.getOrderNo());
        WeChatRefundDTO weChatRefundDTO = new WeChatRefundDTO();
        weChatRefundDTO.setOutRefundNo(orderAfterSale.getOrderAfterNo());
        weChatRefundDTO.setOutTradeNo(orderPay.getOrderNo());
        weChatRefundDTO.setTotalFee(orderPay.getPayAmount().multiply(new BigDecimal(100)).longValue());
        weChatRefundDTO.setRefundFee(orderAfterSale.getRealAmount().multiply(new BigDecimal(100)).longValue());
        payManager.weChatRefund(weChatRefundDTO);
    }

    @Override
    public void refund(String orderNo, String orderAfterNo, BigDecimal refundFee) {
        OrderPay orderPay = orderPayDao.findByOrderPayNo(orderNo, orderNo);
        WeChatRefundDTO weChatRefundDTO = new WeChatRefundDTO();
        weChatRefundDTO.setOutRefundNo(orderAfterNo);
        weChatRefundDTO.setOutTradeNo(orderPay.getOrderNo());
        weChatRefundDTO.setTotalFee(orderPay.getPayAmount().multiply(new BigDecimal(100)).longValue());
        weChatRefundDTO.setRefundFee(refundFee.multiply(new BigDecimal(100)).longValue());
        payManager.weChatRefund(weChatRefundDTO);

    }

    @Override
    public void apply(OrderAfterSale orderAfterSale, List<OrderAfterSaleSku> orderAfterSaleSkus) {
        orderAfterSaleDao.saveOrderAfterSale(orderAfterSale);
        orderAfterSaleSkuDao.saveOrderAfterSku(orderAfterSaleSkus);

    }

    @Override
    public void update(OrderAfterSale orderAfterSale) {
        orderAfterSaleDao.updateSelectiveByAfterNo(orderAfterSale.getOrderAfterNo(), orderAfterSale);
    }

    @Override
    public List<OrderAfterSale> findByOrderNoAndStatus(List<String> orderNos, OrderAfterStatusEnum orderAfterStatusEnum) {
        List<OrderAfterSale> daoByOrderNos = orderAfterSaleDao.findByOrderNos(orderNos);
        if (CollectionUtils.isNotEmpty(daoByOrderNos)){
            return daoByOrderNos.stream().filter(orderAfterSale -> !OrderAfterStatusEnum.CANCELED.getCode()
                    .equals(orderAfterSale.getAfterSaleStatus())).collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    @Override
    public List<OrderAfterSale> findByStatusAndOrderNo(String orderNo, List<Integer> status) {
        return orderAfterSaleDao.findByStatusAndOrderNo(orderNo,status);
    }

    @Override
    public List<OrderAfterSale> findByStatusAndOrderNos(List<String> orderNos, List<Integer> statusList, Integer applyType) {
        return orderAfterSaleDao.findByStatusAndOrderNos(orderNos, statusList, applyType);
    }

    @Override
    public String addOrderAfterToYoushu(String orderAfterNo) {

        OrderAfterSale orderAfterSale = orderAfterSaleDao.findByOrderAfterNo(orderAfterNo);

        List<OrderAfterSaleSku> orderAfterSaleSkus = orderAfterSaleSkuDao.findByOrderAfterNo(orderAfterNo);

        OrderInfo orderInfo = orderInfoDao.findByOrderNo(orderAfterSale.getOrderNo());

        //封装有数退款单
        List<Long> skuIds = StreamUtils.convertFilter(orderAfterSaleSkus, OrderAfterSaleSku::getSkuId, Objects::nonNull);
        List<SkuProductBaseDTO> skuProductBaseDTOList = skuManager.skuProductList(skuIds);
        Map<Long, SkuProductBaseDTO> skuMap = StreamUtils.toMap(skuProductBaseDTOList, SkuProductBaseDTO::getId);
        YoushuReturnOrderAddDetailReq returnOrderAddReq = OrderAfterSaleUtil.buildYoushuReturnOrderReq(orderAfterSale, orderAfterSaleSkus, orderInfo.getOrderParentNo(), skuMap);

        //退单同步有数
        youshuUtil.addReturnOrder(returnOrderAddReq);

        OrderParentTypeEnum parentTypeEnum = OrderParentTypeEnum.getEnum(orderInfo.getParentType());

        if(OrderParentTypeEnum.CHILD == parentTypeEnum){
            orderInfo = orderInfoDao.findByOrderNo(orderInfo.getOrderParentNo());
        }

        //整单退款
        if(orderAfterSale.getRealAmount().compareTo(orderInfo.getRealAmount()) == 0){
            return "";
        }

        //1150&1160
        YoushuOrderStatusEnum orderStatusEnum = YoushuOrderStatusEnum.getEnumByCode(orderInfo.getOrderStatus());
        if(YoushuOrderStatusEnum.DELIVERY == orderStatusEnum || YoushuOrderStatusEnum.RECEIVE == orderStatusEnum || YoushuOrderStatusEnum.CLOSE == orderStatusEnum){
            return "";
        }

        return orderInfo.getOrderParentNo();
    }

    @Override
    public List<OrderAfterSale> findByStatusNotInAndOrderNo(String orderNo, List<Integer> status) {
        return orderAfterSaleDao.findByStatusNotInAndOrderNo(orderNo, status);
    }

    @Override
    public ExpressInfoVO queryExpress(String expressCode,String phone) {

        ExpressInfoVO result = new ExpressInfoVO();
        result.setLogisticCode(expressCode);
        result.setContent(new ArrayList<>());
        try {
            KdniaoExpress kdniaoExpress = kdniaoTrackQueryAPI.getOrderTracesByJson(expressCode, phone);
            result.setCompanyName(kdniaoExpress.getCompanyName());
            result.setLocation(kdniaoExpress.getLocation());
            result.setShipperCode(kdniaoExpress.getShipperCode());

            for (KdniaoTrace trace : kdniaoExpress.getTraces()) {
                DeliveryDetailInfoVO content = new DeliveryDetailInfoVO();
                content.setTime(trace.getAcceptTime());
                content.setDesc(trace.getAcceptStation());
                content.setLocation(trace.getLocation());
                content.setRemark(trace.getRemark());
                result.getContent().add(content);
            }
            return result;
        } catch (Exception e) {
            throw new BusinessException("暂无物流信息");
        }
    }

    @Override
    public void autoRefund(String orderAfterNo, OrderInfo orderInfo) {
        OrderAfterSaleDTO orderAfterSale = orderAfterSaleManager.findByAfterSaleNo(orderAfterNo);
        if (null == orderAfterSale) {
            throw new BusinessException("售后单【" + orderAfterNo + "】不存在");
        }
        if(!orderAfterSale.getAfterSaleStatus().equals(OrderAfterStatusEnum.PENDING.getCode()) && !orderAfterSale.getAfterSaleStatus().equals(OrderAfterStatusEnum.REFUND_FAIL.getCode())) {
            throw new BusinessException("售后单已处理完成，无法退款！");
        }
        if(OrderAfterSaleTypeEnum.REFUND.getCode().equals(orderAfterSale.getAfterSaleType())){
            refundPushAndRefund(orderInfo, orderAfterSale);
        }
    }

    @Override
    public void refundPushAndRefund(OrderInfo orderInfo, OrderAfterSaleDTO orderAfterSale) {
        log.info("refundPushAndRefund .  orderAfterNo:{}, orderNo:{}", orderAfterSale.getOrderAfterNo(), orderAfterSale.getOrderNo());
        OrderAfterSale record = new OrderAfterSale();
        record.setOrderAfterNo(orderAfterSale.getOrderAfterNo());
        // 电子卡、周期购
        if (OrderTypeEnum.CARD_ORDER.getCode().equals(orderInfo.getOrderType())
                && OrderProductTypeEnum.VIRTUAL_CARD.getCode().equals(orderInfo.getProductType())
                || OrderTypeEnum.CYLE_ORDER.getCode().equals(orderInfo.getOrderType())) {
            orderSyncAdapter.refundPush(orderInfo.getOrderNo(), orderAfterSale.getOrderAfterNo());
        // 单品订单、实体卡订单
        } else if (OrderProductTypeEnum.PHYSICAL_CARD.getCode().equals(orderInfo.getProductType())
                || OrderProductTypeEnum.SINGLE_PRODUCT.getCode().equals(orderInfo.getProductType())
                || OrderProductTypeEnum.LOW_PRODUCT.getCode().equals(orderInfo.getProductType())) {
            if (!orderInfo.getOrderStatus().equals(OrderStatusEnum.DELIVERY.getCode())) {
                orderSyncAdapter.refundPush(orderInfo.getOrderNo(), orderAfterSale.getOrderAfterNo());
            }else{
                // 待发货前退款无需推送
                record.setPushFlag(PushFlagEnum.NO_NEED.getCode());
            }
        } else {
            throw new BusinessException("无此类型订单");
        }
        //执行退款
        try{
            orderAfterSaleManager.refund(orderInfo, orderAfterSale);
            record.setAfterSaleStatus(OrderAfterStatusEnum.PROCESSED.getCode());
            record.setPassTime(new Date());
            record.setAuditStatus(OrderAfterAuditStatusEnum.PASS.getCode());
        }catch (Exception e){
            log.error("退款更新状态error", e);
            record.setAfterSaleStatus(OrderAfterStatusEnum.REFUND_FAIL.getCode());
            record.setAuditStatus(OrderAfterAuditStatusEnum.PASS.getCode());
            record.setFailTime(new Date());
        }
        orderAfterSaleManager.update(record);
    }

    @Override
    public void autoRefund(String orderAfterNo) {
        OrderAfterSaleDTO orderAfterSale = orderAfterSaleManager.findByAfterSaleNo(orderAfterNo);
        if(null == orderAfterSale){
            throw new BusinessException("售后单不存在！");
        }
        OrderInfo orderInfo = orderManager.selectLpkByOrderNo(orderAfterSale.getOrderNo());
        if(null == orderInfo){
            throw new BusinessException("订单不存在！");
        }

        //只要售后单内有赠品，无论是否发货，都不自动退款
        List<OrderAfterSaleSku> giftAfterSaleSkus = orderAfterSaleSkuDao.findByOrderAfterNo(orderAfterNo, BasicFlagEnum.YES.getKey());
        if(CollectionUtils.isNotEmpty(giftAfterSaleSkus)){
            return;
        }

        if(OrderProductTypeEnum.SINGLE_PRODUCT.getCode().equals(orderInfo.getProductType())
                || OrderProductTypeEnum.LOW_PRODUCT.getCode().equals(orderInfo.getProductType())
                || OrderProductTypeEnum.PHYSICAL_CARD.getCode().equals(orderInfo.getProductType())
                && OrderStatusEnum.DELIVERY.getCode().equals(orderInfo.getOrderStatus())){

            if(orderSyncAdapter.checkOrderDelivery(orderAfterSale.getSrcNo(), orderAfterSale.getOrderNo())){
                // 订单发货不调用中台取消接口，不进行自动退款
                return;
            }
            // 如果是拼团订单 特殊处理 只有推送oms成功的才需要调oms取消接口
            Integer sourceType = orderInfo.getSourceType();
            String orderNo = orderInfo.getOrderNo();
            if (Objects.nonNull(sourceType) && sourceType.equals(OrderSourceTypeEnum.PIN_TUAN.getCode())) {
                log.info("拼团订单发起自动退款,orderNo:{}", orderNo);
                if (orderInfo.getPushFlag().equals(PushFlagEnum.COMPLETED.getCode())) {
                    log.info("已推送oms拼团订单调用oms取消订单并自动退款，orderNo:{}", orderNo);
                    if(!orderSyncAdapter.orderCancel(orderNo, orderAfterNo, true)){
                        return;
                    }
                }
            } else {
                // 订单取消成功，即可自动退款
                if(!orderSyncAdapter.orderCancel(orderNo, orderAfterNo, true)){
                    return;
                }
            }
        }
        orderAfterSaleManager.autoRefund(orderAfterNo, orderInfo);
    }

    public void setPlanAfterOrder(List<OrderAfterSaleListVO> orderAfterSaleListVOS){
        // key=planOrderNo
        Map<String, MilkDispatchPlan> planMap = new HashMap<>();
        // key=cardNumber value=orderNo
        Map<String, String> orderSkuCardMap = new HashMap<>();
        // key=orderNo
        Map<String, OrderInfo> orderMap = new HashMap<>();

        // 根据配送计划的单号 找到卡号对应的订单信息
        List<String> planOrderNoList = orderAfterSaleListVOS.stream().filter(x-> null == x.getOrderType()).map(x->x.getOrderNo()).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(planOrderNoList)) {
            List<MilkDispatchPlan> list = milkDispatchPlanDao.findByOrders(planOrderNoList);
            planMap = list.stream().collect(Collectors.toMap(x->x.getPlanOrderNo(), x->x));

            List<String> cardNumberList = list.stream().map(x->x.getCardNumber()).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(cardNumberList)) {
                List<OrderSkuCard> orderSkuCardList = orderSkuCardDao.findByCardNumbers(cardNumberList);
                orderSkuCardMap = StreamUtils.toMap(orderSkuCardList, OrderSkuCard::getCardNumber, OrderSkuCard::getOrderNo);

                List<OrderSku> orderSkuList = orderSkuDao.findByCardCodeV2(cardNumberList);

                List<String> orderNoList = orderSkuList.stream().map(x->x.getOrderNo()).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(orderNoList)) {
                    List<OrderInfo> orderList = orderInfoDao.findByOrderNoList(orderNoList);
                    orderMap = orderList.stream().collect(Collectors.toMap(x->x.getOrderNo(), x->x));
                }
            }
        }

        // 如果是配送计划的售后 查询关联卡号对应订单的信息作填充
        for (OrderAfterSaleListVO data : orderAfterSaleListVOS) {
            MilkDispatchPlan plan = planMap.get(data.getOrderNo());
            if (null == plan) {
                continue;
            }
            String orderNo = orderSkuCardMap.get(plan.getCardNumber());
            if (StringUtils.isBlank(orderNo)) {
                continue;
            }
            OrderInfo order = orderMap.get(orderNo);
            if (null != order) {
                data.setOrderStatus(order.getOrderStatus());
                data.setOrderType(order.getOrderType());
                data.setProductType(order.getProductType());
            }
        }
    }

    @Override
    public void returnPlanCount(String planOrderNo){
        // 返回计划提奶提数
        List<MilkDispatchPlan> plans = milkDispatchPlanDao.findByPlanOrderNo(planOrderNo);
        if (CollectionUtils.isNotEmpty(plans)) {
            MilkDispatchPlan plan = plans.get(0);
            MilkDispatchPlan record = new MilkDispatchPlan();
            record.setId(plan.getId());
            record.setPlanStatus(PlanStatusEnum.AFTER_COMPLETED.getCode());
            milkDispatchPlanDao.updateById(record);

            Card card = cardDao.selectByNumber(plan.getCardNumber());

            Card cardRecord = new Card();
            cardRecord.setId(card.getId());
            cardRecord.setRemainingCount(card.getRemainingCount() + plan.getMilkAmount());
            cardRecord.setRemainingAmount(card.getRemainingAmount().add(plan.getAmount()));
            cardDao.update(cardRecord);
        }
    }

    @Override
    public void returnCardPlanCount(Long id) {
        // 返回计划提奶提数
        MilkDispatchPlan plan = milkDispatchPlanDao.findById(id);

        Card card = cardDao.selectByNumber(plan.getCardNumber());
        if(Objects.isNull(card)){
            throw new BusinessException("奶卡不存在！");
        }
        Card cardRecord = new Card();
        cardRecord.setId(card.getId());
        cardRecord.setRemainingCount(card.getRemainingCount() + plan.getMilkAmount());
        cardRecord.setRemainingAmount(card.getRemainingAmount().add(plan.getAmount()));
        cardDao.update(cardRecord);
    }

    @Override
    public void exportOrderAfter(OrderAfterExportDTO dto) {
        OrderAfterSaleListDTO afterSaleListDTO = BeanUtils.deepCopy(dto, OrderAfterSaleListDTO.class);
        afterSaleListDTO.setPageNum(1);
        afterSaleListDTO.setPageSize(Integer.MAX_VALUE);
        PageVO<OrderAfterSaleListVO> orderAfterPage = this.findOrderAfterSaleList(afterSaleListDTO);
        List<OrderAfterSaleListVO> orderAfterList = orderAfterPage.getList();

        DataExportTask dataExportTask = new DataExportTask();
        dataExportTask.setId(dto.getId());
        try {
            List<OrderAfterExcelVO> excelVOS = OrderAfterSaleUtil.convert2ExcelList(orderAfterList);
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();

            Integer mergeRowIndex = 1;
            List<Integer> mergeColumnRegion = Arrays.asList(0, 1, 7, 8, 9, 10, 11);
            EasyExcel.write(outputStream, OrderAfterExcelVO.class)
                    .autoCloseStream(Boolean.TRUE)
                    .registerWriteHandler(new ExcelFillCellMergeStrategy(mergeRowIndex, mergeColumnRegion))
                    .sheet("商城售后单").doWrite(excelVOS);
            FileExportCenterEnum enumByCode = FileExportCenterEnum.getEnum(dto.getExportType());
            String fileName = dto.getFileName() + enumByCode.getSubfix();
            String url = aliyunOssUtils.uploadFile(new ByteArrayInputStream(outputStream.toByteArray()),
                    fileName);
            dataExportTask.setFileUrl(url);
            dataExportTask.setStatus(DataExportStatusEnum.FINISH.getKey());
        } catch (RuntimeException e) {
            dataExportTask.setStatus(DataExportStatusEnum.FAILURE.getKey());
        }
        dataExportTaskDao.updateByPrimaryKey(dataExportTask);
    }

    @Override
    public List<OrderGiftCouponVO> buildGiftCouponVO(String fullReduceRecordId) {
        List<OrderGiftCouponVO> giftCouponList = new ArrayList<>();
        if(StringUtils.isBlank(fullReduceRecordId)){
            return giftCouponList;
        }
        List<Long> ids = Arrays.stream(fullReduceRecordId.split(","))
                .map(Long::valueOf)
                .collect(Collectors.toList());
        List<FullReduceRecord> fullReduceRecordList = fullReduceRecordDao.findByIds(ids);
        List<FullReduceRecord> couponRecordList = StreamUtils.filter(fullReduceRecordList, x -> FullRuleRangeTypeEnum.COUNPON.getCode().equals(x.getType()));
        FullReduceRecord couponRecord = StreamUtils.getFirst(couponRecordList);
        if(null == couponRecord){
            return giftCouponList;
        }
        CouponRule couponRule = couponRuleManager.findById(couponRecord.getTargetId());
        if(null == couponRule){
            return giftCouponList;
        }
        OrderGiftCouponVO giftCouponVO = new OrderGiftCouponVO();
        giftCouponVO.setCount(couponRecord.getCount());
        giftCouponVO.setCouponWay(couponRule.getCouponWay());
        giftCouponVO.setAmountReduce(couponRule.getAmountReduce());
        giftCouponVO.setDiscount(couponRule.getDiscount());
        giftCouponVO.setCouponName(couponRule.getCouponName());
        giftCouponVO.setCouponDesc();
        giftCouponList.add(giftCouponVO);
        return giftCouponList;
    }
}