package com.hengtiansoft.order.manager.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.hengtiansoft.common.constant.CardConstant;
import com.hengtiansoft.common.entity.dto.Pagination;
import com.hengtiansoft.common.entity.exception.BusinessException;
import com.hengtiansoft.common.entity.vo.PageVO;
import com.hengtiansoft.common.enumeration.BasicFlagEnum;
import com.hengtiansoft.common.enumeration.FlagEnum;
import com.hengtiansoft.common.redis.RedisOperation;
import com.hengtiansoft.common.util.BeanUtils;
import com.hengtiansoft.common.util.DateUtil;
import com.hengtiansoft.common.util.PageUtils;
import com.hengtiansoft.common.util.StreamUtils;
import com.hengtiansoft.item.dao.CardAmountChangeDetailDao;
import com.hengtiansoft.item.dao.CardCategoryDao;
import com.hengtiansoft.item.dao.CardDao;
import com.hengtiansoft.item.entity.dto.*;
import com.hengtiansoft.item.entity.po.*;
import com.hengtiansoft.item.entity.vo.*;
import com.hengtiansoft.item.enumeration.*;
import com.hengtiansoft.item.interfaces.ProductManager;
import com.hengtiansoft.item.manager.CardShareManager;
import com.hengtiansoft.item.utils.SkuUtil;
import com.hengtiansoft.order.dao.*;
import com.hengtiansoft.order.entity.dto.*;
import com.hengtiansoft.order.entity.mapper.OrderInfoMapper;
import com.hengtiansoft.order.entity.po.*;
import com.hengtiansoft.order.entity.vo.*;
import com.hengtiansoft.order.enums.*;
import com.hengtiansoft.order.manager.*;
import com.hengtiansoft.order.util.GenNoUtil;
import com.hengtiansoft.order.util.OrderInfoUtil;
import com.hengtiansoft.privilege.dao.FreeTrialSubActivityRecordDao;
import com.hengtiansoft.privilege.entity.dto.FreeTrialUserDTO;
import com.hengtiansoft.privilege.entity.dto.PayUserQueryDTO;
import com.hengtiansoft.privilege.entity.po.FreeTrialSubActivityRecord;
import com.hengtiansoft.privilege.entity.po.FreeTrialUser;
import com.hengtiansoft.privilege.entity.vo.FreeTrialSubCntVO;
import com.hengtiansoft.privilege.enums.FreeCompeteReviewEnum;
import com.hengtiansoft.privilege.manager.FreeTrialUserManager;
import com.hengtiansoft.thirdpart.config.SystemConfig;
import com.hengtiansoft.thirdpart.config.WxMiniConfig;
import com.hengtiansoft.thirdpart.entity.dto.oms.OrderWriteOffDetailOmsDTO;
import com.hengtiansoft.thirdpart.entity.dto.oms.OrderWriteOffOmsCreateDTO;
import com.hengtiansoft.thirdpart.entity.dto.youshu.YoushuOrderAddDetailReq;
import com.hengtiansoft.thirdpart.interfaces.DataFinderManager;
import com.hengtiansoft.thirdpart.interfaces.MotManager;
import com.hengtiansoft.thirdpart.interfaces.NascentCustomerManager;
import com.hengtiansoft.thirdpart.util.OmsUtil;
import com.hengtiansoft.thirdpart.util.YoushuUtil;
import com.hengtiansoft.user.dao.CustomerUserDao;
import com.hengtiansoft.user.dao.ShopParamDao;
import com.hengtiansoft.user.dao.WorkWxExternalContactDao;
import com.hengtiansoft.user.entity.po.CustomerUser;
import com.hengtiansoft.user.entity.po.ShopParam;
import com.hengtiansoft.user.entity.po.WorkWxExternalContact;
import com.hengtiansoft.user.entity.vo.ShopParamVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.github.pagehelper.page.PageMethod.startPage;

/**
 * @Author: <EMAIL>
 */
@Service
@Slf4j
public class OrderManagerImpl implements OrderManager {

    @Value("${divisionDate:2022-02-20}")
    private String divisionDate;
    @Resource
    private CardDao cardDao;
    @Resource
    private OrderInfoDao orderInfoDao;
    @Resource
    private OrderSkuDao orderSkuDao;
    @Resource
    private OrderPackageDao orderPackageDao;
    @Resource
    private OrderPackageSkuDao orderPackageSkuDao;
    @Resource
    private CardShareManager cardShareManager;
    @Resource
    private CustomerUserDao customerUserDao;
    @Resource
    private MilkDispatchPlanDao milkDispatchPlanDao;
    @Resource
    private ShopParamDao shopParamDao;
    @Resource
    private CardCategoryDao cardCategoryDao;
    @Resource
    private ShopInvoiceDao shopInvoiceDao;
    @Resource
    private CardAmountChangeDetailDao cardAmountChangeDetailDao;
    @Resource
    private ProductManager productManager;
    @Resource
    private CouponRuleManager couponRuleManager;
    @Resource
    private OrderAddressDao orderAddressDao;
    @Resource
    private CouponGiftRangeManager couponGiftRangeManager;
    @Resource
    private OrderGiftDao orderGiftDao;
    @Resource
    private OrderAddressManager orderAddressManager;
    @Resource
    private CouponInfoDao couponInfoDao;
    @Autowired
    private DataSourceTransactionManager dstManager;
    @Autowired
    private OrderSkuManager orderSkuManager;
    @Autowired
    private RedisOperation redisOperation;
    @Autowired
    private NascentCustomerManager nascentCustomerManager;
    @Autowired
    private OrderCycleManager orderCycleManager;
    @Autowired
    private OmsUtil omsUtil;
    @Autowired
    private SystemConfig systemConfig;
    @Resource
    private YoushuUtil youshuUtil;
    @Resource
    private OrderAfterSaleDao orderAfterSaleDao;
    @Resource
    private OrderAfterSaleSkuDao orderAfterSaleSkuDao;
    @Resource
    private WxMiniConfig wxMiniConfig;
    @Resource
    private OrderSkuCardDao orderSkuCardDao;
    @Resource
    private CouponRuleDao couponRuleDao;
    @Resource
    private DataFinderManager dataFinderManager;
    @Resource
    private FreeTrialUserManager freeTrialUserManager;
    @Resource
    private OrderPayDao orderPayDao;
    @Resource
    private OrderSkuSenceDao orderSkuSenceDao;
    @Resource
    private OrderInfoMapper orderInfoMapper;
    @Resource
    private CardSummaryFormDao cardSummaryFormDao;
    @Resource
    private FreeTrialSubActivityRecordDao freeTrialSubActivityRecordDao;
    @Resource
    private GenNoUtil genNoUtil;
    @Resource
    private MotManager motManager;
    @Resource
    private WorkWxExternalContactDao workWxExternalContactDao;


    /**
     * 电子卡订单列表
     *
     * @param dto
     * @return
     */
    @Override
    public PageVO<OrderInfoVO> eCardList(OrderSearchDTO dto) {
        startPage(dto.getPageNum(), dto.getPageSize());
        List<OrderInfoVO> orderInfoVOS = orderInfoDao.findECard(dto);
        return PageUtils.convert(orderInfoVOS, data -> {
            OrderInfoVO orderInfoVO = new OrderInfoVO();
            BeanUtils.copy(data, orderInfoVO);
            if (Objects.nonNull(data.getCardCode()) && !"".equals(data.getCardCode())) {
                Card card = cardDao.selectByNumber(data.getCardCode());
                if (Objects.nonNull(card)) {
                    CardShareDTO cardShareDTO = new CardShareDTO();
                    cardShareDTO.setCardId(card.getId());
                    cardShareDTO.setStatus(CardShareStatusEnum.SHARING.getCode());
                    List<CardShare> cardShares = cardShareManager.findByCondition(cardShareDTO);
                    orderInfoVO.setCardId(card.getId());
                    orderInfoVO.setCurrentUserPossess(Objects.equals(dto.getUserId(), card.getUserId()) ? 0 : 1);
                    orderInfoVO.setCardShareStatus(CollectionUtils.isEmpty(cardShares) ? 0 : 1);
                }
            }
            orderInfoVO.setCreateTimeStamp(data.getCreateTime().getTime());
            return orderInfoVO;
        });

    }

    /**
     * 奶卡订单列表
     *
     * @param dto
     * @return
     */
    @Override
    public PageVO<OrderCardListVO> cardList(OrderCardListDTO dto) {
        return null;
    }

    /**
     * 财务报表订单
     *
     * @param dto
     * @return
     */
    @Override
    public PageVO<ReportFormListVO> reportFormBackList(ReportFormListDTO dto) {
        if(Objects.isNull(dto.getBelongToBU())){
            throw new BusinessException("请选择事业部归属！");
        }
        // 校验备份表是否存在
        this.verifyTable(dto);
        // 店铺事业部归属
        if(Objects.nonNull(dto.getBelongToBU())){
            List<String> shopNoList = shopParamDao.findByBelongToBU(dto.getBelongToBU());
            if(CollectionUtils.isNotEmpty(dto.getShopNoList())){
                dto.getShopNoList().retainAll(shopNoList);
            }else{
                dto.setShopNoList(shopNoList);
            }
            if(CollectionUtils.isEmpty(dto.getShopNoList())){
                return PageUtils.emptyPage();
            }
        }
        // 主表查询
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        List<OrderSku> skuList = orderSkuDao.reportFormBackup(dto);
        if (CollectionUtils.isEmpty(skuList)) {
            return PageUtils.emptyPage();
        }
        // 结果封装
        PageVO<ReportFormListVO> result = PageUtils.convert(skuList, data -> BeanUtils.copy(data, ReportFormListVO::new));
        List<ReportFormListVO> dataList = result.getList();
        List<List<ReportFormListVO>> splitList = StreamUtils.split(dataList, CardConstant.LIMIT_LIST_LENGTH);
        List<ReportFormListVO> resultList = new ArrayList<>();
        for (List<ReportFormListVO> tempList : splitList) {

            List<String> orderNoList = StreamUtils.convertDistinct(tempList, ReportFormListVO::getOrderNo, String::compareTo);
            List<OrderInfo> orderList = orderInfoDao.selectByOrderNosInBackup(dto.getDate(), orderNoList);
            Map<String, OrderInfo> orderInfoMap = StreamUtils.toMap(orderList, OrderInfo::getOrderNo);

            List<OrderPackage> packageList = orderPackageDao.selectByOrderNosInBackup(StreamUtils.toList(orderList, OrderInfo::getOrderNo), dto.getDate());
            Map<String, String> packageMap = StreamUtils.toMap(packageList, OrderPackage::getPackageNo, OrderPackage::getTrackingNo);
            List<OrderPackageSku> packageSkus = orderPackageSkuDao.selectByOrderNosInBackup(orderNoList, dto.getDate());
            Map<String, List<String>> packageSkuMap = StreamUtils.group(packageSkus,
                    x -> x.getOrderNo() + x.getSkuCode(), OrderPackageSku::getPackageNo);
            List<String> cardNumberList = StreamUtils.convertFilter(tempList, ReportFormListVO::getCardCode, StringUtils::isNotBlank);
            List<Card> cardList = cardDao.selectByCardNumbersInBackup(dto.getDate(), cardNumberList, DateUtil.stringToDate(dto.getDate(), DateUtil.SIMPLE_DATE_YMD));
            Map<String, Card> cardMap = StreamUtils.toMap(cardList, card -> card.getCardNumber().toUpperCase());

            List<MilkAmountVO> milkAmountVOList = milkDispatchPlanDao.selectMilkAmountsAndAmountsBackup(dto.getDate(), cardNumberList);
            Map<String, Integer> milkAmountMap = StreamUtils.toMap(milkAmountVOList, x -> x.getCardNumber().toUpperCase(), MilkAmountVO::getMilkAmount);
            Map<String, BigDecimal> amountMap = StreamUtils.toMap(milkAmountVOList, x -> x.getCardNumber().toUpperCase(), MilkAmountVO::getAmount);


            List<MilkAmountVO> noIssuedMilkAmountVOList = milkDispatchPlanDao.selectNoIssuedMilkAmountsAndAmountsBackup(dto.getDate(), cardNumberList);
            Map<String, Integer> noIssuedCountMap = StreamUtils.toMap(noIssuedMilkAmountVOList, x -> x.getCardNumber().toUpperCase(), MilkAmountVO::getMilkAmount);
            Map<String, BigDecimal> noIssuedAmountMap = StreamUtils.toMap(noIssuedMilkAmountVOList, x -> x.getCardNumber().toUpperCase(), MilkAmountVO::getAmount);

            List<CardAmountChangeDetail> changeDetailList = cardAmountChangeDetailDao.selectByCardNumbers(DateUtil.dateToString(new Date(), DateUtil.SIMPLE_YMD), cardNumberList);
            Map<String, CardAmountChangeDetail> changeDetailMap = StreamUtils.toMap(changeDetailList, changeDetail -> changeDetail.getCardNumber().toUpperCase());

            List<CardAmountChangeDetail> changeDetailReList = cardAmountChangeDetailDao.selectReconciliationByCardNumbers(DateUtil.dateToString(new Date(), DateUtil.SIMPLE_YMD), cardNumberList);
            Map<String, CardAmountChangeDetail> changeDetailReMap = StreamUtils.toMap(changeDetailReList, changeDetailRe -> changeDetailRe.getCardNumber().toUpperCase());
            
            for (ReportFormListVO data : tempList) {
                List<String> packageNoList = packageSkuMap.get(data.getOrderNo() + data.getSkuCode());
                if(Objects.isNull(data.getCardCode())){
                    data.setCardCode(Strings.EMPTY);
                }
                Card card = cardMap.get(data.getCardCode().toUpperCase());

                Integer milkAmount = milkAmountMap.get(data.getCardCode().toUpperCase());
                BigDecimal amount = amountMap.get(data.getCardCode().toUpperCase());

                Integer noIssuedCount = noIssuedCountMap.get(data.getCardCode().toUpperCase());
                BigDecimal noIssuedAmount = noIssuedAmountMap.get(data.getCardCode().toUpperCase());

                CardAmountChangeDetail changeDetail = changeDetailMap.get(data.getCardCode().toUpperCase());
                CardAmountChangeDetail changeDetailRe = changeDetailReMap.get(data.getCardCode().toUpperCase());
                
                if (Objects.nonNull(card)) {
                    data.setActivateTime(card.getActivateTime());
                    data.setRemainingCount(card.getRemainingCount());
                    data.setRemainingAmount(card.getRemainingAmount());
                    data.setCardCount(card.getCardCount());
                    data.setCardAmount(card.getCardAmount());
                    data.setUsageStatus(card.getUsageStatus());
                    data.setCardStatus(card.getCardStatus());
                    data.setShopNo(card.getShopNo());
                    data.setShopName(card.getShopName());
                    data.setInvoiceType(card.getInvoiceType());
                    data.setInvoiceTime(DateUtil.dateToString(card.getInvoiceTime(), DateUtil.SIMPLE_YMD));
                    if (InvoiceTypeEnum.RAISING.getCode().equals(card.getInvoiceType())) {
                        data.setInvoiceTypeString(InvoiceTypeEnum.RAISING.getDesc());
                    } else if (InvoiceTypeEnum.PREPAID.getCode().equals(card.getInvoiceType())) {
                        data.setInvoiceTypeString(InvoiceTypeEnum.PREPAID.getDesc());
                    }
                    data.setUsageStatusDesc(CardUsageStatusEnum.getDescByCode(card.getUsageStatus()));
                    data.setCardStatusDesc(CardStatusEnum.getEnumByCode(card.getCardStatus()).getDesc());

                    data.setMilkAmount(Objects.isNull(milkAmount) ? 0 : milkAmount);
                    data.setAmountWithdrawn(Objects.isNull(amount) ? BigDecimal.ZERO : amount);

                    data.setPushNoIssuedCount(Objects.isNull(noIssuedCount) ? 0 : noIssuedCount);
                    data.setPushNoIssuedAmount(Objects.isNull(noIssuedAmount) ? BigDecimal.ZERO : noIssuedAmount);


                    if (changeDetailRe != null) {
                        data.setReconciliationTime(changeDetailRe.getReconciliationTime());
                        data.setReconciliationAmount(changeDetailRe.getReconciliationAmount());
                        data.setRealRemainingAmount(changeDetailRe.getReconciliationAmount().subtract(data.getAmountWithdrawn()));
                        if (changeDetail != null) {
                            data.setChangedTime(changeDetail.getChangedTime());
                            data.setChangedAmount(changeDetail.getChangedAmount());
                        }
                    } else if (changeDetail != null) {
                        data.setChangedAmount(changeDetail.getChangedAmount());
                        data.setChangedTime(changeDetail.getChangedTime());
                        // 设置实际剩余金额
                        data.setRealRemainingAmount(changeDetail.getChangedAmount().subtract(data.getAmountWithdrawn()));
                    } else {
                        data.setRealRemainingAmount(card.getCardAmount().subtract(data.getAmountWithdrawn()));
                    }
                    // 设置实际剩余提数
                    data.setRealRemainingCount(data.getCardCount() - data.getMilkAmount());
                }
                OrderInfo orderInfo = orderInfoMap.get(data.getOrderNo());
                if (Objects.nonNull(orderInfo)) {
                    data.setSrcNo(orderInfo.getSrcNo());
                    data.setChannelCode(orderInfo.getChannel());
                    data.setDeliveryTime(orderInfo.getDeliveryTime());
                    if (Objects.isNull(orderInfo.getDeliveryTime()) && Objects.nonNull(orderInfo.getProductType()) && orderInfo.getProductType().equals(OrderProductTypeEnum.VIRTUAL_CARD.getCode()) && Objects.nonNull(orderInfo.getPayTime())) {
                        data.setDeliveryTime(orderInfo.getPayTime());
                    }
                    data.setDeliveryTimeString(DateUtil.dateToString(data.getDeliveryTime(), DateUtil.SIMPLE_FMT));
                }
                data.setCreateTimeString(DateUtil.dateToString(data.getCreateTime(), DateUtil.SIMPLE_FMT));
                data.setActivateTimeString(DateUtil.dateToString(data.getActivateTime(), DateUtil.SIMPLE_YMD));
                if (CollectionUtils.isNotEmpty(packageNoList)) {
                    data.setTrackingNoList(packageNoList.stream().map(packageMap::get).collect(Collectors.toList()));
                }
            }
            resultList.addAll(tempList);
        }
        result.setList(resultList);
        return result;

    }

    private void verifyTable(ReportFormListDTO dto) {
        if (StringUtils.isNotBlank(dto.getDate())) {
            Date currentTime = DateUtil.stringToDate(dto.getDate(), DateUtil.SIMPLE_YMD);
            Calendar currentCalendar = Calendar.getInstance();
            currentCalendar.setTime(currentTime);
            currentCalendar.add(Calendar.DATE, 1);
            String date = DateUtil.dateToString(currentCalendar.getTime(), DateUtil.SIMPLE_DATE_YMD);

            List<String> tableNames = orderSkuDao.showAllTables();
            List<String> resultTab = new ArrayList<>();
            tableNames.forEach(name -> {
                String[] strings = StringUtils.split(name, "_");
                resultTab.add(strings[strings.length - 1]);
            });
            if (!resultTab.contains(date)) {
                throw new BusinessException("备份表中不包含该日期！");
            }
            dto.setDate(date);
        } else {
            throw new BusinessException("请选择账单日期！");
        }
    }

    @Override
    public PageVO<ReportFormListVO> reportFormZipperList(ReportFormListDTO dto) {
        if(Objects.isNull(dto.getBelongToBU())){
            throw new BusinessException("请选择事业部归属！");
        }
        if (StringUtils.isBlank(dto.getDate())) {
            throw new BusinessException("请选择账单日期！");
        }else{
            Date currentTime = DateUtil.stringToDate(dto.getDate(), DateUtil.SIMPLE_YMD);
            Calendar currentCalendar = Calendar.getInstance();
            currentCalendar.setTime(currentTime);
            currentCalendar.add(Calendar.DATE, 1);
            dto.setDate(DateUtil.dateToString(currentCalendar.getTime(), DateUtil.SIMPLE_YMD));
        }
        // 店铺事业部归属
        if(Objects.nonNull(dto.getBelongToBU())){
            List<String> shopNoList = shopParamDao.findByBelongToBU(dto.getBelongToBU());
            if(CollectionUtils.isNotEmpty(dto.getShopNoList())){
                dto.getShopNoList().retainAll(shopNoList);
            }else{
                dto.setShopNoList(shopNoList);
            }
            if(CollectionUtils.isEmpty(dto.getShopNoList())){
                return PageUtils.emptyPage();
            }
        }
        // 主表查询
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        List<OrderSku> skuList = orderSkuDao.reportFormZipper(dto);
        if (CollectionUtils.isEmpty(skuList)) {
            return PageUtils.emptyPage();
        }
        // 结果封装
        PageVO<ReportFormListVO> result = PageUtils.convert(skuList, data -> BeanUtils.copy(data, ReportFormListVO::new));
        List<ReportFormListVO> dataList = result.getList();
        List<List<ReportFormListVO>> splitList = StreamUtils.split(dataList, CardConstant.LIMIT_LIST_LENGTH);
        List<ReportFormListVO> resultList = new ArrayList<>();
        for (List<ReportFormListVO> tempList : splitList) {

            List<String> orderNoList = StreamUtils.convertDistinct(tempList, ReportFormListVO::getOrderNo, String::compareTo);
            List<OrderInfo> orderList = orderInfoDao.selectByOrderNosInZipper(dto.getDate(), orderNoList);
            Map<String, OrderInfo> orderInfoMap = StreamUtils.toMap(orderList, OrderInfo::getOrderNo);

            List<OrderPackage> packageList = orderPackageDao.selectByOrderNosInZipper(StreamUtils.toList(orderList, OrderInfo::getOrderNo), dto.getDate());
            Map<String, String> packageMap = StreamUtils.toMap(packageList, OrderPackage::getPackageNo, OrderPackage::getTrackingNo);
            List<OrderPackageSku> packageSkus = orderPackageSkuDao.selectByOrderNosInZipper(orderNoList, dto.getDate());
            Map<String, List<String>> packageSkuMap = StreamUtils.group(packageSkus,
                    x -> x.getOrderNo() + x.getSkuCode(), OrderPackageSku::getPackageNo);
            List<String> cardNumberList = StreamUtils.toList(tempList, ReportFormListVO::getCardCode);
            List<Card> cardList = cardDao.selectByCardNumbersInZipper(dto.getDate(), StreamUtils.toList(tempList, ReportFormListVO::getCardCode));
            Map<String, Card> cardMap = StreamUtils.toMap(cardList, card -> card.getCardNumber().toUpperCase());

            List<MilkAmountVO> milkAmountVOList = milkDispatchPlanDao.selectMilkAmountsAndAmountsZipper(dto.getDate(), cardNumberList);
            Map<String, Integer> milkAmountMap = StreamUtils.toMap(milkAmountVOList, x -> x.getCardNumber().toUpperCase(), MilkAmountVO::getMilkAmount);
            Map<String, BigDecimal> amountMap = StreamUtils.toMap(milkAmountVOList, x -> x.getCardNumber().toUpperCase(), MilkAmountVO::getAmount);


            List<MilkAmountVO> noIssuedMilkAmountVOList = milkDispatchPlanDao.selectNoIssuedMilkAmountsAndAmountsZipper(dto.getDate(), cardNumberList);
            Map<String, Integer> noIssuedCountMap = StreamUtils.toMap(noIssuedMilkAmountVOList, x -> x.getCardNumber().toUpperCase(), MilkAmountVO::getMilkAmount);
            Map<String, BigDecimal> noIssuedAmountMap = StreamUtils.toMap(noIssuedMilkAmountVOList, x -> x.getCardNumber().toUpperCase(), MilkAmountVO::getAmount);

            List<CardAmountChangeDetail> changeDetailList = cardAmountChangeDetailDao.selectByCardNumbers(DateUtil.dateToString(new Date(), DateUtil.SIMPLE_YMD), cardNumberList);
            Map<String, CardAmountChangeDetail> changeDetailMap = StreamUtils.toMap(changeDetailList, changeDetail -> changeDetail.getCardNumber().toUpperCase());

            List<CardAmountChangeDetail> changeDetailReList = cardAmountChangeDetailDao.selectReconciliationByCardNumbers(DateUtil.dateToString(new Date(), DateUtil.SIMPLE_YMD), cardNumberList);
            Map<String, CardAmountChangeDetail> changeDetailReMap = StreamUtils.toMap(changeDetailReList, changeDetailRe -> changeDetailRe.getCardNumber().toUpperCase());

            for (ReportFormListVO data : tempList) {
                List<String> packageNoList = packageSkuMap.get(data.getOrderNo() + data.getSkuCode());
                if(Objects.isNull(data.getCardCode())){
                    data.setCardCode(Strings.EMPTY);
                }
                Card card = cardMap.get(data.getCardCode().toUpperCase());
                Integer milkAmount = milkAmountMap.get(data.getCardCode().toUpperCase());
                BigDecimal amount = amountMap.get(data.getCardCode().toUpperCase());

                Integer noIssuedCount = noIssuedCountMap.get(data.getCardCode().toUpperCase());
                BigDecimal noIssuedAmount = noIssuedAmountMap.get(data.getCardCode().toUpperCase());

                CardAmountChangeDetail changeDetail = changeDetailMap.get(data.getCardCode().toUpperCase());
                CardAmountChangeDetail changeDetailRe = changeDetailReMap.get(data.getCardCode().toUpperCase());

                if (Objects.nonNull(card)) {
                    data.setActivateTime(card.getActivateTime());
                    data.setRemainingCount(card.getRemainingCount());
                    data.setRemainingAmount(card.getRemainingAmount());
                    data.setCardCount(card.getCardCount());
                    data.setCardAmount(card.getCardAmount());
                    data.setUsageStatus(card.getUsageStatus());
                    data.setCardStatus(card.getCardStatus());
                    data.setShopNo(card.getShopNo());
                    data.setShopName(card.getShopName());
                    data.setInvoiceType(card.getInvoiceType());
                    data.setInvoiceTime(DateUtil.dateToString(card.getInvoiceTime(), DateUtil.SIMPLE_YMD));
                    if (card.getInvoiceType() != null) {
                        if (card.getInvoiceType().equals(InvoiceTypeEnum.RAISING.getCode())) {
                            data.setInvoiceTypeString(InvoiceTypeEnum.RAISING.getDesc());
                        } else if (card.getInvoiceType().equals(InvoiceTypeEnum.PREPAID.getCode())) {
                            data.setInvoiceTypeString(InvoiceTypeEnum.PREPAID.getDesc());
                        }
                    }
                    data.setUsageStatusDesc(CardUsageStatusEnum.getDescByCode(card.getUsageStatus()));
                    data.setCardStatusDesc(CardStatusEnum.getEnumByCode(card.getCardStatus()).getDesc());
                    
                    data.setMilkAmount(Objects.isNull(milkAmount) ? 0 : milkAmount);
                    data.setAmountWithdrawn(Objects.isNull(amount) ? BigDecimal.ZERO : amount);
                    data.setPushNoIssuedCount(Objects.isNull(noIssuedCount) ? 0 : noIssuedCount);
                    data.setPushNoIssuedAmount(Objects.isNull(noIssuedAmount) ? BigDecimal.ZERO : noIssuedAmount);


                    if (changeDetailRe != null) {
                        data.setReconciliationTime(changeDetailRe.getReconciliationTime());
                        data.setReconciliationAmount(changeDetailRe.getReconciliationAmount());
                        data.setRealRemainingAmount(changeDetailRe.getReconciliationAmount().subtract(data.getAmountWithdrawn()));
                        if (changeDetail != null) {
                            data.setChangedTime(changeDetail.getChangedTime());
                            data.setChangedAmount(changeDetail.getChangedAmount());
                        }
                    } else if (changeDetail != null) {
                        data.setChangedAmount(changeDetail.getChangedAmount());
                        data.setChangedTime(changeDetail.getChangedTime());
                        // 设置实际剩余金额
                        data.setRealRemainingAmount(changeDetail.getChangedAmount().subtract(data.getAmountWithdrawn()));
                    } else {
                        data.setRealRemainingAmount(card.getCardAmount().subtract(data.getAmountWithdrawn()));
                    }
                    // 设置实际剩余提数
                    data.setRealRemainingCount(data.getCardCount() - data.getMilkAmount());
                }
                OrderInfo orderInfo = orderInfoMap.get(data.getOrderNo());
                if (Objects.nonNull(orderInfo)) {
                    data.setSrcNo(orderInfo.getSrcNo());
                    data.setChannelCode(orderInfo.getChannel());
                    data.setDeliveryTime(orderInfo.getDeliveryTime());
                    if (Objects.isNull(orderInfo.getDeliveryTime()) && Objects.nonNull(orderInfo.getProductType()) && orderInfo.getProductType().equals(OrderProductTypeEnum.VIRTUAL_CARD.getCode()) && Objects.nonNull(orderInfo.getPayTime())) {
                        data.setDeliveryTime(orderInfo.getPayTime());
                    }
                    data.setDeliveryTimeString(DateUtil.dateToString(data.getDeliveryTime(), DateUtil.SIMPLE_FMT));
                }
                data.setCreateTimeString(DateUtil.dateToString(data.getCreateTime(), DateUtil.SIMPLE_FMT));
                data.setActivateTimeString(DateUtil.dateToString(data.getActivateTime(), DateUtil.SIMPLE_YMD));
                if (CollectionUtils.isNotEmpty(packageNoList)) {
                    data.setTrackingNoList(packageNoList.stream().map(packageMap::get).collect(Collectors.toList()));
                    data.setTrackingNos(StringUtils.join(packageNoList.stream().map(packageMap::get).collect(Collectors.toList()), CardConstant.SEMICOLON));
                }
            }
            resultList.addAll(tempList);
        }
        result.setList(resultList);
        return result;
    }

    @Override
    public PageVO<ReportFormListVO> reportDynamicFormList(ReportFormListDTO dto) {
        if(Objects.isNull(dto.getBelongToBU())){
            throw new BusinessException("请选择事业部归属！");
        }
        if(Objects.isNull(dto.getCreateStartTime()) || Objects.isNull(dto.getCreateEndTime())){
            throw new BusinessException("请选择下单时间！");
        }
        if(Objects.nonNull(dto.getBelongToBU())){
            List<String> shopNoList = shopParamDao.findByBelongToBU(dto.getBelongToBU());
            if(CollectionUtils.isNotEmpty(dto.getShopNoList())){
                dto.getShopNoList().retainAll(shopNoList);
            }else{
                dto.setShopNoList(shopNoList);
            }
            if(CollectionUtils.isEmpty(dto.getShopNoList())){
                return PageUtils.emptyPage();
            }
        }
        // 主表查询
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        List<OrderSku> skuList = new ArrayList<>();
        if (dto.getOverFlag()) {
            skuList = orderSkuDao.reportOverForm(dto);
        } else {
            skuList = orderSkuDao.reportForm(dto);
        }
        if (CollectionUtils.isEmpty(skuList)) {
            return PageUtils.emptyPage();
        }
        // 结果封装
        PageVO<ReportFormListVO> result = PageUtils.convert(skuList, data -> BeanUtils.copy(data, ReportFormListVO::new));
        List<ReportFormListVO> dataList = result.getList();
        List<List<ReportFormListVO>> splitList = StreamUtils.split(dataList, CardConstant.LIMIT_LIST_LENGTH);
        List<ReportFormListVO> resultList = new ArrayList<>();
        for (List<ReportFormListVO> tempList : splitList) {

            List<String> orderNoList = StreamUtils.convertDistinct(tempList, ReportFormListVO::getOrderNo, String::compareTo);
            List<OrderInfo> orderList = orderInfoDao.findByOrderNoList(orderNoList);
            Map<String, OrderInfo> orderInfoMap = StreamUtils.toMap(orderList, OrderInfo::getOrderNo);

            List<OrderPackage> packageList = orderPackageDao.findByOrderNoList(StreamUtils.toList(orderList, OrderInfo::getOrderNo));
            Map<String, String> packageMap = StreamUtils.toMap(packageList, OrderPackage::getPackageNo, OrderPackage::getTrackingNo);
            List<OrderPackageSku> packageSkus = orderPackageSkuDao.selectByOrderNoList(orderNoList);
            Map<String, List<String>> packageSkuMap = StreamUtils.group(packageSkus,
                    x -> x.getOrderNo() + x.getSkuCode(), OrderPackageSku::getPackageNo);

            List<String> cardCodeList = StreamUtils.convertFilter(tempList, ReportFormListVO::getCardCode, StringUtils::isNotBlank);
            List<Card> cardList = cardDao.selectByNumberList(cardCodeList);
            Map<String, Card> cardMap = StreamUtils.toMap(cardList, card -> card.getCardNumber().toUpperCase());

            List<MilkAmountVO> milkAmountAndAmountList = milkDispatchPlanDao.selectMilkAmountsAndAmounts(cardCodeList);
            Map<String, Integer> milkAmountMap = StreamUtils.toMap(milkAmountAndAmountList, x -> x.getCardNumber().toUpperCase(), MilkAmountVO::getMilkAmount);
            Map<String, BigDecimal> amountMap = StreamUtils.toMap(milkAmountAndAmountList, x -> x.getCardNumber().toUpperCase(), MilkAmountVO::getAmount);

            List<MilkAmountVO> noIssuedMilkAmountAndAmountList = milkDispatchPlanDao.selectNoIssuedMilkAmountsAndAmounts(cardCodeList);
            Map<String, Integer> noIssuedCountMap = StreamUtils.toMap(noIssuedMilkAmountAndAmountList, x -> x.getCardNumber().toUpperCase(), MilkAmountVO::getMilkAmount);
            Map<String, BigDecimal> noIssuedAmountMap = StreamUtils.toMap(noIssuedMilkAmountAndAmountList, x -> x.getCardNumber().toUpperCase(), MilkAmountVO::getAmount);

            List<CardCategory> cardCategoryList = new ArrayList<>();
            Map<Long, CardCategory> cardCategoryMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(cardList)) {
                cardCategoryList = cardCategoryDao.selectByPrimaryKey(StreamUtils.convertDistinct(cardList, Card::getCategoryId, Long::compareTo));
                cardCategoryMap = StreamUtils.toMap(cardCategoryList, cardCategory -> cardCategory.getId());
            }

            List<CardAmountChangeDetail> changeDetailList = cardAmountChangeDetailDao.selectByCardNumbers(DateUtil.dateToString(new Date(), DateUtil.SIMPLE_YMD), cardCodeList);
            Map<String, CardAmountChangeDetail> changeDetailMap = StreamUtils.toMap(changeDetailList, changeDetail -> changeDetail.getCardNumber().toUpperCase());

            List<CardAmountChangeDetail> changeDetailReList = cardAmountChangeDetailDao.selectReconciliationByCardNumbers(DateUtil.dateToString(new Date(), DateUtil.SIMPLE_YMD), cardCodeList);
            Map<String, CardAmountChangeDetail> changeDetailReMap = StreamUtils.toMap(changeDetailReList, changeDetailRe -> changeDetailRe.getCardNumber().toUpperCase());

            for (ReportFormListVO data : tempList) {
                List<String> packageNoList = packageSkuMap.get(data.getOrderNo() + data.getSkuCode());
                if(Objects.isNull(data.getCardCode())){
                    data.setCardCode(Strings.EMPTY);
                }
                Card card = cardMap.get(data.getCardCode().toUpperCase());
                Integer milkAmount = milkAmountMap.get(data.getCardCode().toUpperCase());
                BigDecimal amount = amountMap.get(data.getCardCode().toUpperCase());

                Integer noIssuedCount = noIssuedCountMap.get(data.getCardCode().toUpperCase());
                BigDecimal noIssuedAmount = noIssuedAmountMap.get(data.getCardCode().toUpperCase());

                CardAmountChangeDetail changeDetail = changeDetailMap.get(data.getCardCode().toUpperCase());
                CardAmountChangeDetail changeDetailRe = changeDetailReMap.get(data.getCardCode().toUpperCase());
                data.setRemark("-");
                if (Objects.nonNull(card)) {
                    CardCategory cardCategory = cardCategoryMap.get(card.getCategoryId());
                    if (Objects.nonNull(cardCategory)) {
                        data.setCapPrice(cardCategory.getCapPrice());
                        data.setFloorPrice(cardCategory.getFloorPrice());
                    }
                    data.setActivateTime(card.getActivateTime());
                    data.setRemainingCount(card.getRemainingCount());
                    data.setRemainingAmount(card.getRemainingAmount());
                    data.setCardCount(card.getCardCount());
                    data.setCardAmount(card.getCardAmount());
                    data.setUsageStatus(card.getUsageStatus());
                    data.setCardStatus(card.getCardStatus());
                    data.setShopNo(card.getShopNo());
                    data.setShopName(card.getShopName());
                    data.setInvoiceType(card.getInvoiceType());
                    data.setInvoiceTime(DateUtil.dateToString(card.getInvoiceTime(), DateUtil.SIMPLE_YMD));

                    data.setUsageStatusDesc(CardUsageStatusEnum.getDescByCode(card.getUsageStatus()));
                    data.setCardStatusDesc(CardStatusEnum.getEnumByCode(card.getCardStatus()).getDesc());

                    if (card.getInvoiceType() != null) {
                        if (card.getInvoiceType().equals(InvoiceTypeEnum.RAISING.getCode())) {
                            data.setInvoiceTypeString(InvoiceTypeEnum.RAISING.getDesc());
                        } else if (card.getInvoiceType().equals(InvoiceTypeEnum.PREPAID.getCode())) {
                            data.setInvoiceTypeString(InvoiceTypeEnum.PREPAID.getDesc());
                        }
                    }

                    data.setMilkAmount(Objects.isNull(milkAmount) ? 0 : milkAmount);
                    data.setAmountWithdrawn(Objects.isNull(amount) ? BigDecimal.ZERO : amount);
                    data.setPushNoIssuedCount(Objects.isNull(noIssuedCount) ? 0 : noIssuedCount);
                    data.setPushNoIssuedAmount(Objects.isNull(noIssuedAmount) ? BigDecimal.ZERO : noIssuedAmount);
                    if (Objects.nonNull(cardCategory)) {
                        if (Objects.nonNull(cardCategory.getCapPrice()) && Objects.nonNull(data.getRealAmount()) && data.getRealAmount().compareTo(cardCategory.getCapPrice()) > 0) {
                            data.setRemark("超过上限￥" + cardCategory.getCapPrice());
                        } else if (Objects.nonNull(cardCategory.getFloorPrice()) && Objects.nonNull(data.getRealAmount()) && data.getRealAmount().compareTo(cardCategory.getFloorPrice()) < 0) {
                            data.setRemark("低于下限￥" + cardCategory.getFloorPrice());
                        }
                    }
                    if (changeDetailRe != null) {
                        data.setReconciliationTime(changeDetailRe.getReconciliationTime());
                        data.setReconciliationAmount(changeDetailRe.getReconciliationAmount());
                        data.setRealRemainingAmount(changeDetailRe.getReconciliationAmount().subtract(data.getAmountWithdrawn()));
                        if (changeDetail != null) {
                            data.setChangedTime(changeDetail.getChangedTime());
                            data.setChangedAmount(changeDetail.getChangedAmount());
                        }
                    } else if (changeDetail != null) {
                        data.setChangedAmount(changeDetail.getChangedAmount());
                        data.setChangedTime(changeDetail.getChangedTime());
                        // 设置实际剩余金额
                        data.setRealRemainingAmount(changeDetail.getChangedAmount().subtract(data.getAmountWithdrawn()));
                    } else {
                        data.setRealRemainingAmount(card.getCardAmount().subtract(data.getAmountWithdrawn()));
                    }
                    // 设置实际剩余提数
                    data.setRealRemainingCount(data.getCardCount() - data.getMilkAmount());
                }
                OrderInfo orderInfo = orderInfoMap.get(data.getOrderNo());
                if (Objects.nonNull(orderInfo)) {
                    data.setSrcNo(orderInfo.getSrcNo());
                    data.setChannelCode(orderInfo.getChannel());
                    data.setDeliveryTime(orderInfo.getDeliveryTime());
                    if (Objects.isNull(orderInfo.getDeliveryTime()) && Objects.nonNull(orderInfo.getProductType()) && orderInfo.getProductType().equals(OrderProductTypeEnum.VIRTUAL_CARD.getCode()) && Objects.nonNull(orderInfo.getPayTime())) {
                        data.setDeliveryTime(orderInfo.getPayTime());
                    }
                    data.setDeliveryTimeString(DateUtil.dateToString(data.getDeliveryTime(), DateUtil.SIMPLE_FMT));
                }

                data.setCreateTimeString(DateUtil.dateToString(data.getCreateTime(), DateUtil.SIMPLE_FMT));
                data.setActivateTimeString(DateUtil.dateToString(data.getActivateTime(), DateUtil.SIMPLE_YMD));
                if (CollectionUtils.isNotEmpty(packageNoList)) {
                    data.setTrackingNoList(packageNoList.stream().map(packageMap::get).collect(Collectors.toList()));
                }
            }
            resultList.addAll(tempList);
        }
        result.setList(resultList);
        return result;
    }

    /**
     * 财务报表导出
     *
     * @param dto
     * @return
     */
    @Override
    public void reportFormExport(ReportFormExportBackupDTO dto, ExcelWriter excelWriter) {
        Calendar startTime = Calendar.getInstance();
        startTime.setTime(dto.getCreateStartTime());
        Calendar endTime = Calendar.getInstance();
        endTime.setTime(dto.getCreateEndTime());
        long size = endTime.getTimeInMillis() - startTime.getTimeInMillis();
        if (size < 0) {
            throw new BusinessException("传参有误！");
        }
        Calendar time = Calendar.getInstance();
        time.setTime(startTime.getTime());

        for (; time.getTimeInMillis() <= endTime.getTimeInMillis(); time.add(Calendar.MONTH, 1)) {
            if (time.getTimeInMillis() != startTime.getTimeInMillis()) {
                time.setTime(Objects.requireNonNull(DateUtil.getFirstDayOfMonth(time.getTime())));
            }
            Date startDate = time.getTime();
            time.setTime(Objects.requireNonNull(DateUtil.getFirstDayOfMonth(startDate)));
            Date endDate = DateUtil.getLastDayOfMonth(startDate);
            Calendar flagCalendar = Calendar.getInstance();
            flagCalendar.setTime(time.getTime());
            flagCalendar.add(Calendar.MONTH, 1);
            if (flagCalendar.getTimeInMillis() > endTime.getTimeInMillis()) {
                endDate = endTime.getTime();
            }
            dto.setCreateStartTime(startDate);
            dto.setCreateEndTime(endDate);
            ReportFormListDTO reportFormListDTO = new ReportFormListDTO();
            BeanUtils.copy(dto, reportFormListDTO);
            WriteSheet writeSheet = EasyExcel.writerSheet(DateUtil.dateToString(time.getTime(), DateUtil.SIMPLE_FMT_MONTH)).head(ReportFormListVO.class).build();
            int pageNum = 1;
            int pageSize = 50000;
            Integer pages = 1;
            do{
                reportFormListDTO.setPageNum(pageNum);
                reportFormListDTO.setPageSize(pageSize);
                PageVO<ReportFormListVO> voList = this.reportDynamicFormList(reportFormListDTO);
                pages = voList.getPages();
                pageNum ++;
                excelWriter.write(voList.getList(), writeSheet);
                voList = null;
            }while(pageNum <= pages);
        }
    }

    /**
     * 进销存报表导出
     *
     * @param dto
     * @return
     */
    @Override
    public void inventoryFormExport(ReportFormExportBackupDTO dto, ExcelWriter excelWriter) {
        Calendar startTime = Calendar.getInstance();
        startTime.setTime(dto.getSearchTimeStart());
        Calendar endTime = Calendar.getInstance();
        endTime.setTime(dto.getSearchTimeEnd());
        if (dto.getSearchTimeStart().getTime() < 0 || dto.getSearchTimeEnd().getTime() < 0) {
            throw new BusinessException("传参有误！");
        }
        ReportFormListDTO reportDto = new ReportFormListDTO();
        reportDto.setCardNumber(dto.getCardNumber());
        reportDto.setSrcNo(dto.getSrcNo());
        reportDto.setShopNoList(dto.getShopNoList());
        reportDto.setDeliveryStartTime(dto.getDeliveryStartTime());
        reportDto.setDeliveryEndTime(dto.getDeliveryEndTime());
        reportDto.setUsageStatus(dto.getUsageStatus());
        reportDto.setCardStatus(dto.getCardStatus());
        reportDto.setSearchTimeStart(dto.getSearchTimeStart());
        reportDto.setSearchTimeEnd(dto.getSearchTimeEnd());
        reportDto.setShopName(dto.getShopName());
        reportDto.setShopNo(dto.getShopNo());
        reportDto.setCleanFlag(dto.getCleanFlag());
        reportDto.setBelongToBU(dto.getBelongToBU());
        reportDto.setOrderInfoType(dto.getOrderInfoType());
        int pageNum = 1;
        int pageSize = 50000;
        Integer pages = 1;
        do{
            reportDto.setPageNum(pageNum);
            reportDto.setPageSize(pageSize);
            PageVO<InventoryFormListVO> voList = this.inventoryList(reportDto,false);
            if(pageNum == 1){
                pages = voList.getPages();
            }
            WriteSheet writeSheet = EasyExcel.writerSheet((pageNum - 1) / 18)
                    .head(InventoryFormListVO.class)
                    .build();
            pageNum ++;
            excelWriter.write(voList.getList(), writeSheet);
            voList = null;
        }while(pageNum <= pages);
    }

    /**
     * 财务报表-备份表的时间
     *
     * @return
     */
    @Override
    public void matchDay(String date) {
        List<String> tableNames = orderSkuDao.showAllTables();
        List<String> result = new ArrayList<>();
        tableNames.forEach(name -> {
            String[] strings = StringUtils.split(name, "_");
            result.add(strings[strings.length - 1]);
        });
        if (!result.contains(date)) {
            throw new BusinessException("无该日期的静态数据，无法导出！");
        }
    }

    private void matchDayStatic(String date) {
        List<String> tableNames = orderSkuDao.showAllTables();
        List<String> result = new ArrayList<>();
        tableNames.forEach(name -> {
            String[] strings = StringUtils.split(name, "_");
            result.add(strings[strings.length - 1]);
        });
        if (!result.contains(date)) {
            throw new BusinessException("无该日期的数据！");
        }
    }

    /**
     * 财务报表导出-从备份表
     *
     * @param dto
     * @return
     */
    @Override
    public void reportFormExportFromBackup(ReportFormExportBackupDTO dto, ExcelWriter excelWriter) {
        Calendar startTime = Calendar.getInstance();
        startTime.setTime(dto.getCreateStartTime());
        Calendar endTime = Calendar.getInstance();
        endTime.setTime(dto.getCreateEndTime());
        long size = endTime.getTimeInMillis() - startTime.getTimeInMillis();
        if (size < 0) {
            throw new BusinessException("传参有误！");
        }
        Calendar time = Calendar.getInstance();
        time.setTime(startTime.getTime());
        for (; time.getTimeInMillis() <= endTime.getTimeInMillis(); time.add(Calendar.MONTH, 1)) {
            if (time.getTimeInMillis() != startTime.getTimeInMillis()) {
                time.setTime(Objects.requireNonNull(DateUtil.getFirstDayOfMonth(time.getTime())));
            }
            Date startDate = time.getTime();
            time.setTime(Objects.requireNonNull(DateUtil.getFirstDayOfMonth(startDate)));
            Date endDate = DateUtil.getLastDayOfMonth(startDate);
            Calendar flagCalendar = Calendar.getInstance();
            flagCalendar.setTime(time.getTime());
            flagCalendar.add(Calendar.MONTH, 1);
            if (flagCalendar.getTimeInMillis() > endTime.getTimeInMillis()) {
                endDate = endTime.getTime();
            }
            dto.setCreateStartTime(startDate);
            dto.setCreateEndTime(endDate);
            ReportFormListDTO reportFormListDTO = new ReportFormListDTO();
            BeanUtils.copy(dto, reportFormListDTO);

            WriteSheet writeSheet = EasyExcel.writerSheet(DateUtil.dateToString(time.getTime(), DateUtil.SIMPLE_FMT_MONTH)).head(ReportFormListVO.class).build();
            int pageNum = 1;
            int pageSize = 50000;
            Integer pages = 1;
            do{
                reportFormListDTO.setPageNum(pageNum);
                reportFormListDTO.setPageSize(pageSize);
                PageVO<ReportFormListVO> voList = this.reportFormBackList(reportFormListDTO);
                pages = voList.getPages();
                pageNum ++;
                excelWriter.write(voList.getList(), writeSheet);
                voList = null;
            }while(pageNum <= pages);
        }
    }

    @Override
    public void reportFormExportFromZipper(ReportFormExportBackupDTO dto, ExcelWriter excelWriter) {
        Calendar startTime = Calendar.getInstance();
        startTime.setTime(dto.getCreateStartTime());
        Calendar endTime = Calendar.getInstance();
        endTime.setTime(dto.getCreateEndTime());
        long size = endTime.getTimeInMillis() - startTime.getTimeInMillis();
        if (size < 0) {
            throw new BusinessException("传参有误！");
        }
        Calendar time = Calendar.getInstance();
        time.setTime(startTime.getTime());
        for (; time.getTimeInMillis() <= endTime.getTimeInMillis(); time.add(Calendar.MONTH, 1)) {
            if (time.getTimeInMillis() != startTime.getTimeInMillis()) {
                time.setTime(Objects.requireNonNull(DateUtil.getFirstDayOfMonth(time.getTime())));
            }
            Date startDate = time.getTime();
            time.setTime(Objects.requireNonNull(DateUtil.getFirstDayOfMonth(startDate)));
            Date endDate = DateUtil.getLastDayOfMonth(startDate);
            Calendar flagCalendar = Calendar.getInstance();
            flagCalendar.setTime(time.getTime());
            flagCalendar.add(Calendar.MONTH, 1);
            if (flagCalendar.getTimeInMillis() > endTime.getTimeInMillis()) {
                endDate = endTime.getTime();
            }
            dto.setCreateStartTime(startDate);
            dto.setCreateEndTime(endDate);
            ReportFormListDTO reportFormListDTO = new ReportFormListDTO();
            BeanUtils.copy(dto, reportFormListDTO);

            WriteSheet writeSheet = EasyExcel.writerSheet(DateUtil.dateToString(time.getTime(), DateUtil.SIMPLE_FMT_MONTH)).head(ReportFormListVO.class).build();
            int pageNum = 1;
            int pageSize = 50000;
            Integer pages = 1;
            do{
                reportFormListDTO.setPageNum(pageNum);
                reportFormListDTO.setPageSize(pageSize);
                PageVO<ReportFormListVO> voList = this.reportFormZipperList(reportFormListDTO);
                pages = voList.getPages();
                pageNum ++;
                excelWriter.write(voList.getList(), writeSheet);
                voList = null;
            }while(pageNum <= pages);
        }
    }


    /**
     * 导出字段封装
     *
     * @param dto
     * @return
     */
    private List<ReportFormListVO> packageReportExport(ReportFormListDTO dto) {
        if (StringUtils.isNotBlank(dto.getShopName())) {
            // 查询对应渠道
            List<ShopParam> shopParams = shopParamDao.findByShopName(dto.getShopName());
            List<String> shopNos = StreamUtils.toList(shopParams, ShopParam::getShopNum);
            if (CollectionUtils.isNotEmpty(shopNos)) {
                dto.setShopNoList(shopNos);
            }
        }

        List<OrderSku> skuList = new ArrayList<>();
        if (dto.getOverFlag()) {
            skuList = orderSkuDao.reportOverForm(dto);
        } else {
            skuList = orderSkuDao.reportForm(dto);
        }

        // 结果封装
        List<ReportFormListVO> result = new ArrayList<>();

        for (OrderSku orderSku : skuList) {
            result.add(BeanUtils.copy(orderSku, ReportFormListVO::new));
        }
        List<List<ReportFormListVO>> splitList = StreamUtils.split(result, CardConstant.LIMIT_LIST_LENGTH);
        List<ReportFormListVO> resultList = new ArrayList<>();
        for (List<ReportFormListVO> tempList : splitList) {

            List<String> orderNoList = StreamUtils.convertDistinct(tempList, ReportFormListVO::getOrderNo, String::compareTo);
            List<OrderInfo> orderList = orderInfoDao.findByOrderNoList(orderNoList);
            Map<String, OrderInfo> orderInfoMap = StreamUtils.toMap(orderList, OrderInfo::getOrderNo);

            List<OrderPackage> packageList = orderPackageDao.findByOrderNoList(orderNoList);
            Map<String, String> packageMap = StreamUtils.toMap(packageList, OrderPackage::getPackageNo, OrderPackage::getTrackingNo);
            List<OrderPackageSku> packageSkus = orderPackageSkuDao.selectByOrderNoList(orderNoList);
            Map<String, List<String>> packageSkuMap = StreamUtils.group(packageSkus,
                    x -> x.getOrderNo() + x.getSkuCode(), OrderPackageSku::getPackageNo);

            List<String> cardCodeList = StreamUtils.convertFilter(tempList, ReportFormListVO::getCardCode, StringUtils::isNotBlank);
            List<Card> cardList = cardDao.selectByNumberList(cardCodeList);
            Map<String, Card> cardMap = StreamUtils.toMap(cardList, card -> card.getCardNumber().toUpperCase());

            List<MilkAmountVO> milkAmountAndAmountList = milkDispatchPlanDao.selectMilkAmountsAndAmounts(cardCodeList);
            Map<String, Integer> milkAmountMap = StreamUtils.toMap(milkAmountAndAmountList, x -> x.getCardNumber().toUpperCase(), MilkAmountVO::getMilkAmount);
            Map<String, BigDecimal> amountMap = StreamUtils.toMap(milkAmountAndAmountList, x -> x.getCardNumber().toUpperCase(), MilkAmountVO::getAmount);

            List<MilkAmountVO> noIssuedMilkAmountAndAmountList = milkDispatchPlanDao.selectNoIssuedMilkAmountsAndAmounts(cardCodeList);
            Map<String, Integer> noIssuedCountMap = StreamUtils.toMap(noIssuedMilkAmountAndAmountList, x -> x.getCardNumber().toUpperCase(), MilkAmountVO::getMilkAmount);
            Map<String, BigDecimal> noIssuedAmountMap = StreamUtils.toMap(noIssuedMilkAmountAndAmountList, x -> x.getCardNumber().toUpperCase(), MilkAmountVO::getAmount);

            List<CardCategory> cardCategoryList = new ArrayList<>();
            Map<Long, CardCategory> cardCategoryMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(cardList)) {
                cardCategoryList = cardCategoryDao.selectByPrimaryKey(StreamUtils.convertDistinct(cardList, Card::getCategoryId, Long::compareTo));
                cardCategoryMap = StreamUtils.toMap(cardCategoryList, cardCategory -> cardCategory.getId());
            }

            List<CardAmountChangeDetail> changeDetailList = cardAmountChangeDetailDao.selectByCardNumbers(DateUtil.dateToString(new Date(), DateUtil.SIMPLE_YMD), cardCodeList);
            Map<String, CardAmountChangeDetail> changeDetailMap = StreamUtils.toMap(changeDetailList, changeDetail -> changeDetail.getCardNumber().toUpperCase());

            List<CardAmountChangeDetail> changeDetailReList = cardAmountChangeDetailDao.selectReconciliationByCardNumbers(DateUtil.dateToString(new Date(), DateUtil.SIMPLE_YMD), cardCodeList);
            Map<String, CardAmountChangeDetail> changeDetailReMap = StreamUtils.toMap(changeDetailReList, changeDetailRe -> changeDetailRe.getCardNumber().toUpperCase());

            for (ReportFormListVO data : tempList) {
                List<String> packageNoList = packageSkuMap.get(data.getOrderNo() + data.getSkuCode());
                if(Objects.isNull(data.getCardCode())){
                    data.setCardCode(Strings.EMPTY);
                }
                Card card = cardMap.get(data.getCardCode().toUpperCase());
                Integer milkAmount = milkAmountMap.get(data.getCardCode().toUpperCase());
                BigDecimal amount = amountMap.get(data.getCardCode().toUpperCase());

                Integer noIssuedCount = noIssuedCountMap.get(data.getCardCode().toUpperCase());
                BigDecimal noIssuedAmount = noIssuedAmountMap.get(data.getCardCode().toUpperCase());

                CardAmountChangeDetail changeDetail = changeDetailMap.get(data.getCardCode().toUpperCase());
                CardAmountChangeDetail changeDetailRe = changeDetailReMap.get(data.getCardCode().toUpperCase());
                data.setRemark("-");
                if (Objects.nonNull(card)) {
                    CardCategory cardCategory = cardCategoryMap.get(card.getCategoryId());
                    data.setActivateTime(card.getActivateTime());
                    data.setRemainingCount(card.getRemainingCount());
                    data.setRemainingAmount(card.getRemainingAmount());
                    data.setCardCount(card.getCardCount());
                    data.setCardAmount(card.getCardAmount());
                    data.setUsageStatus(card.getUsageStatus());
                    data.setCardStatus(card.getCardStatus());
                    data.setShopNo(card.getShopNo());
                    data.setShopName(card.getShopName());
                    data.setInvoiceType(card.getInvoiceType());
                    data.setInvoiceTime(DateUtil.dateToString(card.getInvoiceTime(), DateUtil.SIMPLE_YMD));
                    data.setUsageStatusDesc(CardUsageStatusEnum.getDescByCode(card.getUsageStatus()));
                    data.setCardStatusDesc(CardStatusEnum.getEnumByCode(card.getCardStatus()).getDesc());
                    if (card.getInvoiceType() != null) {
                        if (card.getInvoiceType().equals(InvoiceTypeEnum.RAISING.getCode())) {
                            data.setInvoiceTypeString(InvoiceTypeEnum.RAISING.getDesc());
                        } else if (card.getInvoiceType().equals(InvoiceTypeEnum.PREPAID.getCode())) {
                            data.setInvoiceTypeString(InvoiceTypeEnum.PREPAID.getDesc());
                        }
                    }
                    data.setMilkAmount(Objects.isNull(milkAmount) ? 0 : milkAmount);
                    data.setAmountWithdrawn(Objects.isNull(amount) ? BigDecimal.ZERO : amount);
                    data.setPushNoIssuedCount(Objects.isNull(noIssuedCount) ? 0 : noIssuedCount);
                    data.setPushNoIssuedAmount(Objects.isNull(noIssuedAmount) ? BigDecimal.ZERO : noIssuedAmount);
                    if (Objects.nonNull(cardCategory)) {
                        if (Objects.nonNull(cardCategory.getCapPrice()) && Objects.nonNull(data.getRealAmount()) && data.getRealAmount().compareTo(cardCategory.getCapPrice()) > 0) {
                            data.setRemark("超过上限￥" + cardCategory.getCapPrice());
                        } else if (Objects.nonNull(cardCategory.getFloorPrice()) && Objects.nonNull(data.getRealAmount()) && data.getRealAmount().compareTo(cardCategory.getFloorPrice()) < 0) {
                            data.setRemark("低于下限￥" + cardCategory.getFloorPrice());
                        }
                    }

                    if (changeDetailRe != null) {
                        data.setReconciliationTime(changeDetailRe.getReconciliationTime());
                        data.setReconciliationAmount(changeDetailRe.getReconciliationAmount());
                        data.setRealRemainingAmount(changeDetailRe.getReconciliationAmount().subtract(data.getAmountWithdrawn()));

                        if (changeDetail != null) {
                            data.setChangedTime(changeDetail.getChangedTime());
                            data.setChangedAmount(changeDetail.getChangedAmount());
                        }
                    } else if (changeDetail != null) {
                        data.setChangedAmount(changeDetail.getChangedAmount());
                        data.setChangedTime(changeDetail.getChangedTime());
                        // 设置实际剩余金额
                        data.setRealRemainingAmount(changeDetail.getChangedAmount().subtract(data.getAmountWithdrawn()));
                    } else {
                        data.setRealRemainingAmount(card.getCardAmount().subtract(data.getAmountWithdrawn()));
                    }
                    // 设置实际剩余提数
                    data.setRealRemainingCount(data.getCardCount() - data.getMilkAmount());
                }
                OrderInfo orderInfo = orderInfoMap.get(data.getOrderNo());
                if (Objects.nonNull(orderInfo)) {
                    data.setSrcNo(orderInfo.getSrcNo());
                    data.setChannelCode(orderInfo.getChannel());
                    data.setDeliveryTime(orderInfo.getDeliveryTime());
                    if (Objects.isNull(orderInfo.getDeliveryTime()) && Objects.nonNull(orderInfo.getProductType()) && orderInfo.getProductType().equals(OrderProductTypeEnum.VIRTUAL_CARD.getCode()) && Objects.nonNull(orderInfo.getPayTime())) {
                        data.setDeliveryTime(orderInfo.getPayTime());
                    }
                    data.setDeliveryTimeString(DateUtil.dateToString(data.getDeliveryTime(), DateUtil.SIMPLE_FMT));
                }

                data.setCreateTimeString(DateUtil.dateToString(data.getCreateTime(), DateUtil.SIMPLE_FMT));
                data.setActivateTimeString(DateUtil.dateToString(data.getActivateTime(), DateUtil.SIMPLE_YMD));
                if (CollectionUtils.isNotEmpty(packageNoList)) {
                    data.setTrackingNos(StringUtils.join(packageNoList.stream().map(packageMap::get).collect(Collectors.toList()), CardConstant.SEMICOLON));
                }
            }
            resultList.addAll(tempList);
        }
        return resultList;
    }

    @Override
    public List<OrderInfo> selectByCardNumber(List<String> cardNumber) {
        // List<OrderSku> orderSkuList = orderSkuDao.findByCardCode(cardNumber);
        List<OrderSku> orderSkuList = orderSkuDao.findByCardCodeV2(cardNumber);
        if (CollectionUtils.isNotEmpty(orderSkuList)) {
            return orderInfoDao.findByOrderNoList(StreamUtils.toList(orderSkuList, OrderSku::getOrderNo));
        }
        return new ArrayList<>();
    }

    /**
     * 根据订单编号查询订单
     *
     * @param orderNo
     * @return
     */
    @Override
    public OrderInfo selectByOrderNo(String orderNo) {
        return orderInfoDao.findByOrderNo(orderNo);
    }

    @Override
    public List<OrderInfo> selectByOrderNoList(List<String> orderNo) {
        if(CollectionUtils.isEmpty(orderNo)){
            return Collections.emptyList();
        }
        return orderInfoDao.findByOrderNoList(orderNo);
    }

    @Override
    public List<OrderInfo> selectWithSkuByOrderNoList(List<String> orderNo) {
        if(CollectionUtils.isEmpty(orderNo)){
            return Collections.emptyList();
        }
        List<OrderInfo> orderInfoList = orderInfoDao.findByOrderNoList(orderNo);
        List<OrderSku> orderSkuList = orderSkuDao.findByOrderNoList(orderNo);
        Map<String, List<OrderSku>> orderSkuMap = StreamUtils.group(orderSkuList, OrderSku::getOrderNo);
        for (OrderInfo order : orderInfoList) {
            order.setOrderSkus(orderSkuMap.get(order.getOrderNo()));
        }
        return orderInfoList;
    }

    @Override
    public List<OrderInfo> findByUserIdsAndFreeTrialId(List<Long> userIds, Long freeTrialId) {
        return orderInfoDao.findByUserIdsAndFreeTrialId(userIds, freeTrialId);
    }

    @Override
    public List<OrderInfo> findByUserIdsAndFreeTrialId(List<Long> userIds, List<Long> freeTrialIds) {
        return orderInfoDao.findByUserIdsAndFreeTrialId(userIds, freeTrialIds);
    }

    @Override
    public List<OrderInfo> findByUserIdsAndFreeTrialTime(List<Long> userIds, Date startTime, Date endTime) {
        return orderInfoDao.findByUserIdsAndFreeTrialTime(userIds, startTime, endTime);
    }

    @Override
    public Date findMaxCreateTime4FreeTrial(Long freeTrialId) {
        return orderInfoDao.findMaxCreateTime4FreeTrial(freeTrialId);
    }

    @Override
    public List<OrderInfo> findChildOrderInfos(OrderInfo orderInfo) {
        List<OrderInfo> childOrderInfos = new ArrayList<>();
        OrderParentTypeEnum parentTypeEnum = OrderParentTypeEnum.getEnum(orderInfo.getParentType());
        if(OrderParentTypeEnum.PARENT == parentTypeEnum){
            childOrderInfos = orderInfoDao.findByOrderParentNo(orderInfo.getOrderNo());
        }

        if(OrderParentTypeEnum.SIGNAL == parentTypeEnum){
            childOrderInfos = Lists.newArrayList(orderInfo);
        }

        if(OrderParentTypeEnum.CHILD == parentTypeEnum){
            childOrderInfos = orderInfoDao.findByOrderParentNo(orderInfo.getOrderParentNo());
        }
        return childOrderInfos;
    }

    @Override
    public List<ProductSaleCountVO> findProductSaleCount(List<Long> spuIds) {
        return orderInfoMapper.findProductSaleCount(spuIds);
    }

    @Override
    public List<String> selectNaiKaShopNo() {
        return orderInfoDao.selectNaiKaShopNo();
    }

    @Override
    public PageVO<CardSummaryFormListVO> cardSummaryList(CardSummaryFormDTO dto) {
        if(Objects.isNull(dto.getBelongToBU())){
            throw new BusinessException("请选择事业部归属！");
        }
        dto.setSearchTimeStart(DateUtil.dateToString(DateUtil.stringToDate(dto.getSearchTimeStart(), DateUtil.SIMPLE_FMT), DateUtil.SIMPLE_YMD));
        dto.setSearchTimeEnd(DateUtil.dateToString(DateUtil.stringToDate(dto.getSearchTimeEnd(), DateUtil.SIMPLE_FMT), DateUtil.SIMPLE_YMD));
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        List<CardSummaryForm> cardSummaryFormList = cardSummaryFormDao.findByCondition(dto);
        return PageUtils.convert(cardSummaryFormList, data ->{
            CardSummaryFormListVO vo = new CardSummaryFormListVO();
            vo.setShopNo(data.getShopNo());
            vo.setShopName(data.getShopName());
            vo.setSummaryRemainingAmount(data.getSummaryAmount());
            return vo;
        });
    }

    @Override
    public void cardSummaryList(ReportFormExportBackupDTO dto, ExcelWriter excelWriter) {
        WriteSheet writeSheet = EasyExcel.writerSheet().head(CardSummaryFormExcelVO.class).build();
        CardSummaryFormDTO queryDTO = new CardSummaryFormDTO();
        queryDTO.setShopNoList(dto.getShopNoList());
        queryDTO.setSearchTimeStart(DateUtil.dateToString(dto.getSearchTimeStart(), DateUtil.SIMPLE_FMT));
        queryDTO.setSearchTimeEnd(DateUtil.dateToString(dto.getSearchTimeEnd(), DateUtil.SIMPLE_FMT));
        queryDTO.setCleanFlag(dto.getCleanFlag());
        queryDTO.setBelongToBU(dto.getBelongToBU());
        int pageNum = 1;
        int pageSize = 5000;
        int pages = 1;
        do {
            queryDTO.setPageNum(pageNum);
            queryDTO.setPageSize(pageSize);
            PageVO<CardSummaryFormListVO> pageVO = this.cardSummaryList(queryDTO);
            Pagination pagination = pageVO.getPagination();
            pages = pagination.getPages();
            pageNum ++;
            List<CardSummaryFormExcelVO> cardSummaryExcelVOS = BeanUtils.copyList(pageVO.getList(), CardSummaryFormExcelVO::new);
            excelWriter.write(cardSummaryExcelVOS, writeSheet);
        }while (pageNum <= pages);
    }

    @Override
    public List<Long> selectLastedDiscountOrderUserId() {
        return orderInfoDao.selectLastedDiscountOrderUserId();
    }

    @Override
    public Integer countDiscountSkuCnt(Long userId, Long discountActivityId, Long skuId) {
        return orderInfoMapper.countDiscountSkuCnt(userId, discountActivityId, skuId);
    }

    @Override
    public Integer countFullTrialFreeTasteCnt(Long userId, Long fullTrialId, Long freeTasteId) {
        return orderInfoMapper.countFullTrialFreeTasteCnt(userId, fullTrialId, freeTasteId);
    }

    @Override
    public Integer countFreeTasteSkuCnt(Long userId, Long freeTrialId) {
        return orderInfoMapper.countFreeTasteSkuCnt(userId, freeTrialId);
    }

    @Override
    public List<FreeTrialSubCntVO> findFreeTasteOrder(List<Long> activityIds) {
        return orderInfoDao.findFreeTasteOrder(activityIds);
    }

    @Override
    public List<OrderInfoActivityVO> getPayUserList(PayUserQueryDTO dto) {
        return orderInfoDao.getPayUserList(dto);
    }

    @Override
    public void addOrderToMot(Long orderId) {
        if(null == orderId){
            return;
        }
        OrderInfo orderInfo = orderInfoDao.findById(orderId);
        if(null == orderInfo){
            return;
        }
        if(StringUtils.isBlank(orderInfo.getBelongMark()) || StringUtils.isBlank(orderInfo.getUnionId())){
            return;
        }
        WorkWxExternalContact workWxExternalContact = StreamUtils.getFirst(workWxExternalContactDao.selectByUnionId(orderInfo.getUnionId()));
        if(null == workWxExternalContact){
            return;
        }
        // 设置时区为中国时区
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSXXX");
        sdf.setTimeZone(TimeZone.getTimeZone("Asia/Shanghai"));
        List<OrderInfo> orderInfoList = orderInfoDao.findChildByOrderParentNo(orderInfo.getOrderParentNo());
        List<OrderSku> orderSkuList = orderSkuDao.queryOrderSkuByOrderNoList(StreamUtils.toList(orderInfoList, OrderInfo::getOrderNo));
        Map<String, OrderSku> orderSkuMap = StreamUtils.toMap(orderSkuList, OrderSku::getOrderNo);
        for (OrderInfo order: orderInfoList) {
            Map<String, Object> params = new HashMap<>();
            params.put("eventCode", order.getOrderNo());
            params.put("eventTime", sdf.format(order.getPayTime()));
            params.put("customerUnionId", order.getUnionId());
            params.put("customerExternalUserId", workWxExternalContact.getExternalUserId());
            params.put("customPhone", order.getPhone());
            params.put("staffUserId", order.getBelongMark());
            params.put("orderId", order.getOrderNo());
            OrderSku orderSku = orderSkuMap.get(order.getOrderNo());
            if(null != orderSku){
                params.put("orderAmount", orderSku.getRealAmount());
                params.put("skuId", orderSku.getSkuId());
                params.put("skuName", orderSku.getProductName());
                params.put("skuAmount", orderSku.getCount());
            }
            motManager.addOrder(params);
        }
    }

    @Override
    public int updateByOrderNo(OrderInfo orderInfo) {
        return orderInfoDao.updateByOrderNo(orderInfo, orderInfo.getOrderNo());
    }

    /**
     * 根据订单编号查询订单
     *
     * @param orderNo
     * @return
     */
    @Override
    public OrderInfo selectByScrOrderNo(String orderNo) {
        return orderInfoDao.findBySrcNo(orderNo);
    }

    @Override
    public List<OrderInfo> selectLikeOrderNoAndYouZan(String orderNo) {
        if (StringUtils.isBlank(orderNo)) {
            return null;
        }
        return orderInfoDao.findLikeOrderNoAndYouZan(orderNo);
    }

    @Override
    public int update(OrderInfo orderInfo) {
        return orderInfoDao.update(orderInfo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateOrderStatusReceive(List<OrderInfo> orderInfos, OrderManualDeliveryDTO dto) {
        Date deliveryTime = new Date();
        for (OrderInfo orderInfo: orderInfos) {
            if(!Objects.equals(orderInfo.getOrderStatus(), OrderStatusEnum.DELIVERY.getCode())){
                throw new BusinessException("订单非待发货状态！");
            }
            OrderParentTypeEnum parentTypeEnum = OrderParentTypeEnum.getEnum(orderInfo.getParentType());
            if(OrderParentTypeEnum.PARENT == parentTypeEnum){
                throw new BusinessException("订单类型错误！");
            }
            OrderInfo updatePo = new OrderInfo();
            updatePo.setId(orderInfo.getId());
            updatePo.setOrderStatus(OrderStatusEnum.RECEIVE.getCode());
            updatePo.setDeliveryTime(deliveryTime);
            updatePo.setDeliveryMode(OrderDeliveryModeEnum.MANUAL_DELIVERY.getCode());
            orderInfoDao.update(updatePo);

            //手工物流
            if(Objects.equals(OrderManualDeliveryEnum.ORDER_REFUND.getCode(), dto.getDeliveryWay())){
                // 生成订单的包裹信息
                String packageNo = genNoUtil.genOrderNo();
                OrderPackage orderPackage = new OrderPackage();
                orderPackage.setOrderNo(orderInfo.getOrderNo());
                orderPackage.setPackageNo(packageNo);
                orderPackage.setExpressCompany(dto.getExpressCompany());
                orderPackage.setTrackingNo(dto.getExpressCode());
                orderPackage.setDeliveryTime(deliveryTime);
                orderPackage.setConfirmTime(deliveryTime);
                orderPackage.setCreateTime(deliveryTime);
                orderPackage.setUpdateTime(deliveryTime);
                orderPackage.setOrderType(CommonOrderTypeEnum.ORDER.getCode());
                orderPackageDao.insert(orderPackage);

                List<OrderSku> orderSkus = orderSkuDao.findByOrderNo(orderInfo.getOrderNo());
                List<OrderPackageSku> orderPackageSkuList = new ArrayList<>();
                for (OrderSku orderSku : orderSkus) {
                    OrderPackageSku orderPackageSku = new OrderPackageSku();
                    orderPackageSku.setOrderNo(orderInfo.getOrderNo());
                    orderPackageSku.setPackageNo(packageNo);
                    orderPackageSku.setSkuId(orderSku.getSkuId());
                    orderPackageSku.setSkuCode(orderSku.getSkuCode());
                    orderPackageSku.setCount(orderSku.getCount());
                    orderPackageSku.setCreateTime(new Date());
                    orderPackageSku.setUpdateTime(new Date());
                    orderPackageSkuList.add(orderPackageSku);
                }
                if(CollectionUtils.isNotEmpty(orderPackageSkuList)){
                    orderPackageSkuDao.batchInsert(orderPackageSkuList);
                }
            }
        }
        OrderInfo orderInfo = StreamUtils.getFirst(orderInfos);
        OrderParentTypeEnum parentTypeEnum = OrderParentTypeEnum.getEnum(StreamUtils.getFirst(orderInfos).getParentType());
        // 如果有父订单，检查订单状态并更新
        if(OrderParentTypeEnum.CHILD == parentTypeEnum){
            OrderInfo parentOrderInfo = orderInfoDao.findByOrderNo(orderInfo.getOrderParentNo());
            List<OrderInfo> childOrderInfos = orderInfoDao.findByOrderParentNo(orderInfo.getOrderParentNo());
            OrderStatusEnum parentOrderStatusEnum = OrderInfoUtil.getStatus(childOrderInfos);
            if(!Objects.equals(parentOrderStatusEnum.getCode(), parentOrderInfo.getOrderStatus())){
                OrderInfo parentRecord = new OrderInfo();
                parentRecord.setId(parentOrderInfo.getId());
                parentRecord.setOrderStatus(parentOrderStatusEnum.getCode());
                parentRecord.setDeliveryTime(deliveryTime);
                orderInfoDao.update(parentRecord);
            }
        }
    }

    @Override
    public int save(OrderInfo orderInfo) {
        return orderInfoDao.insert(orderInfo);
    }

    @Override
    public List<OrderInfo> countUseful() {
        return orderInfoDao.countUseful();
    }

    /**
     * 查物流
     *
     * @param orderNo
     * @return
     */
    @Override
    public List<OrderPackage> selectOrderPackageByOrderNo(String orderNo) {
        return orderPackageDao.findByOrderNo(orderNo);
    }


    @Override
    public List<OrderInfo> selectOrderYouZan() {
        return orderInfoDao.findByYouZan();
    }

    @Override
    public List<OrderInfo> selectByNoSynchronizedNascent() {
        return orderInfoDao.findByNoSynchronizedNascent();
    }

    @Override
    public List<OrderInfo> findLazySynNascentOrders() {
        return orderInfoDao.findLazySynNascentOrders();
    }

    @Override
    public List<OrderInfo> selectOrderECard() {
        return orderInfoDao.findByECard();
    }

    @Override
    public List<OrderPackageSku> selectPackageSkuByOrderNoList(List<String> orderNoList) {
        return orderPackageSkuDao.selectByOrderNoList(orderNoList);
    }

    @Override
    public OrderInfo selectById(Long id){
        return orderInfoDao.findById(id);
    }


    @Override
    public PageVO<InventoryFormListVO> inventoryList(ReportFormListDTO dto, boolean count) {
        if(Objects.isNull(dto.getBelongToBU())){
            throw new BusinessException("请选择事业部归属！");
        }

        if (StringUtils.isNotBlank(dto.getShopNo())) {
            if(CollectionUtils.isNotEmpty(dto.getShopNoList()) && !dto.getShopNoList().contains(dto.getShopNo())){
                dto.getShopNoList().add(dto.getShopNo());
            }else{
                dto.setShopNoList(Lists.newArrayList(dto.getShopNo()));
            }
        }

        if (Objects.isNull(dto.getSearchTimeStart()) || Objects.isNull(dto.getSearchTimeEnd())) {
            throw new BusinessException("搜索时间不能为空！");
        }
        if(Objects.nonNull(dto.getBelongToBU())){
            List<String> shopNoList = shopParamDao.findByBelongToBU(dto.getBelongToBU());
            if(CollectionUtils.isNotEmpty(dto.getShopNoList())){
                dto.getShopNoList().retainAll(shopNoList);
            }else{
                dto.setShopNoList(shopNoList);
            }
            if(CollectionUtils.isEmpty(dto.getShopNoList())){
                return PageUtils.emptyPage();
            }
        }
        String today = DateUtil.dateToString(new Date(), DateUtil.SIMPLE_YMD);
        Date currentTime = DateUtil.stringToDate(today + " 00:00:00", DateUtil.SIMPLE_FMT);

        if (dto.getSearchTimeEnd().after(currentTime)) {
            throw new BusinessException("结束时间必须在今天之前！");
        }

        Calendar startCalendar = Calendar.getInstance();
        startCalendar.setTime(dto.getSearchTimeStart());
        startCalendar.add(Calendar.DATE, 1);

        Calendar endCalendar = Calendar.getInstance();
        endCalendar.setTime(dto.getSearchTimeEnd());
        endCalendar.add(Calendar.DATE, 1);
        //实际搜索时间
        String startDateString = DateUtil.dateToString(dto.getSearchTimeStart(), "yyyyMMdd");
        String endDateString = DateUtil.dateToString(dto.getSearchTimeEnd(), "yyyyMMdd");

        //搜索时间+1天
        String tableDateStart = DateUtil.dateToString(startCalendar.getTime(), "yyyyMMdd");
        String tableDateEnd = DateUtil.dateToString(endCalendar.getTime(), "yyyyMMdd");

        //校验静态表是否存在
        Date date = DateUtil.stringToDate(divisionDate, DateUtil.SIMPLE_YMD);
        if(dto.getSearchTimeStart().before(date)){
            matchDayStatic(tableDateStart);
        }

        if(dto.getSearchTimeEnd().before(date)){
            matchDayStatic(tableDateEnd);
        }

        Date startDate = DateUtil.stringToDate(startDateString + "000000", DateUtil.DATE_PATTERN_FULL);
        Date endDate = DateUtil.stringToDate(endDateString + "235959", DateUtil.DATE_PATTERN_FULL);

        if(!count){
            if(dto.getPageNum() == 1){
                PageHelper.startPage(dto.getPageNum(), dto.getPageSize(), !count);
            }else{
                PageHelper.startPage(dto.getPageNum(), dto.getPageSize(), count);
            }
        }else{
            PageHelper.startPage(dto.getPageNum(), dto.getPageSize(), count);
        }
        // 主表查询
        List<OrderSkuInventoryVo> skuList = this.inventoryFormInBackup(tableDateEnd, dto); //主表体

        if (CollectionUtils.isEmpty(skuList)) {
            return PageUtils.emptyPage();
        }
        // 结果封装
        PageVO<InventoryFormListVO> result = getInventoryFormListVOPageVO(skuList);
        List<InventoryFormListVO> dataList = result.getList();
        List<List<InventoryFormListVO>> splitList = StreamUtils.split(dataList, CardConstant.LIMIT_LIST_LENGTH);
        List<InventoryFormListVO> resultList = new ArrayList<>();
        for (List<InventoryFormListVO> tempList : splitList) {
            //订单号
            List<String> cardCodeList = StreamUtils.toList(tempList, InventoryFormListVO::getCardCode);

            Map<String, Card> cardMap = getCardMap(tableDateEnd, endDate, cardCodeList);

            //计算周期新增的奶卡
            Map<String, Card> addCardMap = getAddCardMap(tableDateEnd, startDate, endDate, tempList);

            // 查询配送计划 获取字段实际剩余提数和金额（根据有无物流判断）
            Map<String, List<MilkDispatchPlan>> planMap = new HashMap<>();
            Map<String, List<MilkDispatchPlan>> reducePlanMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(cardMap.keySet())) {
                //期初之前发货的金额和数量，用于扣减
                planMap = getPlanMap(tableDateStart, startDate, cardMap);

                //已提奶
                reducePlanMap = getReducePlanMap(tableDateEnd, startDate, endDate, cardMap);
            }

            //奶卡对账金额
            Map<String, List<CardAmountChangeDetail>> reconciliationDetailMap = getReconciliationDetailMap(startDate, endDate, cardCodeList);
            //奶卡调整金额
            Map<String, List<CardAmountChangeDetail>> ChangeDetailMap = getChangeDetailMap(startDate, endDate, cardCodeList);
            //奶卡冲销金额
            Map<String, BigDecimal> writeOffAmountMap = getWriteOffAmountMap(endDate, cardCodeList);

            for (InventoryFormListVO data : tempList) {
                if(Objects.isNull(data.getCardCode())){
                    data.setCardCode(Strings.EMPTY);
                }
                Card card = cardMap.get(data.getCardCode().toUpperCase());
                Card addCard = addCardMap.get(data.getCardCode().toUpperCase());
                if(Objects.nonNull(data.getOrderInfoType())){
                    data.setOrderInfoTypeStr(Objects.equals(data.getOrderInfoType(), OrderInfoTypeEnum.AGAIN_ORDER.getCode())?"是":"否");
                }
                if (Objects.nonNull(card)) {
                    data.setCardCount(card.getCardCount());
                    data.setCardAmount(card.getCardAmount());
                    data.setCardstatus(card.getCardStatus());
                    data.setCardstatusString(CardStatusEnum.getDescByCode(card.getCardStatus()));
                    data.setUsageStatus(card.getUsageStatus());
                    data.setUsageStatusDesc(CardUsageStatusEnum.getDescByCode(card.getUsageStatus()));
                }
                data.setWriteOffAmount(writeOffAmountMap.get(data.getCardCode().toUpperCase()));
                //周期内新增奶卡
                data.setAddCount(0);
                data.setAddAmount(BigDecimal.ZERO);
                if (Objects.nonNull(addCard)) {
                    data.setAddCount(addCard.getCardCount());
                    data.setAddAmount(data.getRealAmount());
                    data.setCardCount(0);
                    data.setCardAmount(BigDecimal.ZERO);
                }

                if (Objects.isNull(data.getDeliveryTime())
                        && OrderProductTypeEnum.VIRTUAL_CARD.getCode().equals(data.getProductType())
                        && Objects.nonNull(data.getPayTime())) {
                    data.setDeliveryTime(data.getPayTime());
                }
                
                if(Objects.nonNull(card) && CardTypeEnum.CYCLE_CARD.getCode().equals(card.getCardType())){
                    data.setDeliveryTime(data.getPayTime());
                    data.setDeliveryTimeString(DateUtil.dateToString(data.getPayTime(), DateUtil.SIMPLE_YMD));
                }else{
                    data.setDeliveryTimeString(DateUtil.dateToString(data.getDeliveryTime(), DateUtil.SIMPLE_YMD));
                }
                
                // 设置剩余提数和金额
                List<CardAmountChangeDetail> reconciliationDetails = reconciliationDetailMap.get(data.getCardCode().toUpperCase());
                List<CardAmountChangeDetail> changeDetails = ChangeDetailMap.get(data.getCardCode().toUpperCase());
                BigDecimal changedAmount = BigDecimal.ZERO;
                BigDecimal reconciliationAmount = BigDecimal.ZERO;
                if (CollectionUtils.isNotEmpty(changeDetails)) {
                    for (CardAmountChangeDetail changeDetail : changeDetails) {
                        if (Objects.nonNull(changeDetail) && Objects.nonNull(changeDetail.getChangedAmount())) {
                            changedAmount = changeDetail.getChangedAmount();
                            break;
                        }
                    }
                }
                if (CollectionUtils.isNotEmpty(reconciliationDetails)) {
                    for (CardAmountChangeDetail cardAmountChangeDetail : reconciliationDetails) {
                        if (Objects.nonNull(cardAmountChangeDetail) && Objects.nonNull(cardAmountChangeDetail.getReconciliationAmount())) {
                            reconciliationAmount = cardAmountChangeDetail.getReconciliationAmount();
                            data.setReconciliationAmount(cardAmountChangeDetail.getReconciliationAmount());
                            data.setReconciliationDate(cardAmountChangeDetail.getReconciliationTime());
                            data.setChangeAountId(cardAmountChangeDetail.getId());
                            data.setAdjustAmount(cardAmountChangeDetail.getAdjustAmount());
                            data.setReconciliationDateString(DateUtil.dateToString(cardAmountChangeDetail.getReconciliationTime(), DateUtil.SIMPLE_FMT));
                            break;
                        }
                    }
                }


                if (reconciliationAmount.compareTo(BigDecimal.ZERO) > 0) {
                    if(Objects.nonNull(addCard)){
                        data.setAddAmount(reconciliationAmount);
                    }else{
                        data.setCardAmount(reconciliationAmount);
                    }
                } else if (changedAmount.compareTo(BigDecimal.ZERO) > 0) {
                    if(Objects.nonNull(addCard)) {
                        data.setAddAmount(changedAmount);
                    }else{
                        data.setCardAmount(changedAmount);
                    }
                }
                if(StringUtils.isNotBlank(data.getGwSourceGroup()) && Objects.nonNull(card) && CollectionUtils.isEmpty(reconciliationDetails)){
                    if("38".equals(data.getGwSourceGroup()) || "39".equals(data.getGwSourceGroup())){
                        data.setReconciliationAmount(card.getCardAmount());
                        data.setReconciliationDate(data.getCreateTime());
                        data.setReconciliationDateString(DateUtil.dateToString(data.getCreateTime(), DateUtil.SIMPLE_FMT));
                    }
                }
                data.setStartAmount(Objects.isNull(data.getCardAmount()) ? BigDecimal.ZERO : data.getCardAmount());
                data.setStartCount(Objects.isNull(data.getCardCount()) ? 0 : data.getCardCount());

                List<MilkDispatchPlan> rePlans = planMap.get(data.getCardCode().toUpperCase());
                if (CollectionUtils.isNotEmpty(rePlans)) {
                    Integer realDeCount = 0;
                    BigDecimal realDeAmount = BigDecimal.ZERO;
                    for (MilkDispatchPlan rePlan : rePlans) {
                        if (rePlan.getPushFlag().equals(PushFlagEnum.COMPLETED.getCode()) && StringUtils.isNotBlank(rePlan.getLogistics())) {
                            realDeCount += rePlan.getMilkAmount();
                            realDeAmount = realDeAmount.add(rePlan.getAmount());
                        }
                    }

                    if (Objects.nonNull(data.getCardAmount())) {
                        data.setStartAmount(data.getCardAmount().compareTo(BigDecimal.ZERO) > 0
                                ? data.getCardAmount().subtract(realDeAmount)
                                : BigDecimal.ZERO);
                    }
                    data.setStartCount(Objects.isNull(data.getCardCount()) ? 0 : data.getCardCount() - realDeCount);
                }

                //周期内减少奶卡
                data.setReduceCount(0);
                BigDecimal reduceAmount = BigDecimal.ZERO;
                List<MilkDispatchPlan> reducePlans = reducePlanMap.get(data.getCardCode().toUpperCase());
                if (CollectionUtils.isNotEmpty(reducePlans)) {
                    Integer reduceCount = 0;
                    for (MilkDispatchPlan reducePlan : reducePlans) {
                        if (reducePlan.getPushFlag().equals(PushFlagEnum.COMPLETED.getCode()) && StringUtils.isNotBlank(reducePlan.getLogistics())) {
                            reduceCount += reducePlan.getMilkAmount();
                            reduceAmount = reduceAmount.add(reducePlan.getAmount());

                        }
                    }
                    data.setReduceCount(reduceCount);
                }
                data.setEndCount(data.getStartCount() + data.getAddCount() - data.getReduceCount());
                data.setReduceAmount(reduceAmount);
                data.setEndAmount(data.getStartAmount().add(data.getAddAmount()).subtract(data.getReduceAmount()).add(Objects.isNull(data.getAdjustAmount()) ? BigDecimal.ZERO : data.getAdjustAmount()));

                //有对账金额, 期末提数为0，期末金额为负数的，调整金额取期末金额的绝对值
                if (data.getEndAmount().compareTo(BigDecimal.ZERO) < 0 && data.getEndCount().equals(0) && Objects.nonNull(data.getReconciliationAmount())) {
/*                    CardAmountChangeDetail changeDetail = new CardAmountChangeDetail();
                    changeDetail.setId(data.getChangeAountId());
                    changeDetail.setAdjustAmount(data.getEndAmount().abs());
                    changeDetail.setAdjustTime(new Date());
                    if (cardAmountChangeDetailDao.updateCardAmountById(changeDetail) > 0) {
                        data.setEndAmount(BigDecimal.ZERO);
                        data.setAdjustAmount(changeDetail.getAdjustAmount());
                    }*/
                    data.setEndAmount(BigDecimal.ZERO);
                    data.setAdjustAmount(data.getEndAmount().abs());
                }
            }
            resultList.addAll(tempList);
        }
        if (dto.getCleanFlag()) {
            List<InventoryFormListVO> cleanTempList = StreamUtils.filter(resultList, x -> !x.getStartCount().equals(0) && x.getAddCount().equals(0) || x.getStartCount().equals(0) && !x.getAddCount().equals(0));
            result.setList(cleanTempList);
        } else {
            result.setList(resultList);
        }
        return result;
    }

    private Map<String, Card> getCardMap(String tableDateEnd, Date endDate, List<String> cardCodeList) {
        List<Card> cardList = cardDao.selectByCardNumbersInBackup(tableDateEnd, cardCodeList, endDate);
        Map<String, Card> cardMap = StreamUtils.toMap(cardList, card -> card.getCardNumber().toUpperCase());
        return cardMap;
    }

    private Map<String, Card> getAddCardMap(String tableDateEnd, Date startDate, Date endDate, List<InventoryFormListVO> tempList) {
        List<String> cardNumberList = StreamUtils.filterConvert(tempList, item -> Objects.nonNull(item.getDeliveryTime()) 
                && (item.getDeliveryTime().after(startDate) || item.getDeliveryTime().equals(startDate))
                && (item.getDeliveryTime().before(endDate) || item.getDeliveryTime().equals(endDate)),
                InventoryFormListVO::getCardCode);
        Map<String, Card> addCardMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(cardNumberList)) {
            List<Card> addCardList = cardDao.selectByCardNumbersInBackup(tableDateEnd, cardNumberList, endDate);
            addCardMap = StreamUtils.toMap(addCardList, card -> card.getCardNumber().toUpperCase());
        }
        return addCardMap;
    }

    private Map<String, List<CardAmountChangeDetail>> getChangeDetailMap(Date startDate, Date endDate, List<String> cardCodeList) {
        List<CardAmountChangeDetail> change = cardAmountChangeDetailDao.selectByCardNumbersChanged(cardCodeList, startDate, endDate);
        Map<String, List<CardAmountChangeDetail>> ChangeDetailMap = StreamUtils.group(change, x -> x.getCardNumber().toUpperCase());
        return ChangeDetailMap;
    }

    private Map<String, List<CardAmountChangeDetail>> getReconciliationDetailMap(Date startDate, Date endDate, List<String> cardCodeList) {
        List<CardAmountChangeDetail> reconciliation = cardAmountChangeDetailDao.selectByCardNumbersReconciliation(cardCodeList, startDate, endDate);
        Map<String, List<CardAmountChangeDetail>> reconciliationDetailMap = StreamUtils.group(reconciliation, x -> x.getCardNumber().toUpperCase());
        return reconciliationDetailMap;
    }
    
    private Map<String, BigDecimal> getWriteOffAmountMap(Date endDate, List<String> cardCodeList) {
        List<CardWriteOffAmountVO> writeOffAmountList = cardAmountChangeDetailDao.selectWriteOffAmountByCardNumbers(cardCodeList, endDate);
        return StreamUtils.toMap(writeOffAmountList, x -> x.getCardNumber().toUpperCase(), CardWriteOffAmountVO::getWriteOffAmount);
    }

    private Map<String, List<MilkDispatchPlan>> getReducePlanMap(String tableDateEnd, Date startDate, Date endDate, Map<String, Card> cardMap) {
        List<MilkDispatchPlan> reducePlans = milkDispatchPlanDao.findByCardNumbersAndSearchTime(tableDateEnd, cardMap.keySet(), startDate, endDate);
        return StreamUtils.group(reducePlans, x -> x.getCardNumber().toUpperCase());
    }

    private Map<String, List<MilkDispatchPlan>> getPlanMap(String tableDateStart, Date startDate, Map<String, Card> cardMap) {
        List<MilkDispatchPlan> plans = milkDispatchPlanDao.findByCardNumbersAndTime(tableDateStart, startDate, cardMap.keySet());
        return StreamUtils.group(plans, x -> x.getCardNumber().toUpperCase());
    }

    private PageVO<InventoryFormListVO> getInventoryFormListVOPageVO(List<OrderSkuInventoryVo> skuList) {
        return PageUtils.convert(skuList, data -> BeanUtils.copy(data, InventoryFormListVO::new));
    }

    @Override
    public List<ShopParamVO> getAllShopParam() {
        List<ShopParam> shopParamList = shopParamDao.getAll();
        return BeanUtils.copyList(shopParamList, ShopParamVO::new);
    }

    @Override
    public List<ShopParamVO> getAllNaiKaShopParam() {
        List<ShopParam> shopParamList = shopParamDao.getAllNaiKaShop();
        return BeanUtils.copyList(shopParamList, ShopParamVO::new);
    }

    @Override
    public List<ShopParamVO> queryFuzzyShopParam(String shopName) {
        List<ShopParam> shopParamList = shopParamDao.findByShopName(shopName);
        return BeanUtils.copyList(shopParamList, ShopParamVO::new);
    }

    @Override
    public List<ShopParamVO> queryFuzzyShopParamByNo(String shopNo) {
        List<ShopParam> shopParamList = shopParamDao.findByShopNameOrShopNo(shopNo);
        return BeanUtils.copyList(shopParamList, ShopParamVO::new);
    }

    @Override
    public PageVO<InvoiceTypeFormListVO> invoiceTypeList(InvoiceTypeFormListDTO dto) {
        startPage(dto.getPageNum(), dto.getPageSize());
        List<ShopInvoice> invoiceTypeList = shopInvoiceDao.invoiceTypeList(dto);
        return PageUtils.convert(invoiceTypeList, data -> BeanUtils.copy(data, InvoiceTypeFormListVO::new));
    }

    @Override
    public ShopInvoice invoiceTypeOne(Long id) {
        return shopInvoiceDao.invoiceTypeOne(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveInvoiceType(InvoiceTypeSaveDTO dto) {
        //校验
        checkParam(dto);

        ShopInvoice shopInvoice = BeanUtils.copy(dto, ShopInvoice::new);
        int updateCount = 0;
        if (Objects.isNull(dto.getId())) {
            updateCount = shopInvoiceDao.addInvoiceType(shopInvoice);
        } else {
            updateCount = shopInvoiceDao.updateInvoiceType(shopInvoice);
        }

        if (updateCount > 0) {

            ReportFormListDTO reportFormListDTO = new ReportFormListDTO();
            reportFormListDTO.setCreateStartTime(dto.getStartTime());
            reportFormListDTO.setCreateEndTime(dto.getEndTime());
            reportFormListDTO.setShopNo(dto.getShopNum());
            List<OrderSku> orderSkuList = orderSkuDao.reportForm(reportFormListDTO);

            if (CollectionUtils.isNotEmpty(orderSkuList)) {

                List<Long> ids = StreamUtils.toList(orderSkuList, OrderSku::getId);

                List<OrderSkuCard> orderSkuCardList = orderSkuCardDao.findByOrderSkuIds(ids);
                List<String> cardNumberList = StreamUtils.toList(orderSkuCardList, OrderSkuCard::getCardNumber);

                List<List<String>> splitList = StreamUtils.split(cardNumberList, 500);
                MilkDispatchPlan plan = new MilkDispatchPlan();
                plan.setInvoiceType(dto.getInvoiceType());

                for (List<String> cardCodeSplitList : splitList) {
                    milkDispatchPlanDao.updateByCreatetime(plan, cardCodeSplitList, dto.getStartTime(), dto.getEndTime());

                    List<String> updateCardNumberList = new ArrayList<>();
                    for (OrderSku orderSku : orderSkuList) {
                        if (StringUtils.isNotBlank(orderSku.getCardCode())) {
                            updateCardNumberList.add(orderSku.getCardCode());
                        }
                    }

                    if (CollectionUtils.isNotEmpty(updateCardNumberList)) {
                        Card card = new Card();
                        card.setInvoiceType(dto.getInvoiceType());
                        cardDao.updateByCardNumbers(card, updateCardNumberList);
                    }
                }
            }
        }
    }

    @Override
    public void checkRealAmountByOrder(OrderCreateDTO orderCreateDTO, CouponInfo couponInfo, boolean useCouponFlag) {
        ProductBaseDTO productBaseDTO = productManager.basicDetail(orderCreateDTO.getOrderSkuCreateDTO().getProductId());
        if (useCouponFlag) {
            checkUseCouponPriceByOrder(productBaseDTO, couponInfo, orderCreateDTO);
        } else {
            if (Objects.nonNull(productBaseDTO) && CollectionUtils.isNotEmpty(productBaseDTO.getSkus()) && Objects.nonNull(productBaseDTO.getSkus().get(0).getSalePrice()) && !(productBaseDTO.getSkus().get(0).getSalePrice().compareTo(orderCreateDTO.getRealAmount()) == 0)) {
                throw new BusinessException("实付金额错误，请重试！");
            }
        }
    }

    @Override
    public void checkCouponGift(OrderCreateDTO orderCreateDTO, CouponInfo couponInfo, Long couponId) {
        ProductBaseDTO productBaseDTO = productManager.basicDetail(orderCreateDTO.getOrderSkuCreateDTO().getProductId());
        //赠品券门槛校验
        checkUseCouponGiftPriceByOrder(productBaseDTO, couponInfo, orderCreateDTO);

        //赠品券与赠品校验
        checkCouponGiftSkuByOrder(couponInfo, orderCreateDTO, couponId);

    }

    private void checkCouponGiftSkuByOrder(CouponInfo couponInfo, OrderCreateDTO orderCreateDTO, Long couponId) {
        List<CouponRule> ruleList = couponRuleManager.queryCouponRuleList(Arrays.asList(couponInfo.getRuleId()));
        if (CollectionUtils.isNotEmpty(ruleList)) {
            CouponRule rule = ruleList.get(0);
            if(CouponShareEnum.NOT_SUPPORT.getCode().equals(rule.getCouponShare())
                    && Objects.nonNull(couponId)){
                throw new BusinessException("该赠品券不支持与优惠券共用！");
            }
            List<CouponGiftRange> couponGiftRangeList = couponGiftRangeManager.findByRuleId(rule.getId());
            Map<Long, CouponGiftRange> couponGiftRangeMap = StreamUtils.toMap(couponGiftRangeList,CouponGiftRange::getSkuId);
            CouponGiftRange couponGiftRange = couponGiftRangeMap.get(orderCreateDTO.getGiftSkuId());
            if(Objects.isNull(couponGiftRange)){
                throw new BusinessException("该赠品券不支持该赠品，请重新选择赠品！");
            }

            if(orderCreateDTO.getGiftCount() > couponGiftRange.getCount()){
                throw new BusinessException("赠品数量错误！");
            }
        }else{
            throw new BusinessException("该赠品券不存在！");
        }
    }

    @Override
    public void updateAdjustAmount(CardAmountUpdateDTO dto) {
        if (Objects.isNull(dto) || Objects.isNull(dto.getChangeAountId())) {
            throw new BusinessException("参数缺失，请重试！");
        }

        if (Objects.isNull(dto.getAdjustAmount())) {
            throw new BusinessException("调整金额不能为空！");
        }

        CardAmountChangeDetail changeDetail = new CardAmountChangeDetail();
        changeDetail.setId(dto.getChangeAountId());
        changeDetail.setAdjustAmount(dto.getAdjustAmount());
        changeDetail.setAdjustTime(new Date());
        cardAmountChangeDetailDao.updateCardAmountById(changeDetail);
    }

    @Override
    public void sign(OrderInfo orderInfo, boolean checkPackage) {
        if(Objects.isNull(orderInfo)){
            throw new BusinessException("订单不存在！");
        }
        OrderStatusEnum orderStatusEnum = OrderStatusEnum.getEnum(orderInfo.getOrderStatus());
        if(OrderStatusEnum.CLOSE == orderStatusEnum){
            throw new BusinessException("订单已关闭！");
        }
        if(OrderStatusEnum.CANCELED == orderStatusEnum){
            throw new BusinessException("订单已取消！");
        }
        if(OrderStatusEnum.PAYMENT == orderStatusEnum){
            throw new BusinessException("订单未支付！");
        }
        OrderParentTypeEnum parentTypeEnum = OrderParentTypeEnum.getEnum(orderInfo.getParentType());
        List<OrderInfo> childOrderInfos;
        if(OrderParentTypeEnum.SIGNAL.equals(parentTypeEnum)){
            childOrderInfos = Lists.newArrayList(orderInfo);
        }else if(OrderParentTypeEnum.PARENT.equals(parentTypeEnum)){
            childOrderInfos = orderInfoDao.findByOrderParentNoWithNoStatus(orderInfo.getOrderNo(), OrderStatusEnum.CLOSE.getCode());
            // 只有实体卡和单品需要物流
            childOrderInfos = StreamUtils.filter(childOrderInfos, OrderTypeV2Enum::needTracking);
        }else{
            throw new BusinessException("订单类型有误");
        }

        if(checkPackage) {
            for (OrderInfo childOrderInfo : childOrderInfos) {
                if(Objects.equals(childOrderInfo.getDeliveryMode(), OrderDeliveryModeEnum.MANUAL_DELIVERY.getCode())){
                    continue;
                }
                List<OrderPackageSku> orderPackageSkuList = orderPackageSkuDao.selectByOrderNoList(Arrays.asList(childOrderInfo.getOrderNo()));
                List<String> packageNoList = StreamUtils.convertDistinct(orderPackageSkuList, OrderPackageSku::getPackageNo, String::compareTo);
                List<OrderPackage> orderPackageList = orderPackageDao.findByOrderNo(childOrderInfo.getOrderNo());
                if (CollectionUtils.isEmpty(orderPackageList) || CollectionUtils.isEmpty(packageNoList)
                        || packageNoList.size() != orderPackageList.size()) {
                    log.info("确认收货定时任务 .  无物流信息回传 --> orderNo:{}", childOrderInfo.getOrderNo());
                    // return;
                    throw new BusinessException("确认收货定时任务 .  无物流信息回传 --> orderNo:" + childOrderInfo.getOrderNo());
                }
                for (OrderPackage orderPackage : orderPackageList) {
                    if (StringUtils.isBlank(orderPackage.getTrackingNo())) {
                        log.info("确认收货定时任务 .  无物流信息回传 --> orderNo:{}", childOrderInfo.getOrderNo());
                        // return;
                        throw new BusinessException("确认收货定时任务 .  无物流信息回传 --> orderNo:" + childOrderInfo.getOrderNo());
                    }
                }
            }
        }
        if(OrderParentTypeEnum.PARENT.equals(parentTypeEnum)){
            childOrderInfos.add(orderInfo);
        }
        if(parentTypeEnum == OrderParentTypeEnum.SIGNAL){
            if(Objects.equals(orderInfo.getOrderType(), OrderTypeEnum.FREE_TRIAL.getCode())){
                // 修改中奖测评状态为待测评
                FreeTrialUserDTO userDTO = new FreeTrialUserDTO();
                userDTO.setOrderNo(orderInfo.getOrderNo());
                List<FreeTrialUser> userList = freeTrialUserManager.findByCondition(userDTO);
                if(CollectionUtils.isNotEmpty(userList)){
                    FreeTrialUser trialUser = userList.get(0);
                    trialUser.setCompeteReview(FreeCompeteReviewEnum.NO.getCode());
                    trialUser.setUpdateTime(new Date());
                    freeTrialUserManager.update(trialUser);
                }
            }
        }
        for (OrderInfo childOrderInfo : childOrderInfos) {
            OrderInfo record =new OrderInfo();
            record.setOrderStatus(OrderStatusEnum.COMPLETED.getCode());
            Date now = new Date();
            record.setConfirmTime(now);
            if(Objects.isNull(childOrderInfo.getDeliveryTime())){
                record.setDeliveryTime(now);
            }
            orderInfoDao.updateByOrderNo(record,childOrderInfo.getOrderNo());
            if (Objects.equals(childOrderInfo.getSourceType(), OrderSourceTypeEnum.FULL_TRIAL.getCode()) && Objects.nonNull(childOrderInfo.getFullTrialId())) {
                FreeTrialSubActivityRecord mainOrderRecord = freeTrialSubActivityRecordDao.findBySourceNo(childOrderInfo.getOrderNo());
                if (Objects.nonNull(mainOrderRecord)) {
                    FreeTrialSubActivityRecord updateRecord = new FreeTrialSubActivityRecord();
                    updateRecord.setId(mainOrderRecord.getId());
                    updateRecord.setExecutionTime(new Date());
                    freeTrialSubActivityRecordDao.updateById(updateRecord);
                }
            }
        }
    }

    @Override
    public void sign(String orderNo) {

        OrderInfo record =new OrderInfo();
        record.setOrderStatus(OrderStatusEnum.COMPLETED.getCode());
        record.setConfirmTime(new Date());
        orderInfoDao.updateByOrderNo(record,orderNo);
    }

    @Override
    public List<OrderCouponInfoVO> selectCouponInfoName(List<String> orderNoList) {
        return couponInfoDao.selectCouponInfoName(orderNoList);
    }

    private BigDecimal getDiscountPrice(SkuBaseDTO skuBaseDTO, BigDecimal salePrice) {
        if(null != skuBaseDTO.getDiscountActivityId() && null != skuBaseDTO.getDiscountPrice()){
            if(DiscountActivityShareTypeEnum.COUPON.getCode().equals(skuBaseDTO.getSharingDiscount())){
                //可与优惠券共享，促销价直接当售价使用去计算优惠券
                salePrice = skuBaseDTO.getDiscountPrice();
            }
        }
        return salePrice;
    }

    private void checkUseCouponPriceByOrder(ProductBaseDTO productBaseDTO, CouponInfo info, OrderCreateDTO orderCreateDTO) {
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(productBaseDTO.getSkus())) {
            BigDecimal salePriceSum = null;
            BigDecimal listPriceSum = null;
            for (SkuBaseDTO skuBaseDTO : productBaseDTO.getSkus()) {
                BigDecimal salePrice = skuBaseDTO.getSalePrice();
                salePrice = getDiscountPrice(skuBaseDTO, salePrice);
                if(Objects.nonNull(orderCreateDTO.getOrderSkuCreateDTO().getSkuId()) && orderCreateDTO.getOrderSkuCreateDTO().getSkuId().equals(skuBaseDTO.getId())){
                    if(BasicFlagEnum.YES.getKey().equals(orderCreateDTO.getCycleFlag())){
                        List<SkuAttrDTO> skuAttrList = skuBaseDTO.getSkuAttrs().stream().filter(skuAttr -> "times".equals(skuAttr.getAttrName())
                                && orderCreateDTO.getOrderCycleCreateDTO().getTimes().equals(Integer.valueOf(skuAttr.getAttrValue()))).collect(Collectors.toList());
                        if(CollectionUtils.isEmpty(skuAttrList)){
                            salePriceSum = BigDecimal.ZERO;
                        }else{
                            salePriceSum = new BigDecimal(StreamUtils.getFirst(skuAttrList).getAttrSpecValue()).multiply(new BigDecimal(StreamUtils.getFirst(skuAttrList).getAttrValue())).multiply(new BigDecimal(String.valueOf(orderCreateDTO.getOrderCycleCreateDTO().getMilkAmount())));
                        }
                    }else{
//                        salePriceSum = SkuUtil.calculatePrice(salePrice, orderCreateDTO.getOrderSkuCreateDTO().getCount());
                        salePriceSum = orderCreateDTO.getTotalAmount();
                        listPriceSum = SkuUtil.calculatePrice(skuBaseDTO.getListPrice(), orderCreateDTO.getOrderSkuCreateDTO().getCount());
                    }
                }
            }
            List<CouponRule> ruleList = couponRuleManager.queryCouponRuleList(Arrays.asList(info.getRuleId()));
            if (CollectionUtils.isNotEmpty(ruleList)) {
                CouponRule rule = ruleList.get(0);
                if(Objects.nonNull(rule.getBuyType())){
                    if(CouponBuyTypeEnum.NO_LIMIT.getCode().equals(rule.getBuyType())){
                        //购买类型不限
                    }else if(CouponBuyTypeEnum.SINGLE.getCode().equals(rule.getBuyType())){
                        if(BasicFlagEnum.YES.getKey().equals(orderCreateDTO.getCycleFlag())){
                            throw new BusinessException("该优惠券不支持单次购买，请选择其他优惠券!");
                        }
                    }else if(CouponBuyTypeEnum.CYCLE.getCode().equals(rule.getBuyType())){
                        if(!BasicFlagEnum.YES.getKey().equals(orderCreateDTO.getCycleFlag())){
                            throw new BusinessException("该优惠券不支持周期购，请选择其他优惠券!");
                        }
                    }
                }
                boolean checkRangeFlag = couponRuleManager.checkCouponAllRange(rule.getId(), rule.getCouponRange(), productBaseDTO);
                if (checkRangeFlag) {
                    BigDecimal couponPrice = null;
                    boolean checkPriceFlage = false;
                    if (Objects.nonNull(salePriceSum) && salePriceSum.compareTo(rule.getAmountFull()) >= 0) {
                        checkPriceFlage = true;
                        if (CouponWayEnum.FULL_REDUCE.getCode().equals(rule.getCouponWay())) {
                            couponPrice = salePriceSum.subtract(rule.getAmountReduce()).setScale(2,
                                    BigDecimal.ROUND_HALF_UP);

                        } else if (CouponWayEnum.UNLIMITED.getCode().equals(rule.getCouponWay())) {
                            couponPrice = salePriceSum.multiply(new BigDecimal(rule.getDiscount())).divide(new BigDecimal(100)).setScale(2,
                                    BigDecimal.ROUND_HALF_UP);
                        }

                        couponPrice = couponPrice.compareTo(new BigDecimal("0.01")) < 0 ? new BigDecimal("0.01") : couponPrice;


                    } else if (Objects.isNull(salePriceSum) && Objects.nonNull(listPriceSum) && listPriceSum.compareTo(rule.getAmountFull()) >= 0) {
                        checkPriceFlage = true;
                        if (CouponWayEnum.FULL_REDUCE.getCode().equals(rule.getCouponWay())) {
                            couponPrice = listPriceSum.subtract(rule.getAmountReduce()).setScale(2,
                                    BigDecimal.ROUND_HALF_UP);

                        } else if (CouponWayEnum.UNLIMITED.getCode().equals(rule.getCouponWay())) {
                            couponPrice = listPriceSum.multiply(new BigDecimal(rule.getDiscount())).divide(new BigDecimal(100)).setScale(2,
                                    BigDecimal.ROUND_HALF_UP);
                        }

                        couponPrice = couponPrice.compareTo(new BigDecimal("0.01")) < 0 ? new BigDecimal("0.01") : couponPrice;

                    }

                    if (checkPriceFlage) {
                        if (orderCreateDTO.getRealAmount().compareTo(couponPrice) == 0) {
                            return;
                        }
                        throw new BusinessException("实付金额错误，请重试！");
                    } else {
                        throw new BusinessException("优惠券错误，请重试!");
                    }
                } else {
                    throw new BusinessException("该优惠券不支持这个商品，请选择其他优惠券!");
                }
            }else{
                throw new BusinessException("该优惠券不存在!");
            }
        }
    }

    private void checkUseCouponGiftPriceByOrder(ProductBaseDTO productBaseDTO, CouponInfo info, OrderCreateDTO orderCreateDTO) {
        if (CollectionUtils.isNotEmpty(productBaseDTO.getSkus())) {
            BigDecimal salePriceSum = null;
            BigDecimal listPriceSum = null;
            for (SkuBaseDTO skuBaseDTO : productBaseDTO.getSkus()) {
                if(Objects.nonNull(orderCreateDTO.getOrderSkuCreateDTO().getSkuId()) && orderCreateDTO.getOrderSkuCreateDTO().getSkuId().equals(skuBaseDTO.getId())){
                    if(BasicFlagEnum.YES.getKey().equals(orderCreateDTO.getCycleFlag())){
                        List<SkuAttrDTO> skuAttrList = skuBaseDTO.getSkuAttrs().stream().filter(skuAttr -> "times".equals(skuAttr.getAttrName())
                                && orderCreateDTO.getOrderCycleCreateDTO().getTimes().equals(Integer.valueOf(skuAttr.getAttrValue()))).collect(Collectors.toList());
                        if(CollectionUtils.isEmpty(skuAttrList)){
                            salePriceSum = BigDecimal.ZERO;
                        }else{
                            salePriceSum = new BigDecimal(StreamUtils.getFirst(skuAttrList).getAttrSpecValue()).multiply(new BigDecimal(StreamUtils.getFirst(skuAttrList).getAttrValue())).multiply(new BigDecimal(String.valueOf(orderCreateDTO.getOrderCycleCreateDTO().getMilkAmount())));
                        }
                    }else{
                        salePriceSum = SkuUtil.calculatePrice(skuBaseDTO.getSalePrice(), orderCreateDTO.getOrderSkuCreateDTO().getCount());

                        listPriceSum = SkuUtil.calculatePrice(skuBaseDTO.getListPrice(), orderCreateDTO.getOrderSkuCreateDTO().getCount());
                    }
                }
            }

            List<CouponRule> ruleList = couponRuleManager.queryCouponRuleList(Arrays.asList(info.getRuleId()));
            if (CollectionUtils.isNotEmpty(ruleList)) {
                CouponRule rule = ruleList.get(0);
                if(Objects.nonNull(rule.getBuyType())){
                    if(CouponBuyTypeEnum.NO_LIMIT.getCode().equals(rule.getBuyType())){
                        //购买类型不限
                    }else if(CouponBuyTypeEnum.SINGLE.getCode().equals(rule.getBuyType())){
                        if(BasicFlagEnum.YES.getKey().equals(orderCreateDTO.getCycleFlag())){
                            throw new BusinessException("该赠品券不支持单次购买，请选择其他赠品券!");
                        }
                    }else if(CouponBuyTypeEnum.CYCLE.getCode().equals(rule.getBuyType())){
                        if(!BasicFlagEnum.YES.getKey().equals(orderCreateDTO.getCycleFlag())){
                            throw new BusinessException("该赠品券不支持周期购，请选择其他赠品券!");
                        }
                    }
                }
                boolean checkRangeFlag = couponRuleManager.checkCouponAllRange(rule.getId(), rule.getCouponRange(), productBaseDTO);
                if (checkRangeFlag) {
                    boolean checkPriceFlage = false;
                    //该笔订单有使用优惠券，使用券后价比对赠品券门槛
                    if(Objects.nonNull(orderCreateDTO.getCouponId())){
                        salePriceSum =  orderCreateDTO.getRealAmount();
                    }
                    if (Objects.nonNull(salePriceSum) && salePriceSum.compareTo(rule.getAmountFull()) >= 0) {
                        checkPriceFlage = true;

                    } else if (Objects.isNull(salePriceSum) && Objects.nonNull(listPriceSum) && listPriceSum.compareTo(rule.getAmountFull()) >= 0) {
                        checkPriceFlage = true;
                    }

                    if (checkPriceFlage) {
                        return;
                    } else {
                        throw new BusinessException("赠品券错误，请重试!");
                    }
                } else {
                    throw new BusinessException("该赠品券不支持这个赠品，请选择其他赠品!");
                }
            }
        }
    }

    private void checkParam(InvoiceTypeSaveDTO dto) {
        if (Objects.isNull(dto.getStartTime())) {
            throw new BusinessException("开始时间不能为空！");
        }
        if (Objects.nonNull(dto.getStartTime()) && Objects.nonNull(dto.getEndTime()) && dto.getStartTime().compareTo(dto.getEndTime()) > 0) {
            throw new BusinessException("开始时间不能晚于结束时间！");
        }
        if (Objects.isNull(dto.getShopNum())) {
            throw new BusinessException("渠道不能为空！");
        }

        List<ShopInvoice> shopInvoiceList = shopInvoiceDao.selectShopInvoiceByShopNum(dto);

        List<TimeVO> newTimeVOS = prepareTime(dto, null);
        List<TimeVO> oldTimeVOS = prepareTime(null, shopInvoiceList);
        Integer checkTimeNum = checkTime(newTimeVOS, oldTimeVOS);
        if (checkTimeNum > 0) {
            throw new BusinessException("该渠道有重叠的生效时间区间!");
        }
    }


    private List<TimeVO> prepareTime(InvoiceTypeSaveDTO dto, List<ShopInvoice> shopInvoiceList) {
        List<TimeVO> timeVOS = new ArrayList<>();
        if (dto != null) {
            TimeVO timeVO = new TimeVO();
            timeVO.setStartTime(dto.getStartTime());
            timeVO.setEndTime(dto.getEndTime());
            timeVOS.add(timeVO);
        }
        if (!CollectionUtils.isEmpty(shopInvoiceList)) {
            shopInvoiceList.stream().forEach(data -> {
                TimeVO timeVO = new TimeVO();
                timeVO.setStartTime(data.getStartTime());
                timeVO.setEndTime(data.getEndTime());
                timeVOS.add(timeVO);
            });
        }
        return timeVOS;
    }

    private Integer checkTime(List<TimeVO> newTimeVOS, List<TimeVO> oldTimeVOS) {
        Integer num = 0;  //没有冲突返回null,有冲突返回冲突的时间段
        for (int i = 0; i < newTimeVOS.size(); i++) {
            Date newStart = newTimeVOS.get(i).getStartTime();
            Date newEnd = newTimeVOS.get(i).getEndTime();
            for (int j = 0; j < oldTimeVOS.size(); j++) {
                Date oldStart = oldTimeVOS.get(j).getStartTime();
                Date oldEnd = oldTimeVOS.get(j).getEndTime();
                if (Objects.isNull(oldEnd) && Objects.isNull(newEnd)) {
                    num = num + 1;
                } else if (Objects.isNull(oldEnd)) {
                    if (newEnd.compareTo(oldStart) >= 0) {
                        num = num + 1;
                    }
                } else if (Objects.isNull(newEnd)) {
                    if (newStart.compareTo(oldEnd) <= 0) {
                        num = num + 1;
                    }
                } else {
                    //compareTo返回结果-1 0 1 表示前者比后者<,=,>关系 ,下面的if判断涉及具体的怎样比较可以自行优化
                    if ((oldStart.compareTo(newStart) == -1 && newStart.compareTo(oldEnd) == -1)
                            || (oldStart.compareTo(newEnd) == -1 && newEnd.compareTo(oldEnd) == -1)
                            || (newStart.compareTo(oldStart) == -1 && oldStart.compareTo(newEnd) == -1)   //新加部分
                            || (newStart.compareTo(oldEnd) == -1 && oldEnd.compareTo(newEnd) == -1)   //新加部分
                            || oldEnd.compareTo(newStart) == 0 || oldStart.compareTo(newEnd) == 0
                            || oldEnd.compareTo(newEnd) == 0 || oldStart.compareTo(newStart) == 0) {
                        num = num + 1;
                    }
                }

            }
        }
        return num;

    }

    @Override
    public void reconciliationWriteOff(Card card, CardAmountChangeDetail changeDetail) {
        Integer milkAmount = changeDetail.getMilkAmount();
        BigDecimal amount = changeDetail.getAmountWithdrawn();
        BigDecimal reconciliationAmount = changeDetail.getReconciliationAmount();
        if(reconciliationAmount.compareTo(BigDecimal.ZERO) < 0 ){
            throw new BusinessException("对账金额不允许为负数！");
        }
        List<CardAmountChangeDetail> changeDetailList = cardAmountChangeDetailDao.selectByCardNumberWriteOff(card.getCardNumber());
        BigDecimal sumWriteOffAmount = changeDetailList.stream().map(CardAmountChangeDetail::getWriteOffAmount).reduce(BigDecimal.ZERO,BigDecimal::add);
        BigDecimal realAmount = amount.add(sumWriteOffAmount);
        List<MilkDispatchPlan> plans = milkDispatchPlanDao.findPlanByCardNumber(card.getCardNumber());
        Integer totalSkuMilkAmount = plans.stream().mapToInt(x -> x.getMilkAmount() / x.getDivisor()).sum();
        List<Long> ids = StreamUtils.convert(plans, x -> x.getId());
        Map<String, List<MilkDispatchPlan>> skuCodeMap = StreamUtils.group(plans, MilkDispatchPlan::getSkuCode);
        Map<String, PlanPercentageDTO> skuPercenMap = new HashMap<>();
        int sumCount = skuCodeMap.size();
        int count = 0;
        BigDecimal sumPercentage = BigDecimal.ZERO;
        //计算已提计划中的各个sku金额和金额占已提的百分比
        if(amount.compareTo(BigDecimal.ZERO) > 0 || milkAmount > 0){
            for (Map.Entry<String, List<MilkDispatchPlan>> entry : skuCodeMap.entrySet()) {
                PlanPercentageDTO percentageDTO = new PlanPercentageDTO();
                count ++;
                List<MilkDispatchPlan> planList = entry.getValue();
                BigDecimal skuCodeAmount = planList.stream().map(MilkDispatchPlan::getAmount).reduce(BigDecimal.ZERO,BigDecimal::add);
                Integer skuMilkAmount = 0;
                for (MilkDispatchPlan plan : planList) {
                    skuMilkAmount = skuMilkAmount + plan.getMilkAmount() / plan.getDivisor();
                }
                BigDecimal percentage = BigDecimal.ZERO;
                if(amount.compareTo(BigDecimal.ZERO) > 0){
                    percentage = skuCodeAmount.divide(amount,2, BigDecimal.ROUND_HALF_UP);
                }else{
                    percentage = BigDecimal.valueOf(skuMilkAmount).divide(BigDecimal.valueOf(totalSkuMilkAmount),2, BigDecimal.ROUND_HALF_UP);
                }
                //最后一个sku补足百分比
                if(count == sumCount){
                    percentageDTO.setPercentage(BigDecimal.ONE.subtract(sumPercentage));
                }else{
                    percentageDTO.setPercentage(percentage);
                }
                percentageDTO.setSkuAmount(skuCodeAmount);
                percentageDTO.setSkuMilkAmount(skuMilkAmount);
                skuPercenMap.put(entry.getKey(), percentageDTO);
                sumPercentage = sumPercentage.add(percentage);
            }
        }

        if(card.getCardCount() <= milkAmount){
            //奶卡已经提完
            if(reconciliationAmount.compareTo(realAmount) != 0){
                //对账金额大于或者小于已提金额，冲销
                BigDecimal writeOffAmount = reconciliationAmount.subtract(realAmount);
                OrderWriteOffOmsCreateDTO planWriteOffDTO = writeOffAmountDistribution(card, skuPercenMap, writeOffAmount);
                //调oms冲销接口
                planWriteOffDTO.setReconciliationTime(changeDetail.getReconciliationTime());
                omsUtil.writeOffCreate(planWriteOffDTO);
                changeDetail.setWriteOffAmount(writeOffAmount);
                changeDetail.setWriteOffTime(new Date());
                changeDetail.setWriteOffJson(JSON.toJSONString(planWriteOffDTO));
                log.info("oms冲销,dto ->:{}",JSON.toJSONString(planWriteOffDTO));
            }
        }else{
            //奶卡未提完

            //奶卡剩余提数
            BigDecimal remainingCount = new BigDecimal(String.valueOf(card.getCardCount() - milkAmount));
            //对账金额大于已提金额(或者没有提过),不需要冲销
            if(reconciliationAmount.compareTo(realAmount.add(remainingCount.multiply(new BigDecimal("0.1")))) >= 0
                    || milkAmount.equals(0)){
                BigDecimal difference = reconciliationAmount.subtract(realAmount);
                BigDecimal amountSingle = difference.divide(remainingCount, 2, BigDecimal.ROUND_HALF_UP);
                changeDetail.setCardAmountSingle(amountSingle);
                milkDispatchPlanDao.updateAmountByCardNumber(card.getCardNumber(), amountSingle);

                //除不尽,补足
                if(amountSingle.multiply(remainingCount).compareTo(difference) != 0){
                    List<MilkDispatchPlan> noPushPlanList = milkDispatchPlanDao.findNoPushPlanByCardNumber(card.getCardNumber());
                    Integer noPushPlanCount = noPushPlanList.stream().mapToInt(MilkDispatchPlan::getMilkAmount).sum();

                    if(noPushPlanCount >= remainingCount.intValue()){
                        MilkDispatchPlan firstPlan = StreamUtils.getFirst(noPushPlanList);
                        BigDecimal lastPlanAmount = difference.subtract(amountSingle.multiply(remainingCount.subtract(BigDecimal.valueOf(firstPlan.getMilkAmount()))));
                        MilkDispatchPlan plan = new MilkDispatchPlan();
                        plan.setId(firstPlan.getId());
                        plan.setAmount(lastPlanAmount);
                        milkDispatchPlanDao.updateById(plan);
                    }
                }
            }else if(reconciliationAmount.compareTo(realAmount.add(remainingCount.multiply(new BigDecimal("0.1")))) < 0){
                //对账金额小于等于已提金额
                BigDecimal writeOffAmount = reconciliationAmount.subtract(realAmount);
                OrderWriteOffOmsCreateDTO planWriteOffDTO;
                BigDecimal remainingAmount = remainingCount.multiply(new BigDecimal("0.1"));
                if(remainingAmount.compareTo(BigDecimal.ONE) <= 0){
                    MilkDispatchPlan lastPlan = StreamUtils.getFirst(plans);
                    String lastPlanSkuCode = lastPlan.getSkuCode();
                    planWriteOffDTO = writeOffAmountDistribution(card, skuPercenMap, writeOffAmount, lastPlanSkuCode, remainingAmount);
                    //调oms冲销接口
                    planWriteOffDTO.setReconciliationTime(changeDetail.getReconciliationTime());
                    omsUtil.writeOffCreate(planWriteOffDTO);
                    changeDetail.setWriteOffAmount(writeOffAmount.subtract(remainingAmount));
                    changeDetail.setWriteOffTime(new Date());
                    changeDetail.setWriteOffJson(JSON.toJSONString(planWriteOffDTO));
                    log.info("oms冲销,dto ->:{}",JSON.toJSONString(planWriteOffDTO));

                }else{
                    BigDecimal writeOffTotalAmount = writeOffAmount.subtract(remainingAmount);
                    planWriteOffDTO = writeOffAmountDistribution(card, skuPercenMap, writeOffTotalAmount);
                    //调oms冲销接口
                    planWriteOffDTO.setReconciliationTime(changeDetail.getReconciliationTime());
                    omsUtil.writeOffCreate(planWriteOffDTO);
                    changeDetail.setWriteOffAmount(writeOffTotalAmount);
                    changeDetail.setWriteOffTime(new Date());
                    changeDetail.setWriteOffJson(JSON.toJSONString(planWriteOffDTO));
                    log.info("oms冲销,dto ->:{}",JSON.toJSONString(planWriteOffDTO));
                }
                changeDetail.setCardAmountSingle(new BigDecimal("0.1"));
                milkDispatchPlanDao.updateAmountByCardNumber(card.getCardNumber(), new BigDecimal("0.1"));
            }
        }
        if(CollectionUtils.isNotEmpty(ids)){
            milkDispatchPlanDao.updateBillFlag(ids);
        }
        changeDetail.setCreateTime(new Date());
        changeDetail.setUpdateTime(new Date());
        cardAmountChangeDetailDao.insert(changeDetail);
    }

    @Override
    public void changeDetail(Card card, CardAmountChangeDetail changeDetail) {
        BigDecimal remainingCount = new BigDecimal(String.valueOf(card.getCardCount() - changeDetail.getMilkAmount()));

        milkDispatchPlanDao.updateAmountByCardNumber(card.getCardNumber(), changeDetail.getCardAmountSingle());

        BigDecimal remainingAmount = changeDetail.getChangedAmount().subtract(changeDetail.getAmountWithdrawn());
        //除不尽,补足
        if(changeDetail.getCardAmountSingle().multiply(remainingCount).compareTo(remainingAmount) != 0){
            List<MilkDispatchPlan> noPushPlanList = milkDispatchPlanDao.findNoPushPlanByCardNumber(card.getCardNumber());
            Integer noPushPlanCount = noPushPlanList.stream().mapToInt(MilkDispatchPlan::getMilkAmount).sum();

            if(noPushPlanCount >= remainingCount.intValue()){
                MilkDispatchPlan firstPlan = StreamUtils.getFirst(noPushPlanList);
                BigDecimal lastPlanAmount = remainingAmount.subtract(changeDetail.getCardAmountSingle().multiply(remainingCount.subtract(BigDecimal.valueOf(firstPlan.getMilkAmount()))));
                if(lastPlanAmount.compareTo(BigDecimal.ZERO) > 0){
                    MilkDispatchPlan plan = new MilkDispatchPlan();
                    plan.setId(firstPlan.getId());
                    plan.setAmount(lastPlanAmount);
                    milkDispatchPlanDao.updateById(plan);
                }
            }
        }
    }

    @Override
    public void addOrderToYoushu(String orderNo, boolean deliveryFlag) {

        OrderInfo orderInfo = orderInfoDao.findLpkByOrderNo(orderNo);
        if(Objects.isNull(orderInfo)){
            log.error("订单添加/变更数据上报 . 没查到订单信息 --> orderNo: {}", orderNo);
            return;
        }
        
        CustomerUser customerUser = customerUserDao.findById(orderInfo.getUserId());
        if(Objects.isNull(customerUser)){
            log.error("订单添加/变更数据上报 . 没查到用户信息 --> orderNo: {}", orderNo);
            return;
        }

        //获取cpsId，先根据主订单号，若没有再根据openId。
        String keyBySrcNo = "CPS:cache:srcNo:" + orderInfo.getOrderParentNo();
        String cpsId = redisOperation.get(keyBySrcNo);
        if(StringUtils.isBlank(cpsId)){
            String keyByOpenId = "CPS:cache:openId:" + customerUser.getOpenId();
            cpsId = redisOperation.get(keyByOpenId);
            if(StringUtils.isBlank(cpsId)){
                cpsId = Strings.EMPTY;
            }else{
                redisOperation.setnx(keyBySrcNo, cpsId, 30L, TimeUnit.DAYS);
            }
        }

        List<OrderSku> orderSkuList = new ArrayList<>();
        //根据skucode合并
        List<OrderSku> mergeOrerSkus = new ArrayList<>();

        OrderParentTypeEnum parentTypeEnum = OrderParentTypeEnum.getEnum(orderInfo.getParentType());
        if(OrderParentTypeEnum.PARENT == parentTypeEnum){
            List<OrderInfo> childOrderInfos = orderInfoDao.findByOrderParentNo(orderInfo.getOrderNo());
            orderSkuList = orderSkuDao.findLikeByOrderNo(orderInfo.getOrderNo());
            mergeOrerSkus = this.mergeOrderSkuNo(orderSkuList);

            // 有需要物流的订单
            OrderInfo needTrackingOrderInfo = StreamUtils.findFirst(childOrderInfos, OrderTypeV2Enum::needTracking);
            if(needTrackingOrderInfo == null){
                submitDelivery(orderInfo, customerUser, mergeOrerSkus, cpsId, deliveryFlag);
            }
        }

        //如果是电子卡和周期购订单，由于是直接完成订单的，在完成前要先上报1150【已支付待发货】状态
        if(OrderParentTypeEnum.SIGNAL == parentTypeEnum){
            orderSkuList = orderSkuDao.findByOrderNo(orderInfo.getOrderNo());
            mergeOrerSkus = this.mergeOrderSkuNo(orderSkuList);

            OrderTypeV2Enum orderTypeV2Enum = OrderTypeV2Enum.getEnum(orderInfo);
            if(OrderTypeV2Enum.VIRTUAL_CARD_ORDER == orderTypeV2Enum || OrderTypeV2Enum.CYLE_ORDER == orderTypeV2Enum) {
                submitDelivery(orderInfo, customerUser, mergeOrerSkus, cpsId, deliveryFlag);
            }
        }

        if(OrderParentTypeEnum.CHILD == parentTypeEnum){
            orderInfo = orderInfoDao.findByOrderNo(orderInfo.getOrderParentNo());
            orderSkuList = orderSkuDao.findLikeByOrderNo(orderInfo.getOrderNo());
            mergeOrerSkus = this.mergeOrderSkuNo(orderSkuList);
        }

        List<String> orderAfterNoList = new ArrayList<>();
        List<OrderAfterSaleSku> orderAfterSaleSkuList = new ArrayList<>();
        List<OrderAfterSale> orderAfterSaleList = orderAfterSaleDao.findByFuzzyOrderNo(orderInfo.getOrderParentNo());
        if(CollectionUtils.isNotEmpty(orderAfterSaleList)){
            orderAfterNoList = StreamUtils.toList(orderAfterSaleList, OrderAfterSale::getOrderAfterNo);
            orderAfterSaleSkuList = orderAfterSaleSkuDao.findByOrderAfterNoList(orderAfterNoList);
        }
        OrderPay orderPay = orderPayDao.findByOrderPayNo(orderInfo.getOrderNo(), orderInfo.getOrderParentNo());
        String tradeNo = null == orderPay ? orderInfo.getOrderParentNo() : orderPay.getTradeNo();
        //构造有数请求参数
        YoushuOrderAddDetailReq req = OrderInfoUtil.buildYoushuOrderReq(orderInfo, mergeOrerSkus, customerUser, orderAfterSaleList, orderAfterSaleSkuList, cpsId, tradeNo);

        YoushuOrderAddDetailReq.TargetUrl targetUrl = new YoushuOrderAddDetailReq.TargetUrl();
        targetUrl.setUrl_miniprogram("packageA/pages/order/orderDetail/orderDetail?orderNo=" + orderInfo.getOrderParentNo());
        targetUrl.setMiniprogram_appid(wxMiniConfig.getAppId());
        targetUrl.setMiniprogram_username(wxMiniConfig.getUsername());
        req.setTarget_url(targetUrl);
        //上报有数
        if(OrderTypeEnum.isYoushuOrderAdd(orderInfo.getOrderType())){
            youshuUtil.addOrder(req);
        }
    }

    /**
     * 如果是电子卡和周期购订单，由于是直接完成订单的，在完成前要先上报1150【已支付待发货】状态
     */
    private void submitDelivery(OrderInfo orderInfo, CustomerUser customerUser, List<OrderSku> mergeOrerSkus, String cpsId, boolean deliveryFlag) {
        if(Objects.equals(orderInfo.getOrderStatus(), OrderStatusEnum.COMPLETED.getCode()) && deliveryFlag){
            OrderInfo orderInfoCopy = new OrderInfo();
            BeanUtils.copy(orderInfo, orderInfoCopy);
            orderInfoCopy.setOrderStatus(OrderStatusEnum.DELIVERY.getCode());
            OrderPay orderPay = orderPayDao.findByOrderPayNo(orderInfo.getOrderNo(), orderInfo.getOrderParentNo());
            String tradeNo = null == orderPay ? orderInfo.getOrderParentNo() : orderPay.getTradeNo();
            //构造有数请求参数
            YoushuOrderAddDetailReq req = OrderInfoUtil.buildYoushuOrderReq(orderInfoCopy, mergeOrerSkus, customerUser, null, null, cpsId, tradeNo);
            //上报有数
            if(OrderTypeEnum.isYoushuOrderAdd(orderInfo.getOrderType())){
                youshuUtil.addOrder(req);
            }
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        }
    }

    /**
     * 根据原始单号查询补发单
     * @param srcNo
     * @return
     */
    @Override
    public List<OrderInfo> selectAgainOrder(String srcNo) {
        return orderInfoDao.findAgainBySrcNo(srcNo);
    }

    @Override
    public List<OrderInfo> selectPaidOrder(Long userId) {
        return orderInfoDao.findPaidOrderByUserId(userId);
    }

    @Override
    public OrderInfo selectLpkByOrderNo(String orderNo) {
        return orderInfoDao.findLpkByOrderNo(orderNo);
    }

    @Override
    public List<OrderInfo> findDefaultCommentOrder(Date time) {
        return orderInfoDao.findDefaultCommentOrder(time);
    }

    @Override
    public List<OrderInfo> findByOrderParentNo(String orderNo) {
        return orderInfoDao.findByOrderParentNo(orderNo);
    }

    @Override
    public void batchUpdateComment(List<OrderInfo> list) {
        orderInfoDao.batchUpdateComment(list);
    }

    @Override
    public List<OrderDiscountVO> discountOrder(Long id) {
        return orderInfoDao.discountOrder(id);
    }

    @Override
    public List<OrderDiscountVO> discountNotOrder(Long id, List<Long> userIds) {
        return orderInfoDao.discountNotOrder(id, userIds);
    }

    @Override
    public Integer getOrderCount(Long userId, OrderCountTypeEnum typeEnum, Long id) {
        // 1.先从redis中获取，2.1redis有直接返回 2.2redis没有从数据库中获取并存入redis
        String key = OrderInfoUtil.USER_ORDER_COUNT + userId;
        String hash="";
        switch (typeEnum){
            case SPU:
                hash = "spuId:" + id;
                break;
            case SKU:
                hash = "skuId:" + id;
                break;
            default:
                break;
        }
        Integer orderCount = redisOperation.hget(key, hash);
        if(orderCount == null){
            orderCount = orderInfoDao.getOrderCount(userId, typeEnum, id);
            orderCount = orderCount == null ? 0 : orderCount;
            redisOperation.hset(key, hash, orderCount);
            redisOperation.expire(key, 30, TimeUnit.DAYS);
        }
        return orderCount;
    }

    @Override
    public void addOrderCount(OrderInfo orderInfo) {
        if(StringUtils.startsWith(orderInfo.getOrderNo(), "LPKZP")){
            log.info("订单那数量统计 . 奖品订单不计入订单统计 --> orderNo:{}", orderInfo.getOrderNo());
            return;
        }
        if(Objects.equals(orderInfo.getIsGift(), FlagEnum.YES.getCode())){
            log.info("订单那数量统计 . 赠品订单不计入订单统计 --> orderNo:{}", orderInfo.getOrderNo());
            return;
        }
        if(Objects.nonNull(orderInfo.getDiscountActivityId())){
            log.info("订单那数量统计 . 限时折扣订单不计入订单统计 --> orderNo:{}", orderInfo.getOrderNo());
            return;
        }
        List<OrderSku> skus = orderSkuDao.findByOrderNo(orderInfo.getOrderNo());
        if(CollectionUtils.isEmpty(skus)){
            log.info("订单那数量统计 . 没有订单商品 --> orderNo:{}", orderInfo.getOrderNo());
            return;
        }
        String key = OrderInfoUtil.USER_ORDER_COUNT + orderInfo.getUserId();
        for (OrderSku orderSku : skus) {
            String spuHash = "spuId:" + orderSku.getProductId();
            String skuHash = "skuId:" + orderSku.getSkuId();
            Integer spuOrderCount = this.getOrderCount(orderInfo.getUserId(), OrderCountTypeEnum.SPU, orderSku.getProductId());
            Integer skuOrderCount = this.getOrderCount(orderInfo.getUserId(), OrderCountTypeEnum.SKU, orderSku.getSkuId());
            redisOperation.hset(key, spuHash, spuOrderCount + orderSku.getCount());
            redisOperation.hset(key, skuHash, skuOrderCount + orderSku.getCount());
        }
    }

    @Override
    public void clearOrderCount(OrderInfo orderInfo) {
        List<OrderSku> skus = orderSkuDao.findByOrderNo(orderInfo.getOrderNo());
        if(CollectionUtils.isEmpty(skus)){
            log.info("订单那数量清除 . 没有订单商品 --> orderNo:{}", orderInfo.getOrderNo());
            return;
        }
        String key = OrderInfoUtil.USER_ORDER_COUNT + orderInfo.getUserId();
        for (OrderSku orderSku : skus) {
            String spuHash = "spuId:" + orderSku.getProductId();
            String skuHash = "skuId:" + orderSku.getSkuId();
            redisOperation.hdel(key, spuHash);
            redisOperation.hdel(key, skuHash);
        }
    }
    @Override
    public void clearOrderCount(Long userId){
        redisOperation.del(OrderInfoUtil.USER_ORDER_COUNT + userId);
    }

    @Override
    public Date findMaxCreateTime(Long ruleId) {
        return orderInfoDao.findMaxCreateTime(ruleId);
    }

    @Override
    public List<OrderInfo> findOrderList(Long ruleId) {
        return orderInfoDao.findOrderList(ruleId);
    }

    @Override
    public Integer findOldUserCnt(Long ruleId) {
        return orderInfoDao.findOldUserCnt(ruleId);
    }

    @Override
    public Integer findNewUserCnt(Long ruelId) {
        return orderInfoDao.findNewUserCnt(ruelId);
    }

    @Override
    public List<OrderInfo> findOrderList4FreeTrial(Long freeTrialId) {
        return orderInfoDao.findOrderList4FreeTrial(freeTrialId);
    }

    @Override
    public Integer findOldUserCnt4FreeTrial(Long freeTrialId) {
        return orderInfoDao.findOldUserCnt4FreeTrial(freeTrialId);
    }

    @Override
    public Integer findNewUserCnt4FreeTrial(Long freeTrialId) {
        return orderInfoDao.findNewUserCnt4FreeTrial(freeTrialId);
    }

    @Override
    public void reportVolcengine(List<OrderEventDTO> orderEventDTOList) {
        List<Map<String, Object>> params = new ArrayList<>();
        for (OrderEventDTO orderEventDTO: orderEventDTOList) {
            String orderNo = orderEventDTO.getOrderNo();
            String event = orderEventDTO.getEvent();
            if(StringUtils.isBlank(orderNo)){
                log.error("OrderManagerImpl#reportVolcengine. orderNo不能为空");
                return;
            }
            OrderInfo order = orderInfoDao.findByOrderNo(orderNo);
            String orderParentNo = order.getOrderParentNo();
            List<OrderInfo> orderInfoList = new ArrayList<>();
            if(OrderEventEnum.ORDER_REFUND.getCode().equals(event)){
                orderInfoList = Arrays.asList(order);
            }else{
                orderInfoList = orderInfoDao.findChildByOrderParentNo(orderParentNo);
            }
            List<String> orderNoList = StreamUtils.toList(orderInfoList, OrderInfo::getOrderNo);
            if(CollectionUtils.isEmpty(orderNoList)){
                log.error("OrderManagerImpl#reportVolcengine. orderNoList不能为空");
                return;
            }
            List<OrderSku> orderSkuList = orderSkuDao.findByOrderNoList(orderNoList);
            List<Long> orderSkuIds = StreamUtils.toList(orderSkuList, OrderSku::getId);
            Map<String, OrderSku> orderSkuMap = StreamUtils.toMap(orderSkuList, OrderSku::getOrderNo);
            //订单来源信息
            Map<Long, OrderSkuSence> orderSkuSenceMap = new HashMap<>();
            List<OrderSkuSence> orderSkuSenceList = orderSkuSenceDao.findByTargetIds(orderSkuIds, 2);
            orderSkuSenceMap = StreamUtils.toMap(orderSkuSenceList, OrderSkuSence::getTargetId);

            OrderInfo orderInfo = StreamUtils.getFirst(orderInfoList);
            //优惠券信息
            CouponInfoDTO couponInfo = null;
            if(BasicFlagEnum.YES.getKey().equals(orderInfo.getUseCouponStatus())){
                List<CouponInfo> couponInfoList = couponInfoDao.queryByOrderNo(orderInfo.getOrderParentNo(), CouponInfoTypeEnum.PRICE_COUPON.getCode());
                CouponInfo coupon = StreamUtils.getFirst(couponInfoList);
                if(null != coupon){
                    CouponRule couponRule = couponRuleDao.findById(coupon.getRuleId());
                    couponInfo = new CouponInfoDTO();
                    couponInfo.setId(coupon.getId());
                    couponInfo.setRuleId(coupon.getRuleId());
                    if(null != couponRule){
                        couponInfo.setCouponName(couponRule.getCouponName());
                    }
                }
            }
            //md5加密后的openId
            CustomerUser customerUser = customerUserDao.findById(orderInfo.getUserId());
            String openId = customerUser.getOpenId();
            String openIdMd5 = DigestUtils.md5Hex(StringUtils.isBlank(openId) ? "ryytn" : openId).substring(0,28);

            List<String> json = buildReportParams(orderInfoList, orderSkuMap, orderSkuSenceMap, couponInfo);
            Date localTime = new Date();
            //不同事件时间戳取值不同
            if(OrderEventEnum.ORDER_REFUND.getCode().equals(event)){
                List<OrderAfterSale> afterSaleList = orderAfterSaleDao.findByStatusAndOrderNo(orderInfo.getOrderNo(), Arrays.asList(OrderAfterStatusEnum.REFUNDED.getCode()));
                OrderAfterSale orderAfterSale = StreamUtils.getFirst(afterSaleList);
                if(null == orderAfterSale){
                    continue;
                }
                localTime = Objects.isNull(orderAfterSale.getRefundTime()) ? orderAfterSale.getCreateTime() : orderAfterSale.getRefundTime();
            }else if(OrderEventEnum.ORDER_CREATE.getCode().equals(event)){
                localTime = orderInfo.getCreateTime();
            }else if(OrderEventEnum.ORDER_SUCCESS.getCode().equals(event)){
                localTime = orderInfo.getPayTime();
            }
            Map<String, Object> param = dataFinderManager.buildParams(event, openIdMd5, localTime, json);
            params.add(param);
        }
        if(CollectionUtils.isNotEmpty(params)){
            dataFinderManager.report(params);
        }
    }

    @Override
    public OrderInfo findById(Long id) {
        return orderInfoDao.findById(id);
    }


    private List<String> buildReportParams(List<OrderInfo> orderInfoList, Map<String, OrderSku> orderSkuMap, Map<Long, OrderSkuSence> orderSkuSenceMap, CouponInfoDTO couponInfo) {
        List<String> jsonList = new ArrayList<>();
        for (OrderInfo order: orderInfoList) {
            OrderSku sku = orderSkuMap.get(order.getOrderNo());
            Map<String, Object> map = new HashMap<>();
            map.put("count", sku.getCount());
            if(null != couponInfo){
                map.put("coupon_id", couponInfo.getId());
                map.put("coupon_name", couponInfo.getCouponName());
                map.put("coupon_rule_id", couponInfo.getRuleId());
            }
            if(null != order.getDeliveryTime()){
                map.put("if_packed", "是");
            }else{
                map.put("if_packed", "否");
            }
            map.put("order_no", order.getOrderParentNo());
            map.put("order_sub_no", order.getOrderNo());
            map.put("order_type", OrderTypeEnum.getDescByCode(order.getOrderType()));
            map.put("origin_price", sku.getOriginPrice());
            map.put("product_type", OrderProductTypeEnum.getDescByCode(order.getProductType()));
            map.put("real_amount", order.getRealAmount());
            map.put("real_price", sku.getRealAmount().divide(BigDecimal.valueOf(sku.getCount()), 2, BigDecimal.ROUND_HALF_UP));
            map.put("sku_code", sku.getSkuCode());
            map.put("sku_id", sku.getSkuId());
            map.put("sku_name", sku.getProductName());
            map.put("spu_id", sku.getProductId());
            map.put("spu_name", sku.getProductName());
            map.put("order_create_type", Objects.equals(BasicFlagEnum.YES.getKey(), order.getIsShopCart()) ? "购物车下单" : "直接下单");

            OrderSkuSence orderSkuSence = orderSkuSenceMap.get(sku.getId());
            if(null != orderSkuSence){
                map.put("order_sence", orderSkuSence.getCurrentPath());
                map.put("order_sence_desc", orderSkuSence.getCurrentDesc());
                map.put("order_source", orderSkuSence.getReferPath());
                map.put("order_source_desc", orderSkuSence.getReferQuery());
                map.put("user_launch_sence", orderSkuSence.getUserLaunchSence());
                map.put("user_launch_query", orderSkuSence.getUserLaunchQuery());
            }
            map.put("total_amount", order.getTotalAmount()) ;
            jsonList.add(JSON.toJSONString(map));
        }
        return jsonList;
    }

    private OrderWriteOffOmsCreateDTO writeOffAmountDistribution(Card card, Map<String, PlanPercentageDTO> skuPercenMap, BigDecimal writeOffAmount) {
        OrderWriteOffOmsCreateDTO planWriteOffDTO = new OrderWriteOffOmsCreateDTO();
        planWriteOffDTO.setCardCode(card.getCardNumber());
        planWriteOffDTO.setCpCShopEcode(card.getShopNo());
        int count = 0;
        int sumCount = skuPercenMap.size();
        BigDecimal sumWriteOffAmount = BigDecimal.ZERO;
        List<OrderWriteOffDetailOmsDTO> writeOffDetailList = new ArrayList<>();
        for (Map.Entry<String, PlanPercentageDTO> entry : skuPercenMap.entrySet()) {
            count ++;
            OrderWriteOffDetailOmsDTO writeOffDetailDTO = new OrderWriteOffDetailOmsDTO();
            writeOffDetailDTO.setSkuECode(entry.getKey());
            writeOffDetailDTO.setQty(entry.getValue().getSkuMilkAmount());
            if(count == sumCount){
                writeOffDetailDTO.setOffsetAmount(writeOffAmount.subtract(sumWriteOffAmount));
            }else{
                writeOffDetailDTO.setOffsetAmount(writeOffAmount.multiply(entry.getValue().getPercentage()).setScale(2,
                        BigDecimal.ROUND_HALF_UP));
            }
            if(BigDecimal.ZERO.compareTo(writeOffDetailDTO.getOffsetAmount()) == 0){
                continue;
            }
            writeOffDetailList.add(writeOffDetailDTO);
            sumWriteOffAmount = sumWriteOffAmount.add(writeOffDetailDTO.getOffsetAmount());
        }
        planWriteOffDTO.setSkuModels(writeOffDetailList);
        return planWriteOffDTO;
    }

    private OrderWriteOffOmsCreateDTO writeOffAmountDistribution(Card card, Map<String, PlanPercentageDTO> skuPercenMap, BigDecimal writeOffAmount, String lastSkuCode, BigDecimal remainingAmount) {
        OrderWriteOffOmsCreateDTO planWriteOffDTO = new OrderWriteOffOmsCreateDTO();
        planWriteOffDTO.setCardCode(card.getCardNumber());
        planWriteOffDTO.setCpCShopEcode(card.getShopNo());
        List<OrderWriteOffDetailOmsDTO> writeOffDetailList = new ArrayList<>();
        for (Map.Entry<String, PlanPercentageDTO> entry : skuPercenMap.entrySet()) {
            if(entry.getKey().equals(lastSkuCode)){
                OrderWriteOffDetailOmsDTO writeOffDetailDTO = new OrderWriteOffDetailOmsDTO();
                writeOffDetailDTO.setSkuECode(entry.getKey());
                writeOffDetailDTO.setQty(entry.getValue().getSkuMilkAmount());
                writeOffDetailDTO.setOffsetAmount(writeOffAmount.subtract(remainingAmount));
                writeOffDetailList.add(writeOffDetailDTO);
            }
        }
        planWriteOffDTO.setSkuModels(writeOffDetailList);
        return planWriteOffDTO;
    }

    private List<OrderSkuInventoryVo> inventoryFormInBackup(String date, ReportFormListDTO dto) {
        Date divisionTime = DateUtil.stringToDate(divisionDate, DateUtil.SIMPLE_YMD);
        if(dto.getSearchTimeEnd().before(divisionTime)){
            //备份表
            return orderSkuDao.inventoryFormInBackup(date, dto);
        }else{
            //拉链表
            return orderSkuDao.inventoryFormInZipper(date, dto);
        }
    }
}
