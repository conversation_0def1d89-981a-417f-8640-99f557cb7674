package com.hengtiansoft.order.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.hengtiansoft.address.entity.po.UserReceiveAddress;
import com.hengtiansoft.address.entity.vo.AddressDetailVO;
import com.hengtiansoft.address.entity.vo.AddressVO;
import com.hengtiansoft.address.manager.AddressManager;
import com.hengtiansoft.common.entity.exception.BusinessException;
import com.hengtiansoft.common.entity.vo.PageVO;
import com.hengtiansoft.common.enumeration.BasicFlagEnum;
import com.hengtiansoft.common.enumeration.FlagEnum;
import com.hengtiansoft.common.enumeration.OrderPlatformEnum;
import com.hengtiansoft.common.redis.RedisOperation;
import com.hengtiansoft.common.util.BeanUtils;
import com.hengtiansoft.common.util.DateUtil;
import com.hengtiansoft.common.util.PageUtils;
import com.hengtiansoft.common.util.StreamUtils;
import com.hengtiansoft.item.dao.*;
import com.hengtiansoft.item.entity.dto.*;
import com.hengtiansoft.item.entity.po.*;
import com.hengtiansoft.item.entity.vo.DiscountActivityVO;
import com.hengtiansoft.item.enumeration.*;
import com.hengtiansoft.item.interfaces.ProductManager;
import com.hengtiansoft.item.interfaces.SkuManager;
import com.hengtiansoft.item.interfaces.StockManager;
import com.hengtiansoft.item.manager.CardCategoryManager;
import com.hengtiansoft.item.utils.CateNameContext;
import com.hengtiansoft.item.utils.DiscountUtil;
import com.hengtiansoft.item.utils.SkuUtil;
import com.hengtiansoft.order.adapter.OrderSyncAdapter;
import com.hengtiansoft.order.dao.*;
import com.hengtiansoft.order.entity.common.*;
import com.hengtiansoft.order.entity.dto.*;
import com.hengtiansoft.order.entity.po.*;
import com.hengtiansoft.order.enums.*;
import com.hengtiansoft.order.manager.CouponRuleManager;
import com.hengtiansoft.order.manager.OrderAddressManager;
import com.hengtiansoft.order.manager.OrderManager;
import com.hengtiansoft.order.manager.OrderSkuManager;
import com.hengtiansoft.order.service.CommonOrderOptService;
import com.hengtiansoft.order.service.OrderRemoteService;
import com.hengtiansoft.order.service.PeopleLimitVerifyManger;
import com.hengtiansoft.order.util.CommonOrderUtil;
import com.hengtiansoft.order.util.CouponUtil;
import com.hengtiansoft.privilege.dao.*;
import com.hengtiansoft.privilege.entity.dto.*;
import com.hengtiansoft.privilege.entity.po.*;
import com.hengtiansoft.privilege.entity.vo.FullReduceInfo;
import com.hengtiansoft.privilege.enums.*;
import com.hengtiansoft.privilege.manager.FreeTrialProductManager;
import com.hengtiansoft.privilege.manager.FreeTrialUserManager;
import com.hengtiansoft.privilege.manager.FullReduceManager;
import com.hengtiansoft.privilege.manager.PeopleManager;
import com.hengtiansoft.privilege.util.FullReduceRuleUtil;
import com.hengtiansoft.user.dao.CustomerUserDao;
import com.hengtiansoft.user.dao.CustomerUserMoreDao;
import com.hengtiansoft.user.dao.CustomerUserMoreProductDao;
import com.hengtiansoft.user.dao.UserVisitDetailDao;
import com.hengtiansoft.user.entity.dto.CustomerUserByPhoneDTO;
import com.hengtiansoft.user.entity.po.CustomerUser;
import com.hengtiansoft.user.entity.po.CustomerUserMore;
import com.hengtiansoft.user.entity.po.CustomerUserMoreProduct;
import com.hengtiansoft.user.entity.po.UserVisitDetail;
import com.hengtiansoft.user.entity.vo.CustomerUserMoreVO;
import com.hengtiansoft.user.entity.vo.CustomerUserVO;
import com.hengtiansoft.user.enums.UserLifeCycleEnum;
import com.hengtiansoft.user.enums.UserMoreUpdateTypeEnum;
import com.hengtiansoft.user.manager.CustomerUserManager;
import com.hengtiansoft.user.manager.CustomerUserMoreManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.MutablePair;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class CommonOrderOptServiceImpl implements CommonOrderOptService {
    @Resource
    private RedisOperation redisOperation;
    @Resource
    private CustomerUserDao customerUserDao;
    @Resource
    private OrderInfoDao orderInfoDao;
    @Resource
    private PeopleManager peopleManager;
    @Resource
    private ProductManager productManager;
    @Resource
    private CouponRuleManager couponRuleManager;
    @Resource
    private CouponRangeDao couponRangeDao;
    @Resource
    private FullReduceManager fullReduceManager;
    @Resource
    private FullReduceRuleDao fullReduceRuleDao;
    @Resource
    private FullReduceRuleRangeDao fullReduceRuleRangeDao;
    @Resource
    private ProductDao productDao;
    @Resource
    private CateNameContext cateNameContext;
    @Resource
    private CardCategoryManager cardCategoryManager;
    @Resource
    private SkuManager skuManager;
    @Resource
    private SkuAttrDao skuAttrDao;
    @Resource
    private OrderSyncAdapter orderSyncAdapter;
    @Resource
    private OrderManager orderManager;
    @Resource
    private OrderSkuManager orderSkuManager;
    @Resource
    private OrderAddressManager orderAddressManager;
    @Resource
    private StockManager stockManager;
    @Resource
    private OrderRemoteService orderRemoteService;
    @Resource
    private FreeTrialProductManager freeTrialProductManager;
    @Resource
    private FreeTrialUserManager freeTrialUserManager;
    @Resource
    private CustomerUserManager customerUserManager;
    @Resource
    private AddressManager addressManager;
    @Resource
    private CustomerUserMoreManager customerUserMoreManager;
    @Resource
    private MessageSubscribeDao messageSubscribeDao;
    @Resource
    private PeopleLimitVerifyManger peopleLimitVerifyManger;
    @Resource
    private OrderSkuDao orderSkuDao;
    @Resource
    private ItemProductGroupDao itemProductGroupDao;
    @Resource
    private CardDao cardDao;
    @Resource
    private CustomerUserMoreProductDao customerUserMoreProductDao;
    @Resource
    private CouponInfoDao couponInfoDao;
    @Resource
    private TaskRecordDao taskRecordDao;
    @Resource
    private FreeTrialVoteUserDao freeTrialVoteUserDao;
    @Resource
    private PtSubOrderDao ptSubOrderDao;
    @Resource
    private UserVisitDetailDao userVisitDetailDao;
    @Resource
    private CustomerUserMoreDao customerUserMoreDao;
    @Resource
    private FreeTrialUserDao freeTrialUserDao;
    @Resource
    private SwapOrderDao swapOrderDao;
    @Resource
    private GroupTemplateDao groupTemplateDao;

    @Override
    public PageVO<ProductVO> listWithMarket(ProductSearchDTO dto, CustomerUserVO user) {
        //skuCode
        if (StringUtils.isNotEmpty(dto.getSkuCode())) {
            List<String> skuCodes = new ArrayList<>();
            skuCodes.add(dto.getSkuCode());
            List<SkuBaseDTO> skuBaseDTOs = skuManager.detailsBySkuCodes(skuCodes);
            List<Long> skuIds = StreamUtils.toList(skuBaseDTOs, SkuBaseDTO::getId);
            if (CollectionUtils.isEmpty(skuIds)) {
                return PageUtils.emptyPage(dto);
            }
            dto.setSkuIds(skuIds);
        }
        //商品分组
        if (CollectionUtils.isNotEmpty(dto.getGroupIds())) {
            List<ItemProductGroup> groups = itemProductGroupDao.selectByGroupIds(dto.getGroupIds());
            if (CollectionUtils.isEmpty(groups)) {
                return PageUtils.emptyPage(dto);
            }
            List<Long> ids = StreamUtils.convertDistinct(groups, ItemProductGroup::getProductId, Long::compareTo);
            if (CollectionUtils.isNotEmpty(dto.getProductIds())) {
                dto.getProductIds().retainAll(ids);
                if(CollectionUtils.isEmpty(dto.getProductIds())){
                    return PageUtils.emptyPage(dto);
                }
            } else {
                dto.setProductIds(ids);
            }
        }
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        //查询商品主表
        List<Product> productList = productDao.findByConditionSort(dto);

        // List<ProductVO> productVoList = BeanUtils.deepListCopy(productList,ProductVO.class);
        // List<ProductVO> productVoList = Lists.newArrayList();
        if (CollectionUtils.isEmpty(productList)) {
            return PageUtils.emptyPage(dto);
        }

        // 使用id进行排序
        if(CollectionUtils.isNotEmpty(dto.getProductIds())){
            Map<Long, Product> map = StreamUtils.toMap(productList, Product::getId);
            productList = dto.getProductIds().stream().map(map::get).filter(Objects::nonNull).collect(Collectors.toList());
        }

        CouponRuleListDTO ruleDto = new CouponRuleListDTO();
        ruleDto.setChannel(CouponChannelTypeEnum.WECHAT_MINI.getCode());
        ruleDto.setIsPublic(CouponPublicTypeEnum.PUBLIC.getCode());
        ruleDto.setStatus(CouponStatusTypeEnum.ONLINE.getCode());
        ruleDto.setEndTimeStart(new Date());
        ruleDto.setCouponType(CouponTypeEnum.PRODUCT_COUPON.getCode());


        //查询可领+已领的优惠券
        List<CouponRule> couponRuleList = couponRuleManager.queryEnableReceiveAndReceivedCoupon(ruleDto, user);
        List<Long> ruleIds = StreamUtils.convert(couponRuleList, CouponRule::getId);
        List<CouponRange> couponRangeList = couponRangeDao.queryByRuleIds(ruleIds);
        Map<Long,List<CouponRange>> couponRangeMap = StreamUtils.group(couponRangeList, CouponRange::getRuleId);

        Map<Long, List<SkuBaseDTO>> skuMap = skuManager.getSkuMapByProductIds(StreamUtils.toList(productList, Product::getId));

        PageVO<ProductVO> productVoList = PageUtils.convert(productList, product -> {
            ProductVO productVO = BeanUtils.copy(product, ProductVO::new);
            productVO.setSkus(BeanUtils.copyList(skuMap.get(productVO.getId()), SkuVO::new));
            //组装skuAttr
            List<Long> skuIds = productVO.getSkus().stream().map(SkuVO::getId).collect(Collectors.toList());
            Map<Long, List<SkuAttrDTO>> skuAttrMap = StreamUtils.mapGroup(skuAttrDao.selectBySkuIds(skuIds),
                    e -> BeanUtils.copy(e, SkuAttrDTO::new), SkuAttrDTO::getSkuId);
            for (SkuVO sku : productVO.getSkus()) {
                sku.setSkuAttrs(skuAttrMap.get(sku.getId()));
            }

            if(dto.isGroupFlag()){
                //分组
                List<ItemProductGroup> productGroupList = itemProductGroupDao.selectByProductId(product.getId());
                List<Long> groupIds = StreamUtils.toList(productGroupList, ItemProductGroup::getGroupId);
                if(CollectionUtils.isNotEmpty(groupIds)){
                    List<GroupTemplate> groupTemplateList = groupTemplateDao.findByIds(groupIds);
                    List<String> groupNameList = new ArrayList<>();
                    for (GroupTemplate groupTemplate: groupTemplateList) {
                        groupNameList.add(groupTemplate.getName());
                    }
                    productVO.setGroupNameList(groupNameList);
                }
            }

            //促销活动信息
            buildProdctDiscountActivity(productVO, user, true, null);
            //满减满送
            buildProductFullReduce(productVO, user, false, null, 1, null, productVO.getCycleFlag());
            //优惠券信息
            buildProdctAllCouponList(productVO, couponRuleList, couponRangeMap);

            return productVO;
        });
        productVoList.setList(buildProductVO(productVoList.getList(), dto));
        return productVoList;
    }

    /**
     * Description: 构建商品Vo
     *
     * @param list
     * @param dto
     * @return
     */
    private List<ProductVO> buildProductVO(List<ProductVO> list, ProductSearchDTO dto) {
        List<ProductVO> productVOs = new ArrayList<>();
        for (ProductVO productVO : list) {
            productVO.setCateName(cateNameContext.getName(productVO.getCateId()));
            if (ProductTypeEnum.PACKAGE.getCode().equals(productVO.getProductType())){
                if (Objects.nonNull(productVO.getCardCategoryId())){
                    Long cardCategoryId = productVO.getCardCategoryId();
                    if (Objects.nonNull(cardCategoryId)){
                        CardCategory cardCategory = cardCategoryManager.selectById(cardCategoryId);
                        productVO.setCategoryName(productVO.getCateName());
                        productVO.setCateName(cardCategory.getCategoryName());
                    }
                }
            }
            productVOs.add(productVO);
        }
        return productVOs;
    }

    public void skuMergeSkuAttr(SkuVO vo, List<SkuAttrDTO> skuAttrs) {
        if (CollectionUtils.isEmpty(skuAttrs)) {
            return;
        }
        for (SkuAttrDTO attr : skuAttrs) {
            if (StringUtils.isNotEmpty(attr.getAttrValue())) {
                switch (attr.getAttrName()) {
                    case "ownerId":
                        vo.setOwnerId(attr.getAttrValue());
                        break;
                    case "safeStockQte":
                        vo.setSafeStockQte(Long.valueOf(attr.getAttrValue()));
                        break;
                    case "shipmentType":
                        vo.setShipmentType(Integer.valueOf(attr.getAttrValue()));
                        break;
                    case "purchase":
                        vo.setPurchase(Integer.valueOf(attr.getAttrValue()));
                        break;
                    case "times":
                        if(Objects.isNull(vo.getTimesList())){
                            vo.setTimesList(new ArrayList<>());
                        }
                        vo.getTimesList().add(new TimesVO(Integer.valueOf(attr.getAttrValue()), new BigDecimal(attr.getAttrSpecValue()), attr.getSort()));
                        break;
                    default:
                        break;
                }
            }
        }
        //周期排序
        if(CollectionUtils.isNotEmpty(vo.getTimesList())){
            vo.setTimesList(vo.getTimesList().stream().sorted(Comparator.comparing(TimesVO::getSort)).collect(Collectors.toList()));
        }
    }

    @Override
    public void buildProdctDiscountActivity(ProductVO productVO, CustomerUserVO customerUserVO, boolean noticeFlag, Long skuId) {
        List<SkuVO> skuList = productVO.getSkus();
        List<SkuDiscountActivityDTO> discountActivityList = new ArrayList<>();
        for (SkuVO skuVO : skuList) {
            //指定sku
            if(null != skuId && !skuId.equals(skuVO.getId())){
                continue;
            }
            String key = DiscountUtil.DISCOUNT_ACTIVITY_KEY + skuVO.getId();
            Map<Object, Object> map = redisOperation.hgetAll(key);
            List<SkuDiscountActivityDTO> skuDiscountActivityList = new ArrayList<>();
            for (Map.Entry<Object, Object> entry : map.entrySet()) {
                String jsonStr = String.valueOf(entry.getValue());
                if(StringUtils.isBlank(jsonStr)){
                    continue;
                }
                SkuDiscountActivityDTO skuDiscountActivityDTO = JSON.parseObject(jsonStr, SkuDiscountActivityDTO.class);
                skuDiscountActivityList.add(skuDiscountActivityDTO);
            }

            //人群限制-过滤不可用的活动
            skuDiscountActivityList = this.filterPeopleLimit(skuDiscountActivityList, customerUserVO);
            discountActivityList.addAll(skuDiscountActivityList);

            //进行中的活动
            SkuDiscountActivityDTO skuActivity = inProgressActivity(skuDiscountActivityList);
            if(noticeFlag && null == skuActivity){
                //待开始且预告
                skuActivity = noticeActivity(skuDiscountActivityList);
            }
            if(null != skuActivity){
                skuVO.setDiscountPrice(skuActivity.getDiscountPrice());
                skuVO.setDiscountActivityId(skuActivity.getId());
                skuVO.setSharingDiscount(skuActivity.getSharingDiscount());
                skuVO.setPeopleLimit(skuActivity.getPeopleLimit());
                skuVO.setDiscountActivityStatus(skuActivity.getStatus());
            }
        }

        //进行中
        SkuDiscountActivityDTO skuActivity = inProgressActivity(discountActivityList);
        //没有进行中的折扣活动
        if(noticeFlag && null == skuActivity){
            //待开始且预告
            skuActivity = noticeActivity(discountActivityList);
        }
        if(null != skuActivity){
            DiscountActivityVO discountActivityVO = new DiscountActivityVO();
            discountActivityVO.setStartTime(skuActivity.getStartTime().getTime());
            discountActivityVO.setEndTime(skuActivity.getEndTime().getTime());
            discountActivityVO.setStatus(skuActivity.getStatus());
            discountActivityVO.setLabel(skuActivity.getLabel());
            discountActivityVO.setId(skuActivity.getId());
            productVO.setDiscountActivityVO(discountActivityVO);
        }
    }

    private void buildProductFullReduce(ProductVO productVO,
                                        CustomerUserVO user,
                                        boolean infoFlag,
                                        Long skuId,
                                        Integer count,
                                        Integer times,
                                        Integer cycleFlag) {
        //获取满减活动
        FullReduce fullReduce = fullReduceManager.getFullReduceFromRedis(productVO.getId());
        if(null == fullReduce){
            return;
        }
        //校验人群
        if(!verifyPeopleLimit(fullReduce, user)){
            return;
        }
        //遍历sku
        for (SkuVO skuVO: productVO.getSkus()) {
            //指定sku
            if(null != skuId && !skuId.equals(skuVO.getId())){
                continue;
            }
            BigDecimal price = skuVO.getSalePrice();
            boolean isShareDiscount = FullReduceShareTypeEnum.isShareDiscount(fullReduce.getShareType());
            //sku有折扣信息
            if(null != skuVO.getDiscountActivityId()){
                if(DiscountActivityStatusEnum.IN_PROGRESS.getCode().equals(skuVO.getDiscountActivityStatus())){
                    //满减与折扣不共享且限时折扣进行中
                    if(!isShareDiscount){
                        continue;
                    }
                    price = Objects.isNull(skuVO.getDiscountPrice())? skuVO.getSalePrice(): skuVO.getDiscountPrice();
                }
            }
            //获取用来计算满减门槛的价格
            SkuPriceSumDTO skuPriceSumDTO = this.getSkuSalePrice(skuVO, cycleFlag, price, count, times);
            //计算满减价格
            FullReduceVO fullReduceVO = new FullReduceVO();
            //周期购
            if(null != skuPriceSumDTO.getCycleSalePriceSum()){
                BigDecimal fullReducePrice = calculateFullReducePrice(fullReduce, cycleFlag, count, skuPriceSumDTO.getCycleSalePriceSum());
                if(null != fullReducePrice){
                    if(BasicFlagEnum.YES.getKey().equals(cycleFlag)){
                        fullReduceVO.setSumFullReducePrice(fullReducePrice);
                    }
                    fullReduceVO.setId(fullReduce.getId());
                    fullReduceVO.setShareType(fullReduce.getShareType());
                    if(null != skuPriceSumDTO.getCycleMax()){
                        fullReduceVO.setCycleMax(skuPriceSumDTO.getCycleMax());
                        fullReduceVO.setCycleSingleFullReducePrice(fullReducePrice.divide(skuPriceSumDTO.getCycleMax(), 2, BigDecimal.ROUND_UP).divide(BigDecimal.valueOf(count), 2, BigDecimal.ROUND_UP));
                    }
                    skuVO.setFullReduce(fullReduceVO);
                }
            }
            //单次购买
            if(null != skuPriceSumDTO.getSalePriceSum()){
                BigDecimal fullReducePrice = calculateFullReducePrice(fullReduce, cycleFlag, count, skuPriceSumDTO.getSalePriceSum());
                if(null != fullReducePrice){
                    if(BasicFlagEnum.NO.getKey().equals(cycleFlag)){
                        fullReduceVO.setSumFullReducePrice(fullReducePrice);
                    }
                    fullReduceVO.setId(fullReduce.getId());
                    fullReduceVO.setShareType(fullReduce.getShareType());
                    fullReduceVO.setFullReducePrice(fullReducePrice.divide(BigDecimal.valueOf(count), 2, BigDecimal.ROUND_UP));
                    skuVO.setFullReduce(fullReduceVO);
                }
            }
            if(null == productVO.getFullReduce()){
                List<String> labelList = getFullReduceLabel(fullReduce);
                FullReduceMallDTO fullReduceMallDTO = BeanUtils.deepCopy(fullReduce, FullReduceMallDTO.class);
                fullReduceMallDTO.setLabelList(labelList);
                if(infoFlag){
                    List<FullReduceInfo> fullReduceInfo = getFullReduceCompleteLabel(fullReduce);
                    fullReduceMallDTO.setFullReduceInfo(fullReduceInfo);
                }
                productVO.setFullReduce(fullReduceMallDTO);
            }
        }
    }

    private List<FullReduceInfo> getFullReduceCompleteLabel(FullReduce fullReduce) {
        List<FullReduceInfo> labelList = new ArrayList<>();
        List<FullReduceRule> ruleList = fullReduceRuleDao.findByFullId(fullReduce.getId(), "level asc");
        FullReduceRule fullReduceRule = StreamUtils.getFirst(ruleList);
        List<FullReduceRuleRange> reduceRuleRangeList = fullReduceRuleRangeDao.findByFullReduceId(fullReduce.getId());
        Map<Long, List<FullReduceRuleRange>> reduceRuleRangeMap = StreamUtils.group(reduceRuleRangeList, FullReduceRuleRange::getFullReduceRuleId);
        if(null != fullReduceRule){
            List<FullReduceRuleRange> ruleRangeList = reduceRuleRangeMap.get(fullReduceRule.getId());
            List<FullReduceRuleRange> giftList = StreamUtils.filter(ruleRangeList, x -> FullRuleRangeTypeEnum.GIFT.getCode().equals(x.getType()));
            List<FullReduceRuleRange> couponList = StreamUtils.filter(ruleRangeList, x -> FullRuleRangeTypeEnum.COUNPON.getCode().equals(x.getType()));
            FullReduceInfo info = getCompleteLabel(fullReduce, fullReduceRule, giftList, couponList);
            info.setStartTime(fullReduce.getStartTime());
            info.setEndTime(fullReduce.getEndTime());
            labelList.add(info);
        }
        return labelList;
    }

    private List<String> getFullReduceLabel(FullReduce fullReduce) {
        List<String> labelList = new ArrayList<>();
        List<String> priceLabelList = new ArrayList<>();
        List<String> couponLabelList = new ArrayList<>();
        List<String> giftLabelList = new ArrayList<>();
        List<FullReduceRule> ruleList = fullReduceRuleDao.findByFullId(fullReduce.getId(), "level asc");
        List<FullReduceRuleRange> reduceRuleRangeList = fullReduceRuleRangeDao.findByFullReduceId(fullReduce.getId());
        Map<Long, List<FullReduceRuleRange>> reduceRuleRangeMap = StreamUtils.group(reduceRuleRangeList, FullReduceRuleRange::getFullReduceRuleId);
        for (FullReduceRule fullReduceRule: ruleList) {
            List<FullReduceRuleRange> ruleRangeList = reduceRuleRangeMap.get(fullReduceRule.getId());
            List<FullReduceRuleRange> giftList = StreamUtils.filter(ruleRangeList, x -> FullRuleRangeTypeEnum.GIFT.getCode().equals(x.getType()));
            List<FullReduceRuleRange> couponList = StreamUtils.filter(ruleRangeList, x -> FullRuleRangeTypeEnum.COUNPON.getCode().equals(x.getType()));

            //减钱/打折
            if(null != FullRuleReduceTypeEnum.getEnumByCode(fullReduceRule.getReduceType())){
                priceLabelList.add(getLabel(fullReduce, fullReduceRule, FullRuleReduceModelEnum.REDUCE));
            }
            //赠品
            if(CollectionUtils.isEmpty(giftLabelList) && CollectionUtils.isNotEmpty(giftList)){
                giftLabelList.add(getLabel(fullReduce, fullReduceRule, FullRuleReduceModelEnum.GIFT));
            }
            //优惠券
            if(CollectionUtils.isEmpty(couponLabelList) && CollectionUtils.isNotEmpty(couponList)){
                couponLabelList.add(getLabel(fullReduce, fullReduceRule, FullRuleReduceModelEnum.COUPON));
            }
        }
        labelList.addAll(priceLabelList);
        labelList.addAll(couponLabelList);
        labelList.addAll(giftLabelList);
        return labelList;
    }

    private String getLabel(FullReduce fullReduce, FullReduceRule fullReduceRule, FullRuleReduceModelEnum modelEnum) {
        FullReduceTypeEnum reduceTypeEnum = FullReduceTypeEnum.getEnumByCode(fullReduce.getType());
        FullReduceDiscountRuleEnum discountRuleEnum = FullReduceDiscountRuleEnum.getEnumByCode(fullReduce.getDiscountRule());
        StringBuilder sb = new StringBuilder();
        switch (reduceTypeEnum){
            case FULL_MONEY:
                if(FullReduceDiscountRuleEnum.STEP == discountRuleEnum){
                    sb.append("满").append(fullReduceRule.getFullAmount().stripTrailingZeros().toPlainString()).append(modelEnum.getStr(fullReduceRule));
                }else if(FullReduceDiscountRuleEnum.LOOP == discountRuleEnum){
                    sb.append("每满").append(fullReduceRule.getFullAmount().stripTrailingZeros().toPlainString()).append(modelEnum.getStr(fullReduceRule));
                }
                break;
            case FULL_REAL_AMOUNT:
                if(FullReduceDiscountRuleEnum.STEP == discountRuleEnum){
                    sb.append("实付").append(fullReduceRule.getFullAmount().stripTrailingZeros().toPlainString()).append(modelEnum.getStr(fullReduceRule));
                }else if(FullReduceDiscountRuleEnum.LOOP == discountRuleEnum){
                    sb.append("实付每").append(fullReduceRule.getFullAmount().stripTrailingZeros().toPlainString()).append(modelEnum.getStr(fullReduceRule));
                }
                break;
            case FULL_COUNT:
                if(FullReduceDiscountRuleEnum.STEP == discountRuleEnum){
                    sb.append("满").append(fullReduceRule.getFullCount()).append("件").append(modelEnum.getStr(fullReduceRule));
                }else if(FullReduceDiscountRuleEnum.LOOP == discountRuleEnum){
                    sb.append("每满").append(fullReduceRule.getFullCount()).append("件").append(modelEnum.getStr(fullReduceRule));
                }
                break;
        }
        return sb.toString();
    }

    private FullReduceInfo getCompleteLabel(FullReduce fullReduce, FullReduceRule fullReduceRule, List<FullReduceRuleRange> giftList, List<FullReduceRuleRange> couponList) {
        FullReduceTypeEnum reduceTypeEnum = FullReduceTypeEnum.getEnumByCode(fullReduce.getType());
        FullReduceDiscountRuleEnum discountRuleEnum = FullReduceDiscountRuleEnum.getEnumByCode(fullReduce.getDiscountRule());
        Set<Integer> modelList = new HashSet<>();
        StringBuilder sb = new StringBuilder();
        switch (reduceTypeEnum){
            case FULL_MONEY:
                if(FullReduceDiscountRuleEnum.STEP == discountRuleEnum){
                    sb.append("满").append(fullReduceRule.getFullAmount().stripTrailingZeros().toPlainString());
                }else if(FullReduceDiscountRuleEnum.LOOP == discountRuleEnum){
                    sb.append("每满").append(fullReduceRule.getFullAmount().stripTrailingZeros().toPlainString());
                }
                break;
            case FULL_REAL_AMOUNT:
                if(FullReduceDiscountRuleEnum.STEP == discountRuleEnum){
                    sb.append("实付").append(fullReduceRule.getFullAmount().stripTrailingZeros().toPlainString());
                }else if(FullReduceDiscountRuleEnum.LOOP == discountRuleEnum){
                    sb.append("实付每").append(fullReduceRule.getFullAmount().stripTrailingZeros().toPlainString());
                }
                break;
            case FULL_COUNT:
                if(FullReduceDiscountRuleEnum.STEP == discountRuleEnum){
                    sb.append("满").append(fullReduceRule.getFullCount()).append("件");
                }else if(FullReduceDiscountRuleEnum.LOOP == discountRuleEnum){
                    sb.append("每满").append(fullReduceRule.getFullCount()).append("件");
                }
                break;
        }
        FullRuleReduceTypeEnum reduceType = FullRuleReduceTypeEnum.getEnumByCode(fullReduceRule.getReduceType());
        if(null != reduceType){
            if(FullRuleReduceTypeEnum.REDUCE == reduceType){
                sb.append("减").append(fullReduceRule.getReduceAmount().stripTrailingZeros().toPlainString()).append(",").toString();
                modelList.add(FullRuleReduceModelEnum.REDUCE.getCode());
            }else if(FullRuleReduceTypeEnum.DISCOUNT == reduceType){
                sb.append("打").append(fullReduceRule.getDiscount().stripTrailingZeros().toPlainString()).append("折,").toString();
                modelList.add(FullRuleReduceModelEnum.REDUCE.getCode());
            }
        }
        if(CollectionUtils.isNotEmpty(giftList)){
            sb.append("送1件赠品,");
            modelList.add(FullRuleReduceModelEnum.GIFT.getCode());
        }
        if(CollectionUtils.isNotEmpty(couponList)){
            sb.append("送1张优惠券,");
            //赠品/优惠券前端统一当作满赠
            modelList.add(FullRuleReduceModelEnum.GIFT.getCode());
        }
        sb.setLength(sb.length() - 1);
        FullReduceInfo fullReduceInfo = new FullReduceInfo();
        fullReduceInfo.setLabel(sb.toString());
        fullReduceInfo.setFullReduceId(fullReduce.getId());
        fullReduceInfo.setModel(new ArrayList<>(modelList));
        return fullReduceInfo;
    }

    @Override
    public BigDecimal calculateFullReducePrice(FullReduce fullReduce, Integer cycleFlag, Integer count, BigDecimal price) {
        //购买类型不符，不计算满减价格
        if(!this.checkBuyTypeWithCycleFlag(fullReduce.getBuyType(), cycleFlag)){
            return null;
        }
        List<FullReduceRule> ruleList = fullReduceRuleDao.findByFullId(fullReduce.getId(), "level desc");
        FullReduceTypeEnum reduceTypeEnum = FullReduceTypeEnum.getEnumByCode(fullReduce.getType());
        FullReduceDiscountRuleEnum ruleEnum = FullReduceDiscountRuleEnum.getEnumByCode(fullReduce.getDiscountRule());
        FullReduceRule reduceRule;
        BigDecimal fullReducePrice = null;
        FullRuleReduceTypeEnum fullRuleReduceTypeEnum;
        switch (reduceTypeEnum){
            case FULL_COUNT:
                reduceRule = FullReduceRuleUtil.getReduceRule(ruleList, ruleEnum, reduceTypeEnum, count, null);
                if(null == reduceRule){
                    return null;
                }
                fullRuleReduceTypeEnum = FullRuleReduceTypeEnum.getEnumByCode(reduceRule.getReduceType());
                if(null == fullRuleReduceTypeEnum){
                    return null;
                }
                if(FullReduceDiscountRuleEnum.LOOP == ruleEnum){
                    int n = count / reduceRule.getFullCount();
                    fullReducePrice = price.subtract(reduceRule.getReduceAmount().multiply(new BigDecimal(n)));
                }else{
                    if(FullRuleReduceTypeEnum.REDUCE == fullRuleReduceTypeEnum){
                        fullReducePrice = price.subtract(reduceRule.getReduceAmount());
                    }else if(FullRuleReduceTypeEnum.DISCOUNT == fullRuleReduceTypeEnum){
                        fullReducePrice = price.multiply(reduceRule.getDiscount()).divide(BigDecimal.TEN);
                    }
                }
                if(fullReducePrice.compareTo(BigDecimal.ZERO) <= 0){
                    fullReducePrice = new BigDecimal("0.01");
                }
                break;
            case FULL_MONEY:
            case FULL_REAL_AMOUNT:
                reduceRule = FullReduceRuleUtil.getReduceRule(ruleList, ruleEnum, reduceTypeEnum, null, price);
                if(null == reduceRule){
                    return null;
                }
                fullRuleReduceTypeEnum = FullRuleReduceTypeEnum.getEnumByCode(reduceRule.getReduceType());
                if(null == fullRuleReduceTypeEnum){
                    return null;
                }
                if(FullReduceDiscountRuleEnum.LOOP == ruleEnum){
                    int n = price.divide(reduceRule.getFullAmount(),0, BigDecimal.ROUND_DOWN).intValue();
                    fullReducePrice = price.subtract(reduceRule.getReduceAmount().multiply(new BigDecimal(n)));
                }else{
                    if(FullRuleReduceTypeEnum.REDUCE == fullRuleReduceTypeEnum){
                        fullReducePrice = price.subtract(reduceRule.getReduceAmount());
                    }else if(FullRuleReduceTypeEnum.DISCOUNT == fullRuleReduceTypeEnum){
                        fullReducePrice = price.multiply(reduceRule.getDiscount()).divide(BigDecimal.TEN);
                    }
                }
                if(fullReducePrice.compareTo(BigDecimal.ZERO) <= 0){
                    fullReducePrice = new BigDecimal("0.01");
                }
                break;
        }
        return fullReducePrice;
    }

    private boolean checkBuyTypeWithCycleFlag(Integer buyType, Integer cycleFlag){
        if(FullReduceBuyTypeEnum.NO_LIMIT.getCode().equals(buyType)){
            //购买类型不限
        }else if(FullReduceBuyTypeEnum.SINGLE.getCode().equals(buyType)){
            //单次购买券，单品支持周期购---若单品支持周期购 只展示周期购单提（券后）价，不用计算单品的单次价
            if(BasicFlagEnum.YES.getKey().equals(cycleFlag)){
                return false;
            }
        }else if(FullReduceBuyTypeEnum.CYCLE.getCode().equals(buyType)){
            //周期购买券，单品不支持周期购
            if(!BasicFlagEnum.YES.getKey().equals(cycleFlag)){
                return false;
            }
        }
        return true;
    }

    private SkuPriceSumDTO getSkuSalePrice(SkuVO skuVO, Integer cycleFlag, BigDecimal price, Integer count, Integer times){
        BigDecimal salePriceSum = null;
        BigDecimal cycleSalePriceSum = null;
        BigDecimal cycleMax = null;
        if(BasicFlagEnum.YES.getKey().equals(cycleFlag)){
            List<SkuAttrDTO> skuAttrList = skuVO.getSkuAttrs().stream().filter(skuAttr -> "times".equals(skuAttr.getAttrName())).collect(Collectors.toList());
            if(null != times){
                skuAttrList = StreamUtils.filter(skuAttrList, x-> String.valueOf(times).equals(x.getAttrValue()));
            }
            for (SkuAttrDTO skuAttrDTO: skuAttrList) {
                skuAttrDTO.setAttrValueInt(Integer.valueOf(skuAttrDTO.getAttrValue()));
            }
            //取期数最多的周期购
            skuAttrList = skuAttrList.stream().sorted(Comparator.comparing(SkuAttrDTO::getAttrValueInt).reversed()).collect(Collectors.toList());
            if(CollectionUtils.isEmpty(skuAttrList)){
                throw new BusinessException("周期购期数选择错误,请重新选择！");
            }else{
                SkuAttrDTO skuAttrDTO = StreamUtils.getFirst(skuAttrList);
                BigDecimal cyclePrice = new BigDecimal(skuAttrDTO.getAttrSpecValue());
                cycleMax = new BigDecimal(skuAttrDTO.getAttrValue());
                cycleSalePriceSum = cyclePrice.multiply(cycleMax).multiply(BigDecimal.valueOf(count));
            }
        }
        salePriceSum = SkuUtil.calculatePrice(price, count);
        return SkuPriceSumDTO.builder()
                .salePriceSum(salePriceSum)
                .cycleSalePriceSum(cycleSalePriceSum)
                .cycleMax(cycleMax)
                .build();
    }

    @Override
    public boolean verifyPeopleLimit(FullReduce fullReduce, CustomerUserVO user) {
        FullReducePeopleLimitEnum peopleLimitEnum = FullReducePeopleLimitEnum.getEnumByCode(fullReduce.getPeopleLimit());
        CustomerUser customerUser = BeanUtils.copy(user, CustomerUser::new);
        PeopleVerifyDTO verifyDTO = new PeopleVerifyDTO();
        verifyDTO.setPeopleLimit(peopleLimitEnum.getCode());
        verifyDTO.setGrade(fullReduce.getGrade());
        if(StringUtils.isNotBlank(fullReduce.getLabelId())){
            verifyDTO.setLabelIds(Arrays.stream(fullReduce.getLabelId().split(","))
                    .map(Long::valueOf)
                    .collect(Collectors.toList()));
        }
        return peopleLimitVerifyManger.peopleLimitVerify(verifyDTO, customerUser);
    }

    @Override
    public List<SkuDiscountActivityDTO> filterPeopleLimit(List<SkuDiscountActivityDTO> skuDiscountActivityList, CustomerUserVO customerUserVO) {
        //未登录返回无限制的促销活动
        CustomerUser customerUser = BeanUtils.copy(customerUserVO, CustomerUser::new);
        List<SkuDiscountActivityDTO> peopleLimitDiscountActivityList = new ArrayList<>();

        for (SkuDiscountActivityDTO activity: skuDiscountActivityList) {
            PeopleVerifyDTO verifyDTO = new PeopleVerifyDTO();
            // 限时折扣和其他活动的 3 对应不同  映射成通用
            verifyDTO.setPeopleLimit(activity.getPeopleLimit() == 3 ? 4 : activity.getPeopleLimit());
            if(StringUtils.isNotBlank(activity.getLabelId())){
                verifyDTO.setLabelIds(Arrays.stream(activity.getLabelId().split(","))
                        .map(Long::valueOf)
                        .collect(Collectors.toList()));
            }
            if(peopleLimitVerifyManger.peopleLimitVerify(verifyDTO, customerUser)){
                peopleLimitDiscountActivityList.add(activity);
            }
        }
        return peopleLimitDiscountActivityList;
    }

    private SkuDiscountActivityDTO inProgressActivity(List<SkuDiscountActivityDTO> discountActivityList) {
        List<SkuDiscountActivityDTO> inProgressActivityList = StreamUtils.filter(discountActivityList, x -> DiscountActivityStatusEnum.IN_PROGRESS.getCode().equals(x.getStatus()));
        BigDecimal minDiscountPrice = null;
        Long minDiscountPriceActivityId = null;
        Long skuId = null;
        if(CollectionUtils.isNotEmpty(inProgressActivityList)){
            for (SkuDiscountActivityDTO skuDiscountActivityDTO : inProgressActivityList) {
                if(null == minDiscountPrice || skuDiscountActivityDTO.getDiscountPrice().compareTo(minDiscountPrice) < 0){
                    minDiscountPrice = skuDiscountActivityDTO.getDiscountPrice();
                    minDiscountPriceActivityId = skuDiscountActivityDTO.getId();
                    skuId = skuDiscountActivityDTO.getSkuId();
                }
            }
        }
        Map<String, SkuDiscountActivityDTO> discountActivityDTOMap = StreamUtils.toMap(discountActivityList, x -> x.getSkuId() + ":" + x.getId());
        return discountActivityDTOMap.get(skuId + ":" + minDiscountPriceActivityId);
    }

    private SkuDiscountActivityDTO noticeActivity(List<SkuDiscountActivityDTO> discountActivityList) {
        List<SkuDiscountActivityDTO> noticeActivityList  = StreamUtils.filter(discountActivityList, x -> DiscountActivityStatusEnum.NOT_STARTED.getCode().equals(x.getStatus()) &&
                (DiscountActivityNoticeEnum.SUCCESS_NOTICE.getCode().equals(x.getNotice()) || DiscountActivityNoticeEnum.AFTER_HOUR_NOTICE.getCode().equals(x.getNotice())));
        Comparator<SkuDiscountActivityDTO> comparatorByDate = (s1, s2) -> ObjectUtils.compare(s1.getStartTime(), s2.getStartTime());
        List<SkuDiscountActivityDTO> noticeActivitySortList = StreamUtils.sort(noticeActivityList, comparatorByDate);
        Date now = new Date();
        if(CollectionUtils.isNotEmpty(noticeActivitySortList)){
            for (SkuDiscountActivityDTO skuDiscountActivityDTO : noticeActivitySortList) {
                if(DiscountActivityNoticeEnum.SUCCESS_NOTICE.getCode().equals(skuDiscountActivityDTO.getNotice()) && skuDiscountActivityDTO.getEndTime().after(now)){
                    return skuDiscountActivityDTO;
                }else if(DiscountActivityNoticeEnum.AFTER_HOUR_NOTICE.getCode().equals(skuDiscountActivityDTO.getNotice())){
                    Date BeforeStart = DateUtil.getDateBeforeHours(skuDiscountActivityDTO.getStartTime(), skuDiscountActivityDTO.getNoticeHour());
                    if(BeforeStart.before(now) && skuDiscountActivityDTO.getEndTime().after(now)){
                        return skuDiscountActivityDTO;
                    }
                }
            }
        }
        return null;
    }

    /**
     * 商品封装券后价信息+优惠券信息  （若单品支持周期购 只展示周期购单提（券后）价）
     * @param productVO
     * @param couponRuleList
     */
    @Override
    public void buildProdctAllCouponList(ProductVO productVO, List<CouponRule> couponRuleList, Map<Long, List<CouponRange>> couponRangeMap) {
        this.buildProductSku(productVO);
        List<CouponVO> couponVOList = new ArrayList<>();
        for (SkuVO skuVO: productVO.getSkus()) {
            //判断当前skuVO是否与优惠券互斥
            //判断限时折扣互斥优惠券
            //判断满减满送互斥优惠券
            if (!isShareCoupon(skuVO)) {
                continue;
            }
            //计算sku的标价和售价之和
            SkuPriceSumDTO skuPriceSumDTO = this.calculatePrice(skuVO, productVO.getCycleFlag(), 1, null);
            BigDecimal salePriceSum = null == skuPriceSumDTO.getCycleSalePriceSum() ? skuPriceSumDTO.getSalePriceSum(): skuPriceSumDTO.getCycleSalePriceSum();

            List<CouponVO> couponPriceList = new ArrayList<>();
            for (CouponRule rule : couponRuleList) {
                List<CouponRange> couponRanges = couponRangeMap.get(rule.getId());
                //商品范围校验
                Boolean checkRangeFlag = CouponUtil.checkProductCouponRange(rule.getId(), rule.getCouponRange(),
                        BeanUtils.deepCopy(productVO, ProductBaseDTO.class), couponRanges);
                //购买类型校验
                Boolean checkBuyTypeFlag = this.checkBuyTypeWithCycle(rule, productVO);
                //校验领券时间是否已过优惠券领取结束时间
                Boolean checkCouponTimeFlag = this.checkCouponTime(rule);

                if(!checkRangeFlag || !checkBuyTypeFlag || !checkCouponTimeFlag){
                    continue;
                }
                //优惠券需要计算券后价
                if(CouponTypeEnum.PRODUCT_COUPON.getCode().equals(rule.getCouponType())){
                    //计算券后价
                    BigDecimal couponPrice = CouponUtil.computeCouponPrice(salePriceSum, rule);
                    //券后价-装载
                    if (null != couponPrice) {
                        CouponVO couponVO = new CouponVO();
                        BeanUtils.copy(rule, couponVO);
                        couponVO.setRuleId(rule.getId());
                        couponVO.setCouponPrice(couponPrice);
                        if(BasicFlagEnum.YES.getKey().equals(productVO.getCycleFlag())){
                            BigDecimal cycleSingleCouponPrice = couponPrice.divide(skuPriceSumDTO.getCycleMax(),2, BigDecimal.ROUND_UP);
                            couponVO.setCouponPrice(cycleSingleCouponPrice);
                        }
                        couponPriceList.add(couponVO);
                    }
                }
                //优惠券展示信息-装载
                CouponVO couponVO = new CouponVO();
                BeanUtils.copy(rule, couponVO);
                couponVOList.add(couponVO);
            }
            //券后价-排序
            if (CollectionUtils.isNotEmpty(couponPriceList)) {
                List<CouponVO> afterSortedCouponPrice = couponPriceList.stream().sorted(Comparator.comparing(CouponVO::getCouponPrice).thenComparing(CouponVO::getEndTime)).collect(Collectors.toList());
                if(CollectionUtils.isNotEmpty(afterSortedCouponPrice) && null != StreamUtils.getFirst(afterSortedCouponPrice)){
                    skuVO.setCouponPrice(StreamUtils.getFirst(afterSortedCouponPrice));
                }
            }
        }
        //优惠券展示信息-排序
        this.couponInfo(StreamUtils.distinct(couponVOList, Comparator.comparing(CouponVO::getId)), productVO);
    }

    /**
     * 把skuAttrs分别组装到各个属性上
     * @param productVO
     */
    private void buildProductSku(ProductVO productVO){
        List<SkuVO> skus = productVO.getSkus();
        if (CollectionUtils.isNotEmpty(skus)) {
            for (SkuVO skuVO : skus) {
                skuVO.setPurchase(0);
                for (SkuAttrDTO skuAttr : skuVO.getSkuAttrs()) {
                    switch (skuAttr.getAttrName()){
                        case "purchase":
                            skuVO.setPurchase(Integer.valueOf(skuAttr.getAttrValue()));
                            break;
                        case "ownerId":
                            skuVO.setOwnerId(skuAttr.getAttrName());
                            break;
                        case "safeStockQte":
                            skuVO.setSafeStockQte(Long.getLong(skuAttr.getAttrName()));
                            break;
                        case "times":
                            if(Objects.isNull(skuVO.getTimesList())){
                                skuVO.setTimesList(new ArrayList<>());
                            }
                            skuVO.getTimesList().add(new TimesVO(Integer.valueOf(skuAttr.getAttrValue()), new BigDecimal(skuAttr.getAttrSpecValue()), skuAttr.getSort()));
                            break;
                        default:
                            break;
                    }
                }
                //周期排序
                if(CollectionUtils.isNotEmpty(skuVO.getTimesList())){
                    List<TimesVO> sortedCycleList = skuVO.getTimesList().stream().sorted(Comparator.comparing(TimesVO::getTimes)).collect(Collectors.toList());
                    skuVO.setTimesList(sortedCycleList);
                    BigDecimal cycleCheapPrice = sortedCycleList.get(0).getPrice();
                    for (TimesVO timesVO: sortedCycleList) {
                        BigDecimal cyclePrice = timesVO.getPrice();
                        if(cycleCheapPrice.compareTo(cyclePrice) > 0){
                            cycleCheapPrice = cyclePrice;
                        }
                    }
                    skuVO.setCyclePrice(cycleCheapPrice);
                }
            }
        }
    }

    private boolean isShareCoupon(SkuVO skuVO) {
        boolean discountFlag = false;
        if(null != skuVO.getDiscountActivityId()
                && DiscountActivityStatusEnum.IN_PROGRESS.getCode().equals(skuVO.getDiscountActivityStatus())){
            discountFlag = true;
            if(DiscountActivityShareTypeEnum.NO_COUPON.getCode().equals(skuVO.getSharingDiscount())){
                return false;
            }
        }
        FullReduce fullReduce = fullReduceManager.getFullReduceFromRedis(skuVO.getProductId());
        if(null != fullReduce){
            if(discountFlag){
                if(FullReduceShareTypeEnum.isShareDiscount(fullReduce.getShareType())
                        && !FullReduceShareTypeEnum.isShareCoupon(fullReduce.getShareType())){
                    return false;
                }
            }else{
                if(!FullReduceShareTypeEnum.isShareCoupon(fullReduce.getShareType())){
                    return false;
                }
            }
        }
        return true;
    }

    private SkuPriceSumDTO calculatePrice(SkuVO skuVO, Integer cycleFlag, Integer count, Integer times){
        BigDecimal salePrice = skuVO.getSalePrice();

        //有满减满送价就用满减满送价，没有满减满送价有限时折扣价就用限时折扣价
        FullReduceVO fullReduceVO = skuVO.getFullReduce();
        if(null != fullReduceVO){
            SkuPriceSumDTO skuPriceSumDTO = new SkuPriceSumDTO();
            if(null == fullReduceVO.getCycleMax()){
                skuPriceSumDTO.setSalePriceSum(fullReduceVO.getSumFullReducePrice());
                return skuPriceSumDTO;
            }else{
                skuPriceSumDTO.setCycleSalePriceSum(fullReduceVO.getSumFullReducePrice());
                skuPriceSumDTO.setCycleMax(fullReduceVO.getCycleMax());
                return skuPriceSumDTO;
            }
        }else if(null != skuVO.getDiscountPrice()){
            salePrice = this.getDiscountPrice(skuVO, salePrice);
        }

        return this.getSkuSalePrice(skuVO, cycleFlag, salePrice, count, times);
    }

    private Boolean checkBuyTypeWithCycle(CouponRule rule, ProductVO productVO) {
        boolean checkBuyTypeFlag = true;
        if(Objects.isNull(rule.getBuyType())){
            return checkBuyTypeFlag;
        }
        if(CouponBuyTypeEnum.NO_LIMIT.getCode().equals(rule.getBuyType())){
            //购买类型不限
        }else if(CouponBuyTypeEnum.SINGLE.getCode().equals(rule.getBuyType())){
            //单次购买券，单品支持周期购---若单品支持周期购 只展示周期购单提（券后）价，不用计算单品的单次价
            if(BasicFlagEnum.YES.getKey().equals(productVO.getCycleFlag())){
                checkBuyTypeFlag = false;
            }
        }else if(CouponBuyTypeEnum.CYCLE.getCode().equals(rule.getBuyType())){
            //周期购买券，单品不支持周期购
            if(!BasicFlagEnum.YES.getKey().equals(productVO.getCycleFlag())){
                checkBuyTypeFlag = false;
            }
        }
        return checkBuyTypeFlag;
    }

    private Boolean checkCouponTime(CouponRule rule) {
        Boolean checkCouponFlag = true;
        if(Objects.nonNull(rule.getEffectiveTime())){
            CouponInfo info = computeTime(rule, new Date());
            if(info.getStartTime().after(rule.getEndTime())){
                checkCouponFlag = false;
            }
        }
        return checkCouponFlag;
    }

    private void couponInfo(List<CouponVO> couponVOList, ProductVO productVO) {
        //优惠券展示信息-排序
        if (CollectionUtils.isNotEmpty(couponVOList)) {
            List<CouponVO> afterSorted = new ArrayList<>();
            List reduceCouponList= couponVOList.stream().filter(couponVO -> CouponWayEnum.FULL_REDUCE.getCode().equals(couponVO.getCouponWay())).sorted(Comparator.comparing(CouponVO::getAmountReduce).reversed()).collect(Collectors.toList());
            List discountCouponList= couponVOList.stream().filter(couponVO -> CouponWayEnum.UNLIMITED.getCode().equals(couponVO.getCouponWay())).sorted(Comparator.comparing(couponVO -> couponVO.getDiscount())).collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(reduceCouponList)){
                afterSorted.addAll(reduceCouponList);
            }
            if(CollectionUtils.isNotEmpty(discountCouponList)){
                afterSorted.addAll(discountCouponList);
            }
            productVO.setCouponRuleList(afterSorted);
        } else {
            productVO.setCouponRuleList(couponVOList);
        }
    }

    private BigDecimal getDiscountPrice(SkuVO skuVO, BigDecimal salePrice) {
        return getDiscountPrice(skuVO.getDiscountActivityId(), skuVO.getDiscountPrice(), skuVO.getDiscountActivityStatus(), salePrice);
    }
    private BigDecimal getDiscountPrice(Long discountActivityId, BigDecimal discountPrice, Integer status, BigDecimal salePrice){
        if(null != discountActivityId && null != discountPrice && DiscountActivityStatusEnum.IN_PROGRESS.getCode().equals(status)){
            salePrice = discountPrice;
        }
        return salePrice;
    }

    private CouponInfo computeTime(CouponRule couponRule, Date time) {
        CouponInfo couponInfo = new CouponInfo();
        String currentDate = DateUtil.dateToString(time, DateUtil.SIMPLE_YMD) + " 00:00:00";

        //生效时间 生效延时时间(天数)
        int delayTime = null == couponRule.getDelayTime() ? 0 : couponRule.getDelayTime().intValue();
        Date startTime = 0 == delayTime ? DateUtil.stringToDate(currentDate,DateUtil.SIMPLE_FMT) : DateUtil.getNextDate(currentDate, delayTime);
        couponInfo.setStartTime(startTime);
        //失效时间 有效天数
        int effectTime = null == couponRule.getEffectiveTime() ? 0 : couponRule.getEffectiveTime().intValue();
        int extend = 0 == delayTime ? effectTime + delayTime : effectTime + delayTime;
        Date endTime = new Date(DateUtil.getNextDate(currentDate, extend).getTime() - 1000);

        couponInfo.setEndTime(endTime);
        return couponInfo;
    }

    @Override
    public OrderInfo createOrderInfo(OrderTypeEnum orderTypeEnum, CustomerUser customerUser, AddressDetailVO userReceiveAddress, OrderCreateZeroDTO dto) {
        // 生成订单主表
        OrderInfo orderInfo = new OrderInfo();
        Date createTime;
        Date payTime;
        String orderNo;
        String currentTime = DateUtil.dateToString(new Date(), "yyyyMMddHHmmssSSS");
        Integer count;
        Integer partPoint = null;
        SkuBaseDTO skuBaseDTO = dto.getSkuBaseDTO();
        ProductBaseDTO productBaseDTO = dto.getProductBaseDTO();
        BigDecimal originPrice;
        BigDecimal productAmount;
        if(orderTypeEnum == OrderTypeEnum.LUCKY_ORDER){
            createTime = dto.getCreateTime();
            payTime = dto.getPayTime();
            orderNo = "LPKZP" + currentTime + (int) (Math.random() * 9000 + 1000);
            count = 1;
            originPrice = BigDecimal.ZERO;
            productAmount = BigDecimal.ZERO;
            orderInfo.setAmountType(OrderAmountTypeEnum.FREE.getCode());
        }else if(orderTypeEnum == OrderTypeEnum.POINT_ORDER){
            Date now = new Date();
            createTime = now;
            payTime = now;
            orderNo = "LPKJF" + currentTime + (int) (Math.random() * 9000 + 1000);
            count = dto.getCount();
            orderInfo.setPoint(dto.getTotalPoint());
            partPoint = dto.getPartPoint();
            originPrice = dto.getPrice();
            productAmount = new BigDecimal(count).multiply(originPrice);
            orderInfo.setAmountType(OrderAmountTypeEnum.POINT.getCode());
        }else if(orderTypeEnum == OrderTypeEnum.FREE_TRIAL){
            Date now = new Date();
            createTime = now;
            payTime = now;
            orderNo = "LPKFT" + currentTime + (int) (Math.random() * 9000 + 1000);
            count = 1;
            originPrice = skuBaseDTO.getListPrice() != null ? skuBaseDTO.getListPrice() : BigDecimal.ZERO;
            productAmount = new BigDecimal(count).multiply(originPrice);
            orderInfo.setAmountType(OrderAmountTypeEnum.FREE.getCode());
        }else{
            throw new BusinessException("不支持的订单类型");
        }
        orderInfo.setParentType(OrderParentTypeEnum.SIGNAL.getCode());
        orderInfo.setOrderNo(orderNo);
        orderInfo.setSrcNo(orderNo);
        orderInfo.setOrderParentNo(orderNo);
        orderInfo.setUserId(customerUser.getId());
        orderInfo.setProductAmount(productAmount);
        orderInfo.setTotalAmount(BigDecimal.ZERO);
        orderInfo.setRealAmount(BigDecimal.ZERO);
        orderInfo.setIsComment(FlagEnum.YES.getCode());
        ProductTypeEnum productTypeEnum = ProductTypeEnum.getEnum(productBaseDTO.getProductType());
        orderInfo.setProductType(OrderProductTypeEnum.getEnum(productTypeEnum).getCode());
        orderInfo.setOrderType(orderTypeEnum.getCode());
        orderInfo.setOrderStatus(OrderStatusEnum.DELIVERY.getCode());
        orderInfo.setPushFlag(PushFlagEnum.NOT_PUSH.getCode());
        orderInfo.setChannel(OrderChannelEnum.LPK.getCode());
        // todo
        orderInfo.setBuyerNick(customerUser.getNickName());
        orderInfo.setShopNo(orderSyncAdapter.getMallShopCode());
        orderInfo.setShopName("商城电子奶卡");
        orderInfo.setPlatform(OrderPlatformEnum.ZIYAN.getCode());
        orderInfo.setCreateTime(createTime);
        orderInfo.setPayTime(payTime);
        orderInfo.setIsGift(FlagEnum.YES.getCode());
        // 生成订单明细
        OrderSku orderSku = new OrderSku();
        orderSku.setPoint(partPoint);
        orderSku.setProductId(productBaseDTO.getId());
        orderSku.setProductCode(productBaseDTO.getProductName());
        orderSku.setSkuId(skuBaseDTO.getId());
        orderSku.setSkuCode(skuBaseDTO.getSkuCode());
        orderSku.setOrderNo(orderInfo.getOrderNo());
        orderSku.setCategoryId(productBaseDTO.getCardCategoryId());
        orderSku.setCategoryName(productBaseDTO.getCardCategoryName());
        orderSku.setProductName(productBaseDTO.getProductName());
        orderSku.setSkuDesc(skuBaseDTO.getSpecValueList());
        orderSku.setSkuPicUrl(StringUtils.isNotBlank(skuBaseDTO.getPicUrl()) ? skuBaseDTO.getPicUrl() : productBaseDTO.getPicUrl());
        orderSku.setCount(count);
        orderSku.setOriginPrice(originPrice);
        orderSku.setTotalAmount(BigDecimal.ZERO);
        orderSku.setRealAmount(BigDecimal.ZERO);
        orderSku.setUnlockFlag(OrderUnlockFlagEnum.UNLOCKED.getCode());
        orderSku.setSnSyncFlag(SnSyncFlagEnum.SYNC.getCode());
        orderSku.setOrderSkuNo(orderInfo.getOrderNo() + "-" + orderSku.getSkuCode());
        // 生成订单地址
        OrderAddress orderAddress = new OrderAddress();
        orderAddress.setOrderNo(orderInfo.getOrderNo());
        orderAddress.setReceiverName(userReceiveAddress.getReceiveName());
        orderAddress.setReceiverPhone(userReceiveAddress.getPhone());
        orderAddress.setProvince(userReceiveAddress.getProvinceName());
        orderAddress.setCity(userReceiveAddress.getCityName());
        orderAddress.setDistrict(userReceiveAddress.getDistrictName());
        orderAddress.setAddress(userReceiveAddress.getDetailAddress());

        orderManager.save(orderInfo);
        orderSkuManager.saveMore(Lists.newArrayList(orderSku));
        orderAddressManager.saveList(Lists.newArrayList(orderAddress));

        if(orderTypeEnum == OrderTypeEnum.POINT_ORDER){
            StockCalculateDTO stockCalculateDTO = new StockCalculateDTO();
            stockCalculateDTO.setSkuId(orderSku.getSkuId());
            stockCalculateDTO.setStockNum(Long.valueOf(orderSku.getCount()));
            if (!stockManager.decrease(Collections.singletonList(stockCalculateDTO))) {
                throw new BusinessException("商品库存信息更新失败!");
            }
        }

        // 推送中台发货
        OrderConfirmDTO confirmDTO = new OrderConfirmDTO();
        confirmDTO.setOrderNo(orderInfo.getOrderNo());
        confirmDTO.setPayTime(String.valueOf(payTime.getTime()));
        if (OrderProductTypeEnum.VIRTUAL_CARD.getCode().equals(orderInfo.getProductType())) {
            orderRemoteService.confirmOrder(confirmDTO, orderInfo);
        } else if (OrderProductTypeEnum.PHYSICAL_CARD.getCode().equals(orderInfo.getProductType())
                || OrderProductTypeEnum.SINGLE_PRODUCT.getCode().equals(orderInfo.getProductType())
                || OrderProductTypeEnum.LOW_PRODUCT.getCode().equals(orderInfo.getProductType())) {
            orderRemoteService.confirmEntityOrder(confirmDTO, orderInfo);
        }
        // pushAll(customerUser, orderInfo);
        return orderInfo;
    }

    @Override
    public List<MutablePair<CustomerUser, OrderInfo>> createOrder(FreeTrial freeTrial, List<Integer> drawTypeList) {
        // 查询中奖人员:
        List<FreeTrialUser> drawUsers = freeTrialUserManager.findDrawUser(freeTrial.getId(), drawTypeList);
        return doCreateOrder(freeTrial, drawUsers);
    }

    @Override
    public List<MutablePair<CustomerUser, OrderInfo>> doCreateOrder(FreeTrial freeTrial, List<FreeTrialUser> drawUsers) {
        List<FreeTrialProduct> freeTrialProducts = freeTrialProductManager.findByFreeTrialIds(Lists.newArrayList(freeTrial.getId()), FreeProductTypeEnum.FREE.getCode());
        if(CollectionUtils.isEmpty(freeTrialProducts)){
            log.error("免费试用活动开奖任务 . 没找到免费试用商品");
            return Lists.newArrayList();
        }
        FreeTrialProduct freeTrialProduct = freeTrialProducts.get(0);
        List<MutablePair<CustomerUser, OrderInfo>> pairList = Lists.newArrayList();
        for (FreeTrialUser dto : drawUsers) {
            AddressDetailVO userReceiveAddress = null;
            if(StringUtils.isNotBlank(dto.getExtInfo())){
                FreeTrialUserExtInfoDTO extInfoDTO = JSON.parseObject(dto.getExtInfo(), FreeTrialUserExtInfoDTO.class);
                if(Objects.nonNull(extInfoDTO)){
                    userReceiveAddress = BeanUtils.copy(extInfoDTO, AddressDetailVO::new);
                }
            }else{
                userReceiveAddress = getAddress(dto);
            }
            if(userReceiveAddress == null){
                throw new BusinessException("地址不存在");
            }
            SkuBaseDTO skuBaseDTO = skuManager.detail(freeTrialProduct.getSkuId());
            if(skuBaseDTO == null){
                log.info("商品不存在，skuId:{}", freeTrialProduct.getSkuId());
                throw new BusinessException("商品不存在");
            }
            ProductBaseDTO productBaseDTO = productManager.singleGetOne(skuBaseDTO.getProductId());
            if(!Objects.equals(ProductSaleStatusEnum.ON_SHELVES.getCode(), productBaseDTO.getSaleStatus())){
                throw new BusinessException("商品未上架");
            }
            OrderCreateZeroDTO zeroDTO = new OrderCreateZeroDTO();
            zeroDTO.setSkuBaseDTO(skuBaseDTO);
            zeroDTO.setProductBaseDTO(productBaseDTO);
            zeroDTO.setCount(1);
            zeroDTO.setPrice(skuBaseDTO.getListPrice());
            CustomerUser customerUser = customerUserManager.findById(dto.getUserId());
            OrderInfo orderInfo = this.createOrderInfo(OrderTypeEnum.FREE_TRIAL, customerUser, userReceiveAddress, zeroDTO);
            dto.setOrderNo(orderInfo.getOrderNo());
            freeTrialUserManager.update(dto);
            pairList.add(MutablePair.of(customerUser, orderInfo));
            // this.pushAll(customerUser, orderInfo);
        }
        return pairList;
    }

    private AddressDetailVO getAddress(FreeTrialUser dto) {
        UserReceiveAddress userReceiveAddress = addressManager.findByIdWithDel(dto.getAddressId());
        //用户收货地址
        AddressDetailVO addressDetailVO = BeanUtils.copy(userReceiveAddress, AddressDetailVO::new);

        AddressVO district = addressManager.findOne(userReceiveAddress.getAddressId());
        AddressVO province = addressManager.findOne(district.getProvinceId());
        AddressVO city = addressManager.findOne(district.getCityId());

        addressDetailVO.setDistrictName(district.getArea());
        addressDetailVO.setDistrictId(district.getId());
        addressDetailVO.setCityName(city.getArea());
        addressDetailVO.setCityId(city.getId());
        addressDetailVO.setProvinceName(province.getArea());
        addressDetailVO.setProvinceId(province.getId());
        return addressDetailVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateUserMore(List<CustomerUser> users, UserMoreUpdateTypeEnum typeEnum) {
        // 订单统计、最近访问时间同步
        List<Long> userIds = StreamUtils.toList(users, CustomerUser::getId);
        List<CustomerUserMore> userMoreList = customerUserMoreManager.findByUserIds(userIds);
        List<Long> existsUserIds = StreamUtils.toList(userMoreList, CustomerUserMore::getUserId);
        Map<Long, CustomerUserMore> userMoreMap = StreamUtils.toMap(userMoreList, CustomerUserMore::getUserId);
        Map<Long, CustomerUser> userMap = StreamUtils.toMap(users, CustomerUser::getId);
        List<Long> insertIds = CollUtil.subtractToList(userIds, existsUserIds);
        List<Long> updateIds = CollUtil.subtractToList(userIds, insertIds);
        Map<Long, CustomerUserMoreVO> orderInfoMap = Maps.newHashMap();
        Map<Long, CustomerUserMoreVO> orderTimeMap = Maps.newHashMap();
        Map<Long, CustomerUserMoreVO> orderInfo90Map = Maps.newHashMap();
        Map<Long, List<OrderInfo>> allOrderInfoMap = Maps.newHashMap();
        Map<Long, MiniMessageSubscribe> messageMap = Maps.newHashMap();
        Map<String, OrderSku> orderSkuMap = Maps.newHashMap();
        Map<Long, List<ItemProductGroup>> productGroupMap = Maps.newHashMap();
        Map<Long, Product> productMap = Maps.newHashMap();
        Map<String, List<Card>> cardMap = Maps.newHashMap();
        Map<Long, MiniMessageSubscribeVisitsDTO> userVisitMap = Maps.newHashMap();
        Map<Long, CouponUsedDTO> couponUsedMap = Maps.newHashMap();
        Map<Long, Integer> signInCountMap = Maps.newHashMap();
        Map<Long, Integer> voteCountMap = Maps.newHashMap();
        Map<Long, Integer> ptCountMap = Maps.newHashMap();
        Map<Long, Integer> freeTrialCountMap = Maps.newHashMap();
        Map<Long, Integer> swapCountMap = Maps.newHashMap();
        List<OrderInfo> allOrderInfos = new ArrayList<>();
        // 查询用户订单信息
        if(typeEnum == null || typeEnum == UserMoreUpdateTypeEnum.ORDER){
            List<CustomerUserMoreVO> orderInfoVOS = customerUserDao.findOrderInfo(userIds);
            orderInfoMap = StreamUtils.toMap(orderInfoVOS, CustomerUserMoreVO::getUserId);

            List<CustomerUserMoreVO> orderTimvVOS = customerUserDao.findOrderTime(userIds);
            orderTimeMap = StreamUtils.toMap(orderTimvVOS, CustomerUserMoreVO::getUserId);

            List<CustomerUserMoreVO> orderInfo90VOS = customerUserDao.findOrderInfo90(userIds);
            orderInfo90Map = StreamUtils.toMap(orderInfo90VOS, CustomerUserMoreVO::getUserId);
            // 包含了父订单，子订单、单独订单
            allOrderInfos = orderInfoDao.findAllOrderByUserIds(userIds);
            allOrderInfoMap = StreamUtils.group(allOrderInfos, OrderInfo::getUserId);
        }

        if(typeEnum == null || typeEnum == UserMoreUpdateTypeEnum.ALL){

            List<OrderSku> orderSkuList = orderSkuDao.findByOrderNoList(StreamUtils.toList(allOrderInfos, OrderInfo::getOrderNo));
            orderSkuMap = StreamUtils.toMap(orderSkuList, OrderSku::getOrderNo);
            List<Long> productIds = StreamUtils.convertDistinct(orderSkuList, OrderSku::getProductId, Long::compareTo);

            List<ItemProductGroup> productGroupList = itemProductGroupDao.selectByProductId(productIds);
            productGroupMap = StreamUtils.group(productGroupList, ItemProductGroup::getProductId);

            List<Product> productList = productDao.findByIds(productIds);
            productMap = StreamUtils.toMap(productList, Product::getId);

            List<Card> cardList = cardDao.findByOrderNos(StreamUtils.toList(allOrderInfos, OrderInfo::getOrderNo));
            cardMap = StreamUtils.filterGroup(cardList, x-> StringUtils.isNotBlank(x.getOrderNo()), Card::getOrderNo);

            List<MiniMessageSubscribeVisitsDTO> userVisitList = messageSubscribeDao.selectVisitsUser(userIds);
            userVisitMap = StreamUtils.toMap(userVisitList, MiniMessageSubscribeVisitsDTO::getUserId);

            List<CouponUsedDTO> couponUsedList = couponInfoDao.selectCouponCount(userIds);
            couponUsedMap = StreamUtils.toMap(couponUsedList, CouponUsedDTO::getUserId);

            // 签到
            List<SignInTaskCountDTO> signInTaskCountDTOS = taskRecordDao.countSignIn(userIds);
            signInCountMap = StreamUtils.toMap(signInTaskCountDTOS, SignInTaskCountDTO::getUserId, SignInTaskCountDTO::getCount);

            // 共创投票
            List<VoteCountDTO> voteCountDTOS = freeTrialVoteUserDao.countVote(userIds);
            voteCountMap = StreamUtils.toMap(voteCountDTOS, VoteCountDTO::getUserId, VoteCountDTO::getCount);

            // 0元试用
            List<UserJoinFreeTrialCountDTO> freeTrialCountDTOS = freeTrialUserDao.countJoinFreeTrial(userIds);
            freeTrialCountMap = StreamUtils.toMap(freeTrialCountDTOS, UserJoinFreeTrialCountDTO::getUserId, UserJoinFreeTrialCountDTO::getCount);

            // 积分兑换
            List<UserSwapOrderCountDTO> swapOrderCountDTOS = swapOrderDao.countSwapOrder(userIds);
            swapCountMap = StreamUtils.toMap(swapOrderCountDTOS, UserSwapOrderCountDTO::getUserId, UserSwapOrderCountDTO::getCount);


            // 拼团订单
            List<UserJoinPtCountDTO> ptCountList = ptSubOrderDao.countJoinPt(userIds);
            ptCountMap = StreamUtils.toMap(ptCountList, UserJoinPtCountDTO::getUserId, UserJoinPtCountDTO::getCount);
        }

        if(typeEnum == null || typeEnum == UserMoreUpdateTypeEnum.MESSAGE){
            // 查询用户触达信息
            List<MiniMessageSubscribe> messageList = messageSubscribeDao.findMaxSendTime(userIds);
            messageMap = StreamUtils.toMap(messageList, MiniMessageSubscribe::getUserId);
        }

        List<CustomerUserMore> inserList = Lists.newArrayList();
        List<CustomerUserMoreProduct> insertProductList = Lists.newArrayList();
        for (Long insertId : insertIds) {
            CustomerUserMore customerUserMore = CommonOrderUtil.getCustomerUserMore(null, insertId, userMap, orderInfoMap, orderTimeMap, orderInfo90Map, allOrderInfoMap, typeEnum, orderSkuMap, productGroupMap, productMap, cardMap, userVisitMap, couponUsedMap, signInCountMap, voteCountMap, ptCountMap, freeTrialCountMap, swapCountMap);
            CommonOrderUtil.buildMessage(customerUserMore, messageMap);
            inserList.add(customerUserMore);
        }
        for (Long updateId : updateIds) {
            CustomerUserMore userMore = userMoreMap.get(updateId);
            CustomerUserMore customerUserMore = CommonOrderUtil.getCustomerUserMore(userMore, updateId, userMap, orderInfoMap, orderTimeMap, orderInfo90Map, allOrderInfoMap, typeEnum, orderSkuMap, productGroupMap, productMap, cardMap, userVisitMap, couponUsedMap, signInCountMap, voteCountMap, ptCountMap, freeTrialCountMap, swapCountMap);
            CommonOrderUtil.buildMessage(customerUserMore, messageMap);
            inserList.add(customerUserMore);
        }
        customerUserMoreManager.deleteByUserIds(updateIds);
        customerUserMoreProductDao.deleteByCustomerUserMoreIds(updateIds);
        if(CollectionUtils.isNotEmpty(inserList)){
            customerUserMoreManager.insertList(inserList);
            for (CustomerUserMore userMore : inserList) {
                CustomerUserMoreProduct customerUserMoreProduct = CommonOrderUtil.getCustomerUserMoreProduct(userMore);
                if(StringUtils.isNotBlank(customerUserMoreProduct.getGroupId()) ||
                        StringUtils.isNotBlank(customerUserMoreProduct.getCateId()) ||
                        StringUtils.isNotBlank(customerUserMoreProduct.getCardCategoryId())){
                    insertProductList.add(customerUserMoreProduct);
                }
            }
        }
        if(CollectionUtils.isNotEmpty(insertProductList)){
            customerUserMoreProductDao.insertList(insertProductList);
        }
    }

    @Override
    public void updateUserLifeCycle(List<CustomerUserByPhoneDTO> userList) {
        List<String> phones = StreamUtils.toList(userList, CustomerUserByPhoneDTO::getPhone);
        List<UserVisitDetail> userVisitDetails = userVisitDetailDao.selectLastVisitByPhones(phones);
        Map<String, UserVisitDetail> userVisitDetailMap = StreamUtils.toMap(userVisitDetails, UserVisitDetail::getPhone);

        List<String> cardNumbers = cardDao.selectByPhones(phones);

        List<OrderInfo> allOrderInfo = orderInfoDao.selectByPhones(phones);
        Map<String, List<OrderInfo>> orderInfoMap = StreamUtils.group(allOrderInfo, OrderInfo::getPhone);
        List<CustomerUserMore> updateList = new ArrayList<>();
        for (CustomerUserByPhoneDTO user: userList) {
            if(Objects.isNull(user.getFirstLoginTime()) || Objects.isNull(user.getPhone())){
                continue;
            }
            user.setUserIdList(Arrays.stream(user.getUserIds().split(","))
                    .map(Long::valueOf)
                    .collect(Collectors.toList()));

            long registerDays = DateUtil.caculateDayDiff(user.getFirstLoginTime(), new Date());
            UserVisitDetail userVisitDetail = userVisitDetailMap.get(user.getPhone());
            long visitDays = -1;
            if(null != userVisitDetail){
                visitDays = DateUtil.caculateDayDiff(userVisitDetail.getVisitTime(), new Date());
            }
            boolean bindCard = cardNumbers.contains(user.getPhone());
            List<OrderInfo> orderInfoList = orderInfoMap.get(user.getPhone());
            List<OrderInfo> recent90DaysOrders = StreamUtils.filter(orderInfoList, x-> DateUtil.caculateDayDiff(x.getPayTime(), new Date()) < 90);

            if(registerDays < 730 && CollectionUtils.size(orderInfoList) == 0 && !bindCard && visitDays > 365){
                user.setLifeCycle(UserLifeCycleEnum.CYCLE_5.getCode());
            }else if(visitDays > 180){
                user.setLifeCycle(UserLifeCycleEnum.CYCLE_4.getCode());
            }else if(visitDays < 90 && CollectionUtils.size(recent90DaysOrders) >= 1){
                user.setLifeCycle(UserLifeCycleEnum.CYCLE_3.getCode());
            }else if(CollectionUtils.size(orderInfoList) == 1){
                user.setLifeCycle(UserLifeCycleEnum.CYCLE_2.getCode());
            }else if(registerDays < 365 && CollectionUtils.size(orderInfoList) == 0){
                user.setLifeCycle(UserLifeCycleEnum.CYCLE_1.getCode());
            }
            for (Long userId : user.getUserIdList()) {
                CustomerUserMore userMoreUpdate = new CustomerUserMore();
                userMoreUpdate.setUserId(userId);
                userMoreUpdate.setLifeCycle(user.getLifeCycle());
                updateList.add(userMoreUpdate);
            }
        }
        if(CollectionUtils.isNotEmpty(updateList)){
            customerUserMoreDao.updateLifeCycleBatchByUserId(updateList);
        }
    }

}
