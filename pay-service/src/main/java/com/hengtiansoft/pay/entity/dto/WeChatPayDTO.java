package com.hengtiansoft.pay.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@ApiModel("微信支付接口")
public class WeChatPayDTO {
    @NotBlank(message = "内部交易订单号不能为空！")
    @ApiModelProperty("业务方系统内部交易订单号 要求64个字符内")
    private String outTradeNo;

    @ApiModelProperty("openId 小程序支付时必传")
    private String openId;

    @ApiModelProperty("商品id pc支付必传")
    private String productId;

    @ApiModelProperty("商品描述")
    @NotBlank(message = "商品描述不能为空！")
    private String body;

    @ApiModelProperty("商品描述")
    private List<String> bodyList;

    @NotNull(message = "订单总金额不能为空")
    @ApiModelProperty("订单总金额：分")
    private Long totalFee;
}
