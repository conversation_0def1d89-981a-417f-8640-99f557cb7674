package com.hengtiansoft.thirdpart.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;
import org.springframework.web.client.RestTemplate;

@Configuration
@PropertySource(value = "classpath:config/${spring.profiles.active}/thirdPart.properties", encoding = "UTF-8",
    ignoreResourceNotFound = true)
@Data
public class SystemConfig {

    /*********** SMS ***********/
    @Value("${sms.sendFlag:false}")
    private Boolean sendFlag;

    @Value("${sms.userId:}")
    private String smsUserId;

    @Value("${sms.marketing.userId:}")
    private String smsMarketingUserId;
    
    @Value("${sms.url:}")
    private String smsUrl;

    @Value("${sms.sname:}")
    private String smsSname;

    @Value("${sms.spwd:}")
    private String smsSpwd;

    /********* express快递100 *********/
    @Value("${express.customer:}")
    private String expressCustomer;

    @Value("${express.key:}")
    private String expressKey;

    @Value("${express.expressUrl:}")
    private String expressUrl;
    
    @Value("${express.expressAutoUrl:}")
    private String expressAutoUrl;
    
    /********* 有赞 *********/
    @Value("${youzanapi.clientId:}")
    private String yzClientId;
    
    @Value("${youzanapi.clientSecret:}")
    private String yzClientSecret;
    
    @Value("${youzanapi.grantId:}")
    private String ycGrantId;

    @Value("${youzan.authorize.url}")
    private String youZanAuthorizeUrl;

    /*************摩西科技获取有赞accessToken*****************/
    @Value("${moxi.youzanapi.url}")
    private String moXiYouZanApiUrl;

    @Value("${moxi.youzanapi.appid}")
    private String moXiYouZanApiAppId;

    @Value(("${moxi.youzanapi.appkey}"))
    private String moXiYouZanApiAppKey;

    /********* 旺店通-奇门 **********/
    @Value("${taobao.qm.service.url}")
    private String qmServiceUrl;

    @Value("${taobao.qm.wdt.service.url}")
    private String qmWdtServiceUrl;

    /**
     * ("奇门授权应用")
     */
    @Value("${qm.appkey}")
    private String qmAppKey;

    /**
     * 奇门appSecret
     */
    @Value("${qm.appSecret}")
    private String qmAppSecret;
    /**
     * ("目标appkey，由旺店通提供")1
     */
    @Value("${wdt.targetAppKey}")
    private String targetAppKey;

    /**
     * 天猫精灵消息中心
     */
    @Value("${taobao.ailab.aicloud.url}")
    private String aicloudUrl;

    /**
     * 旺店通的appSecret 1
     */
    @Value("${wdt.appSecret}")
    private String wdtAppSecret;

    /**
     * 路由参数，淘宝平台发起上线后由下游自定义维护
     */
    @Value("${customerId}")
    private String customerId;

    @Value("${tinai.shop.no}")
    private String shopNo;

    /**
     * 卖家账号（购买ERP时由旺店通分配给ERP购买方，请从ERP购买方获取）1
     */
    @Value("${sId}")
    private String sId;

    /**
     * 淘宝开放平台授权信息获取url
     */
    @Value("${taobao.authorize.code.url}")
    private String taoBaoAuthorizeCodeUrl;

    /**
     * 淘宝开放平台授权信息获取url
     */
    @Value("${taobao.authorize.token.url}")
    private String taoBaoAuthorizeTokenUrl;

    /**
     * 淘宝开放平台授权信息回调地址
     */
    @Value("${taobao.authorize.redirect.url}")
    private String taoBaoAuthorizeRedirectUrl;

    /********* nascent南讯 *********/
    @Value("${nascent.url}")
    private String nascentUrl;

    @Value("${nascent.appkey}")
    private String nascentAppkey;

    @Value("${nascent.appSecret}")
    private String nascentAppSecret;

    @Value("${nascent.groupId}")
    private Long nascentGroupID;

    @Value("${nascent.shopId}")
    private Long nascentShopId;

    @Value("${nascent.integralAccount}")
    private String nascentIntegralAccount;

    /********* 创新服务 *********/
    @Value("${milk.create.domain}")
    private String milkCreateDomain;

    /********* oms *********/

    @Value("${oms.userName}")
    private String omsUserName;

    @Value("${oms.userKey}")
    private String omsUserKey;

    @Value("${oms.userSecret}")
    private String omsUserSecret;

    @Value("${oms.domain}")
    private String omsDomain;

    @Value("${oms.shopCode.naika}")
    private String omsNaikaShopCode;

    @Value("${oms.shopCode.youzan}")
    private String omsYouzanShopCode;

    @Value("${oms.shopCode.tinai}")
    private String omsTinaiShopCode;

    /********* 腾讯有数 *********/

    @Value("${youshu.domain}")
    private String youshuDomain;

    @Value("${youshu.appId}")
    private String youshuAppId;

    @Value("${youshu.appSecret}")
    private String youshuAppSecret;

    @Value("${youshu.dataSourceId}")
    private String youshuDataSourceId;

    @Value("${youshu.merchantId}")
    private String youshuMerchantId;

    public static String biURl;
    @Value("${bi.url}")
    public void setBiURl(String biUrl){
        SystemConfig.biURl=biUrl;
    }

    /********* 腾讯云 *********/
    @Value("${tencentcloud.secretId}")
    private String tencentCloudSecretId;

    @Value("${tencentcloud.secretKey}")
    private String tencentCloudSecretKey;

    /********* 星云有客 *********/
    @Value("${iyouke.appId}")
    private String iyoukeAppId;

    @Value("${iyouke.secret}")
    private String iyoukeSecret;

    @Value("${iyouke.syncFlag:false}")
    private Boolean iyoukeSyncFlag;

    /********* coze *********/
    @Value("${coze.ai.guide.apiKey}")
    private String cozeAiGuideApiKey;

    @Value("${coze.ai.guide.botId}")
    private String cozeAiGuideBotId;

    @Value("${coze.ai.guide.clientId}")
    private String cozeAiGuideClientId;

    @Value("${coze.ai.guide.clientSecret}")
    private String cozeAiGuideClientSecret;

    /********* mot *********/
    @Value("${mot.corporationId}")
    private String motCorporationId;

    @Value("${mot.secretKey}")
    private String motSecretKey;

    @Value("${mot.bizId}")
    private String motBizId;

    @Value("${mot.wxworkCorpId}")
    private String motWxworkCorpId;

    @Value("${mot.wxworkAgentId}")
    private String motWxworkAgentId;

    @Bean
    public RestTemplate restTemplate(RestTemplateBuilder builder){
        return builder.build();
    }

}
