package com.hengtiansoft.thirdpart.impl;

import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.coze.openapi.client.bots.RetrieveBotReq;
import com.coze.openapi.client.bots.RetrieveBotResp;
import com.coze.openapi.client.chat.CancelChatReq;
import com.coze.openapi.client.chat.CancelChatResp;
import com.coze.openapi.client.common.pagination.PageResp;
import com.coze.openapi.client.connversations.CreateConversationReq;
import com.coze.openapi.client.connversations.CreateConversationResp;
import com.coze.openapi.client.connversations.message.ListMessageReq;
import com.coze.openapi.client.connversations.message.model.Message;
import com.coze.openapi.client.dataset.document.*;
import com.coze.openapi.client.dataset.document.model.Document;
import com.coze.openapi.client.dataset.document.model.DocumentBase;
import com.coze.openapi.client.dataset.document.model.DocumentChunkStrategy;
import com.coze.openapi.service.auth.TokenAuth;
import com.coze.openapi.service.config.Consts;
import com.coze.openapi.service.service.CozeAPI;
import com.hengtiansoft.common.redis.RedisOperation;
import com.hengtiansoft.thirdpart.config.SystemConfig;
import com.hengtiansoft.thirdpart.entity.dto.coze.AiGuideChatDTO;
import com.hengtiansoft.thirdpart.interfaces.CozeManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

@Component
@Slf4j
public class CozeManagerImpl implements CozeManager {

    @Resource
    private RedisOperation redisOperation;
    @Resource
    private SystemConfig systemConfig;
    @Resource
    private RedissonClient redissonClient;

    private static final String OAUTH2_TOKEN = "https://api.coze.cn/api/permission/oauth2/token";

    @Override
    public CancelChatResp chatCancel(AiGuideChatDTO dto) {
        CozeAPI coze = getCoze();
        CancelChatReq req = CancelChatReq.builder()
                .chatID(dto.getChatId())
                .conversationID(dto.getConversationId())
                .build();
        log.info("cancel chat req: {}", JSON.toJSONString(req));
        CancelChatResp resp = null;
        try {
            resp = coze.chat().cancel(req);
            log.info("cancel chat resp: {}", JSON.toJSONString(resp));
        }catch (Exception e){
            log.info("cancel chat error: {}", e);
        }
        return resp;
    }

    @Override
    public CreateConversationResp conversationCreate() {
        CozeAPI coze = getCoze();
        CreateConversationReq req = CreateConversationReq.builder()
                .build();
        log.info("conversation create req: {}", JSON.toJSONString(req));
        CreateConversationResp resp = coze.conversations().create(req);
        log.info("conversation create resp: {}", JSON.toJSONString(resp));
        return resp;
    }

    @Override
    public RetrieveBotResp getOnlineInfo(String botId) {
        CozeAPI coze = getCoze();
        RetrieveBotReq req = RetrieveBotReq.of(botId);
        log.info("getOnlineInfo req: {}", JSON.toJSONString(req));
        RetrieveBotResp resp = coze.bots().retrieve(req);
        log.info("getOnlineInfo resp: {}", JSON.toJSONString(resp));
        return resp;
    }

    @Override
    public CreateDocumentResp knowledgeDocumentCreate(Long datasetID, List<DocumentBase> documentBases, DocumentChunkStrategy chunkStrategy) {
        CozeAPI coze = getCoze();
        CreateDocumentReq req = CreateDocumentReq.builder()
                .datasetID(datasetID)
                .documentBases(documentBases)
                .chunkStrategy(chunkStrategy)
                .build();
        log.info("knowledge document create req: {}", JSON.toJSONString(req));
        CreateDocumentResp resp = coze.datasets()
                .documents()
                .create(req);
        log.info("knowledge document create resp: {}", JSON.toJSONString(resp));
        return resp;
    }

    @Override
    public DeleteDocumentResp knowledgeDocumentDelete(List<Long> documentIds) {
        CozeAPI coze = getCoze();
        DeleteDocumentReq req = DeleteDocumentReq.builder()
                .documentIDs(documentIds)
                .build();
        log.info("knowledge document delete req: {}", JSON.toJSONString(req));
        DeleteDocumentResp resp = coze.datasets()
                .documents()
                .delete(req);
        log.info("knowledge document delete resp: {}", JSON.toJSONString(resp));
        return resp;
    }

    @Override
    public PageResp<Document> knowledgeDocumentList(Long datasetID, Integer page, Integer size) {
        CozeAPI coze = getCoze();
        ListDocumentReq req = ListDocumentReq.builder()
                .datasetID(datasetID)
                .page(page)
                .size(size)
                .build();
        log.info("knowledge document list req: {}", JSON.toJSONString(req));
        PageResp<Document> resp = coze.datasets()
                .documents()
                .list(req);
        log.info("knowledge document list resp: {}", JSON.toJSONString(resp));
        return resp;
    }

    @Override
    public PageResp<Message> messageList(AiGuideChatDTO dto) {
        CozeAPI coze = getCoze();
        ListMessageReq req = ListMessageReq.builder()
                .conversationID(dto.getConversationId())
                .afterID(dto.getAfterId())
                .build();
        log.info("message list req: {}", JSON.toJSONString(req));
        PageResp<Message> resp = coze.conversations().messages().list(req);
        log.info("message list resp: {}", JSON.toJSONString(resp));
        return resp;
    }

    @Override
    public String createToken(String code, String redirectUri) {
        Map<String,String> params = new HashMap<>();
        params.put("grant_type", "authorization_code");
        params.put("code", code);
        params.put("client_id", systemConfig.getCozeAiGuideClientId());
        params.put("redirect_uri", redirectUri);
        params.put("client_secret", systemConfig.getCozeAiGuideClientSecret());
        String clientSecret = systemConfig.getCozeAiGuideClientSecret();
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        headers.put("Authorization", "Bearer " + clientSecret);
        log.info("coze authorization_code req:{}", JSON.toJSONString(params));
        String response = HttpRequest.post(OAUTH2_TOKEN)
                .body(JSON.toJSONString(params))
                .header("Content-Type", "application/json")
                .header("Authorization", "Bearer " + clientSecret)
                .execute()
                .body();
        log.info("coze authorization_code resp:{}", response);
        try {
            JSONObject jsonObject = JSON.parseObject(response);
            if (StringUtils.isNotBlank(jsonObject.getString("access_token"))) {
                String accessToken = jsonObject.getString("access_token");
                Integer expiresIn = jsonObject.getInteger("expires_in");
                long expirationTimestamp = expiresIn - System.currentTimeMillis() / 1000 - 10;
                if(expirationTimestamp <= 0){
                    log.error("coze refresh_token access_token expire.  accessToken:{}", accessToken);
                    return accessToken;
                }
                String refreshToken = jsonObject.getString("refresh_token");
                redisOperation.setex("coze_access_token", accessToken, expirationTimestamp, TimeUnit.SECONDS);
                redisOperation.setex("coze_refresh_token", refreshToken, 30, TimeUnit.DAYS);
            }
        } catch (Exception e) {
            log.error("coze authorization_code error:{}", e);
        }
        return response;
    }

    @Override
    public CozeAPI getCoze() {
        String token = getToken();
        TokenAuth authCli = new TokenAuth(token);
        return new CozeAPI.Builder()
                .baseURL(Consts.COZE_CN_BASE_URL)
                .auth(authCli)
                .readTimeout(60000)
                .build();
    }

    @Override
    public CozeAPI getCoze(String secretToken) {
        String token = secretToken;
        if(StringUtils.isBlank(token)){
            token = getToken();
        }
        TokenAuth authCli = new TokenAuth(token);
        return new CozeAPI.Builder()
                .baseURL(Consts.COZE_CN_BASE_URL)
                .auth(authCli)
                .readTimeout(60000)
                .build();
    }

    @Override
    public String getToken() {
        Object token = redisOperation.get("coze_access_token");
        if (Objects.nonNull(token)) {
            return token.toString();
        }
        String accessToken = "";
        String lockKey = "cozeRefreshToken";
        RLock lock = redissonClient.getLock(lockKey);
        if (Objects.isNull(lock)) {
            log.error("getLockException");
            return "";
        }
        boolean tryLock;
        try {
            tryLock = lock.tryLock(5, TimeUnit.SECONDS);
            if (!tryLock) {
                log.error("coze refresh_token. try getLock failed");
                return "";
            }
            Object refreshToken = redisOperation.get("coze_refresh_token");
            if(Objects.isNull(refreshToken)){
                log.error("coze refreshToken is null，请重新手动授权获得新的auth_code");
                return "";
            }
            Map<String,String> params = new HashMap<>();
            params.put("grant_type", "refresh_token");
            params.put("refresh_token", refreshToken.toString());
            params.put("client_id", systemConfig.getCozeAiGuideClientId());
            params.put("client_secret", systemConfig.getCozeAiGuideClientSecret());
            String clientSecret = systemConfig.getCozeAiGuideClientSecret();
            log.info("coze refresh_token req:{}", JSON.toJSONString(params));
            String response = HttpRequest.post(OAUTH2_TOKEN)
                    .body(JSON.toJSONString(params))
                    .header("Content-Type", "application/json")
                    .header("Authorization", "Bearer " + clientSecret)
                    .execute()
                    .body();
            log.info("coze refresh_token resp:{}", response);
            JSONObject jsonObject = JSON.parseObject(response);
            if (StringUtils.isNotBlank(jsonObject.getString("access_token"))) {
                accessToken = jsonObject.getString("access_token");
                Integer expiresIn = jsonObject.getInteger("expires_in");
                long expirationTimestamp = expiresIn - System.currentTimeMillis() / 1000 - 10;
                if(expirationTimestamp <= 0){
                    log.error("coze refresh_token access_token expire.  accessToken:{}", accessToken);
                    return accessToken;
                }
                refreshToken = jsonObject.getString("refresh_token");
                redisOperation.setex("coze_access_token", accessToken, expirationTimestamp, TimeUnit.SECONDS);
                redisOperation.setex("coze_refresh_token", refreshToken, 30, TimeUnit.DAYS);
            }
        } catch (Exception e) {
            log.error("coze refresh_token 异常.", e);
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
        return accessToken;
    }
}
