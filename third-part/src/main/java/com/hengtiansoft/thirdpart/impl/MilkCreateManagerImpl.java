package com.hengtiansoft.thirdpart.impl;

import com.hengtiansoft.common.entity.vo.Response;
import com.hengtiansoft.thirdpart.entity.dto.oms.*;
import com.hengtiansoft.thirdpart.entity.vo.oms.OmsCardVO;
import com.hengtiansoft.thirdpart.interfaces.MilkCreateManager;
import com.hengtiansoft.thirdpart.util.MilkCreateClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class MilkCreateManagerImpl implements MilkCreateManager {
    @Resource
    private MilkCreateClient milkCreateClient;
    @Override
    public String getCardPwdByNumber(String cardNumber) {
        String msg = milkCreateClient.queryCard(cardNumber);
        return msg;
    }

    @Override
    public Response<Object> unfreezeCard(TmallRequestDTO<OmsUnfreezeCardDTO> param) {
        return milkCreateClient.cardUnfreeze(param);
    }

    @Override
    public Response<Boolean> cardInvalid(TmallRequestDTO<OmsCardInvalidDTO> param) {
        return milkCreateClient.cardInvalid(param);
    }

    @Override
    public Response<Object> cardReconciliation(TmallRequestDTO<OmsChangeAmountDTO> param) {
        return milkCreateClient.cardReconciliation(param);
    }

    @Override
    public Response<List<OmsCardVO>> cardQuery(TmallRequestDTO<OmsCardQueryDTO> param) {
        return milkCreateClient.cardQuery(param);
    }
}
