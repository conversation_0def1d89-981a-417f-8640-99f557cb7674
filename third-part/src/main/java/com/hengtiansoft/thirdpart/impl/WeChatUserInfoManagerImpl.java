package com.hengtiansoft.thirdpart.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.hengtiansoft.common.entity.exception.BusinessException;
import com.hengtiansoft.common.redis.RedisOperation;
import com.hengtiansoft.common.util.HttpClientTemplate;
import com.hengtiansoft.thirdpart.config.WxMiniConfig;
import com.hengtiansoft.thirdpart.entity.dto.wechat.*;
import com.hengtiansoft.thirdpart.interfaces.WeChatUserInfoManager;
import com.hengtiansoft.thirdpart.util.WXCoreUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
public class WeChatUserInfoManagerImpl implements WeChatUserInfoManager {


    private static final String JS_CODE_URL = "https://api.weixin.qq.com/sns/jscode2session";

    private static final String GET_USER_PHONE_URL = "https://api.weixin.qq.com/wxa/business/getuserphonenumber?access_token=";

    private static final String STABLE_TOKEN_URL = "https://api.weixin.qq.com/cgi-bin/stable_token";

    private static final String TOKEN_URL = "https://api.weixin.qq.com/cgi-bin/token";

    private static final String GET_USER_URL = "https://api.weixin.qq.com/cgi-bin/user/get?access_token=";

    private static final String BATCH_USER_INFO_URL = "https://api.weixin.qq.com/cgi-bin/user/info/batchget?access_token=";


    @Resource
    private HttpClientTemplate httpClientTemplate;

    @Resource
    private RedisOperation redisOperation;

    @Resource
    private WxMiniConfig wxMiniConfig;

    @Override
    public WxOpenIdResultDTO findUserInfo(String authCode) {

        Map<String, String> map = new HashMap<>(16);

        map.put("appid", wxMiniConfig.getAppId());
        map.put("secret", wxMiniConfig.getSecret());
        map.put("js_code", authCode);
        map.put("grant_type", "authorization_code");

        try {
            log.info("请求小程序登录凭证校验. request: {}", JSON.toJSONString(map));
            String result = httpClientTemplate.doGet(JS_CODE_URL, map);
            log.info("请求小程序登录凭证校验. result: {}", result);
            WxOpenIdResultDTO wxOpenIdResultDTO = JSON.parseObject(result, WxOpenIdResultDTO.class);

            String errorCode = wxOpenIdResultDTO.getErrCode();

            String errMsg = wxOpenIdResultDTO.getErrMsg();

            if (StringUtils.isNotBlank(errorCode)) {
                log.error("获取微信认证信息错误 WeChatUserInfoManager##findUserInfo errorCode {} errMsg{}", errorCode, errMsg);
                return null;
            }

            return wxOpenIdResultDTO;

        } catch (IOException e) {
            log.error("请求微信认证出现异常！", e);
            return null;
        }

    }

    @Override
    public WxPhoneInfo getUserPhoneNumber(String code) {
        String accessToken = this.miniStableAccessToken();
        String requestUrl = GET_USER_PHONE_URL  + accessToken;

        try {
            Map<String, String> params = new HashMap<>();
            params.put("code", code);
            log.info("请求微信获取手机号. request: {}", JSON.toJSONString(params));
            String result = httpClientTemplate.doPostJson(requestUrl, params);
            log.info("请求微信获取手机号. result: {}", result);
            WxPhoneInfoResponse response = JSON.parseObject(result, WxPhoneInfoResponse.class);
            if (!"0".equals(response.getErrcode())) {
                log.error("请求微信获取手机号 WeChatUserInfoManager##getUserPhoneNumber errorCode {} errMsg{}", response.getErrcode(), response.getErrmsg());
                if("40001".equals(response.getErrcode())){
                    //token失效
                    redisOperation.del("mini_stable_access_token");
                }
                return null;
            }
            return response.getWxPhoneInfo();
        }catch (Exception e){
            log.error("请求微信获取手机号出现异常！", e);
            return null;
        }
    }


    @Override
    public WxPhoneInfo decrypt(String encryptedData, String sessionKey, String offset) {

        String newEncryptedData = encryptedData.replace(' ', '+');

        String newOffset = offset.replace(' ', '+');

        try {
            String result = WXCoreUtil.decrypt(wxMiniConfig.getAppId(), newEncryptedData, sessionKey, newOffset);
            log.info("微信认证信息解密手机号. result: {}", result);
            return JSON.parseObject(result, WxPhoneInfo.class);

        } catch (Exception e) {
            log.error("微信认证信息解密错误", e);
            return null;
        }
    }

    @Override
    public WxOpenIdResultDTO openIdExchangeUnionId(String openId) {
        WxOpenIdResultDTO wxOpenIdResultDTO = new WxOpenIdResultDTO();
        if (StringUtils.isBlank(openId)){
            return wxOpenIdResultDTO;
        }
        String url = "https://api.weixin.qq.com/cgi-bin/user/info?access_token=";
        String accessToken = this.stableAccessToken();
        String requestUrl = url  + accessToken + "&openid=" + openId + "&lang=zh_CN";
        try {
            String result = httpClientTemplate.doGet(requestUrl);
            log.info("获取微信公众号信息:{}", result);
            wxOpenIdResultDTO = JSON.parseObject(result, WxOpenIdResultDTO.class);
            String errorCode = wxOpenIdResultDTO.getErrCode();
            String errMsg = wxOpenIdResultDTO.getErrMsg();
            if (StringUtils.isNotBlank(errorCode)) {
                log.error("获取微信公众号信息错误 WeChatUserInfoManager##openIdChangeUnionId errorCode {} errMsg {}", errorCode, errMsg);
                return wxOpenIdResultDTO;
            }
            return wxOpenIdResultDTO;
        } catch (IOException e) {
            log.error("获取微信公众号信息错误 WeChatUserInfoManager##openIdChangeUnionId error", e);
        }
        return wxOpenIdResultDTO;
    }


    @Override
    public String stableAccessToken() {
        Object token = redisOperation.get("access_token");
        if (Objects.nonNull(token)) {
            return token.toString();
        }
        try {
            Map<String, String> params = new HashMap<>();
            params.put("grant_type", "client_credential");
            params.put("appid", wxMiniConfig.getWechartAppId());
            params.put("secret", wxMiniConfig.getWechartAppSecret());
            String result = httpClientTemplate.doPostJson(STABLE_TOKEN_URL, params);
            log.info("获取微信公众号稳定版accessToken:[{}]", result);
            JSONObject object = JSONObject.parseObject(result);
            String accessToken = object.getString("access_token");
            Integer expires_in = object.getInteger("expires_in");
            if (StringUtils.isBlank(accessToken)) {
                String errorCode = object.getString("errcode");
                String errorMsg = object.getString("errmsg");
                if (StringUtils.isNotBlank(errorCode)) {
                    log.error("获取微信公众号稳定版accessToken错误 WeChatUserInfoManager##stableAccessToken errorCode {} errMsg{}", errorCode, errorMsg);
                    return StringUtils.EMPTY;
                }
            }

            if(Objects.nonNull(expires_in)){
                expires_in = expires_in > 60 ? expires_in - 60 : expires_in;
                redisOperation.setex("access_token", accessToken, expires_in, TimeUnit.SECONDS);
            }

            return accessToken;
        } catch (IOException e) {
            log.error("获取微信公众号稳定版accessToken错误 WeChatUserInfoManager##stableAccessToken error", e);
        }
        return StringUtils.EMPTY;
    }

    @Override
    public String miniStableAccessToken() {
        Object token = redisOperation.get("mini_stable_access_token");
        if (Objects.nonNull(token)) {
            return token.toString();
        }
        try {
            Map<String, String> params = new HashMap<>();
            params.put("grant_type", "client_credential");
            params.put("appid", wxMiniConfig.getAppId());
            params.put("secret", wxMiniConfig.getSecret());
            String result = httpClientTemplate.doPostJson(STABLE_TOKEN_URL, params);
            log.info("获取微信小程序稳定版accessToken:[{}]", result);
            JSONObject object = JSONObject.parseObject(result);
            String accessToken = object.getString("access_token");
            Integer expires_in = object.getInteger("expires_in");
            if (StringUtils.isBlank(accessToken)) {
                String errorCode = object.getString("errcode");
                String errorMsg = object.getString("errmsg");
                if (StringUtils.isNotBlank(errorCode)) {
                    log.error("获取微信小程序稳定版accessToken错误 WeChatUserInfoManager##miniStableAccessToken errorCode {} errMsg{}", errorCode, errorMsg);
                    return StringUtils.EMPTY;
                }
            }

            if(Objects.nonNull(expires_in)){
                expires_in = expires_in > 60 ? expires_in - 60 : expires_in;
                redisOperation.setex("mini_stable_access_token", accessToken, expires_in, TimeUnit.SECONDS);
            }

            return accessToken;
        } catch (Exception e) {
            log.error("获取微信小程序稳定版accessToken错误 WeChatUserInfoManager##miniStableAccessToken error", e);
        }
        return StringUtils.EMPTY;
    }

    @Override
    public String testMiniAccessToken(String url) {
        try {
            Map<String, String> params = new HashMap<>();
            params.put("grant_type", "client_credential");
            params.put("appid", wxMiniConfig.getAppId());
            params.put("secret", wxMiniConfig.getSecret());
            String result = "";
            if("token".equals(url)){
                result = httpClientTemplate.doGet(TOKEN_URL, params);
            }else{
                result = httpClientTemplate.doPostJson(STABLE_TOKEN_URL, params);
            }
            log.info("获取微信小程序accessToken:[{}]", result);
            JSONObject object = JSONObject.parseObject(result);
            String accessToken = object.getString("access_token");
            if (StringUtils.isBlank(accessToken)) {
                String errorCode = object.getString("errcode");
                String errorMsg = object.getString("errmsg");
                if (StringUtils.isNotBlank(errorCode)) {
                    log.error("获取微信小程序accessToken错误 WeChatUserInfoManager##miniAccessToken errorCode {} errMsg{}", errorCode, errorMsg);
                    return StringUtils.EMPTY;
                }
            }

            return accessToken;
        } catch (Exception e) {
            log.error("获取微信小程序accessToken错误 WeChatUserInfoManager##miniAccessToken error", e);
        }
        return StringUtils.EMPTY;
    }

    @Override
    public WxUserOpenIdResponse getWechatOpenIdList(String openId) {
        String accessToken = this.stableAccessToken();
        String url = GET_USER_URL + accessToken;

        if(StringUtils.isNotBlank(openId)){
            url = url + "&next_openid=" + openId;
        }
        try {
            log.info("获取微信公众号openId列表 req: {}", openId);
            String result = httpClientTemplate.doGet(url);
            log.info("获取微信公众号openId列表 result: {}", result);
            WxUserOpenIdResponse response = JSON.parseObject(result, WxUserOpenIdResponse.class);
            if(StringUtils.isNotBlank(response.getErrcode())){
                log.error("获取微信公众号openId列表错误 WeChatUserInfoManager##getWechatOpenId errorCode:{} errMsg:{}", response.getErrcode(), response.getErrmsg());
                throw new BusinessException("获取微信公众号openId列表错误 WeChatUserInfoManager##getWechatOpenId errorCode:" + response.getErrcode() + "errMsg:" + response.getErrmsg());
            }

            return response;
        }catch (Exception e){
            log.error("获取微信公众号openId列表错误 WeChatUserInfoManager##getWechatOpenId error", e);
            throw new BusinessException("获取微信公众号openId列表错误 WeChatUserInfoManager##getWechatOpenId error");
        }
    }

    @Override
    public List<WxUserInfo> batchGetUserInfo(List<String> openIds) {
        String accessToken = this.stableAccessToken();
        String url = BATCH_USER_INFO_URL + accessToken;

        try {
            JSONObject json = new JSONObject();
            JSONArray userList = new JSONArray();
            for (String openId : openIds) {
                JSONObject user = new JSONObject();
                user.put("openid", openId);
                userList.add(user);
            }
            json.put("user_list", userList);
            log.info("获取微信公众号用户信息 req:{}", JSON.toJSONString(json));
            String result = httpClientTemplate.doPostJson(url, json);
            log.info("获取微信公众号用户信息 resp:{}", result);
            WxUserInfoResponse response = JSON.parseObject(result, WxUserInfoResponse.class);
            if(StringUtils.isNotBlank(response.getErrcode())){
                log.error("获取微信公众号用户信息错误 WeChatUserInfoManager##batchGetUserInfo errorCode {} errMsg{}", response.getErrcode(), response.getErrmsg());
                return Collections.emptyList();
            }
            return response.getUserInfos();
        }catch (Exception e){
            log.error("获取微信公众号用户信息错误 WeChatUserInfoManager##batchGetUserInfo error", e);
        }
        return Collections.emptyList();
    }

}
