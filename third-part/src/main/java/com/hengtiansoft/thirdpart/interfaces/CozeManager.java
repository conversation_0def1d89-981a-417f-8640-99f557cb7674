package com.hengtiansoft.thirdpart.interfaces;


import com.coze.openapi.client.bots.RetrieveBotResp;
import com.coze.openapi.client.chat.CancelChatResp;
import com.coze.openapi.client.common.pagination.PageResp;
import com.coze.openapi.client.connversations.CreateConversationResp;
import com.coze.openapi.client.connversations.message.model.Message;
import com.coze.openapi.client.dataset.document.CreateDocumentResp;
import com.coze.openapi.client.dataset.document.DeleteDocumentResp;
import com.coze.openapi.client.dataset.document.model.Document;
import com.coze.openapi.client.dataset.document.model.DocumentBase;
import com.coze.openapi.client.dataset.document.model.DocumentChunkStrategy;
import com.coze.openapi.service.service.CozeAPI;
import com.hengtiansoft.thirdpart.entity.dto.coze.AiGuideChatDTO;

import java.util.List;

public interface CozeManager {

    CozeAPI getCoze();

    CozeAP<PERSON> getCoze(String secretToken);

    CancelChatResp chatCancel(AiGuideChatDTO dto);

    CreateConversationResp conversationCreate();

    RetrieveBotResp getOnlineInfo(String botId);

    CreateDocumentResp knowledgeDocumentCreate(Long datasetID, List<DocumentBase> documentBases, DocumentChunkStrategy chunkStrategy);

    DeleteDocumentResp knowledgeDocumentDelete(List<Long> documentIds);

    PageResp<Document> knowledgeDocumentList(Long datasetID, Integer page, Integer size);

    PageResp<Message> messageList(AiGuideChatDTO dto);

    String createToken(String code, String redirectUri);

    String getToken();

}
