package com.hengtiansoft.user.dao;

import com.hengtiansoft.user.entity.mapper.CustomerCheckPhoneMapper;
import com.hengtiansoft.user.entity.po.CustomerCheckPhone;
import com.hengtiansoft.user.entity.po.CustomerUser;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 **/
@Repository
public class CustomerCheckPhoneDao {

    @Resource
    private CustomerCheckPhoneMapper customerCheckPhoneMapper;


    public CustomerCheckPhone findByPhone(String phone){
        Example example = new Example(CustomerCheckPhone.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("phone", phone);
        List<CustomerCheckPhone> customerUsers = customerCheckPhoneMapper.selectByExample(example);
        if (CollectionUtils.isNotEmpty(customerUsers)){
            return customerUsers.get(0);
        }
        return null;
    }

    public int saveOne(CustomerCheckPhone customerCheckPhone){
       return customerCheckPhoneMapper.insert(customerCheckPhone);
    }

    public int deleteByPhone(String phone) {
        Example example = new Example(CustomerCheckPhone.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("phone", phone);
        return customerCheckPhoneMapper.deleteByExample(example);
    }

}