package com.hengtiansoft.user.dao;

import com.hengtiansoft.common.enumeration.DeleteFlagEnum;
import com.hengtiansoft.user.entity.mapper.CustomerMiniUserMapper;
import com.hengtiansoft.user.entity.po.CustomerMiniUser;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 **/
@Repository
public class CustomerMiniUserDao {

    @Resource
    private CustomerMiniUserMapper customerMiniUserMapper;

    public List<CustomerMiniUser> findByUserId(Long userId){
        Example example = new Example(CustomerMiniUser.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("userId", userId);
        criteria.andEqualTo("delflag", DeleteFlagEnum.IS_NOT_DELETE.getCode());
        return customerMiniUserMapper.selectByExample(example);
    }

    public List<CustomerMiniUser> findByOpenId(String openId){
        Example example = new Example(CustomerMiniUser.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("openId", openId);
        criteria.andEqualTo("delflag", DeleteFlagEnum.IS_NOT_DELETE.getCode());
        return customerMiniUserMapper.selectByExample(example);
    }


    public List<CustomerMiniUser> findByUnionId(String unionId){
        Example example = new Example(CustomerMiniUser.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("unionId", unionId);
        criteria.andEqualTo("delflag", DeleteFlagEnum.IS_NOT_DELETE.getCode());
        return customerMiniUserMapper.selectByExample(example);
    }

    public List<CustomerMiniUser> findByOpenIdAndUserId(String openId, Long userId){
        Example example = new Example(CustomerMiniUser.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("openId", openId);
        criteria.andEqualTo("userId", userId);
        criteria.andEqualTo("delflag", DeleteFlagEnum.IS_NOT_DELETE.getCode());
        return customerMiniUserMapper.selectByExample(example);
    }

    public List<CustomerMiniUser> findByOpenIdAndUserIds(String openId, List<Long> userIds){
        Example example = new Example(CustomerMiniUser.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("openId", openId);
        criteria.andIn("userId", userIds);
        criteria.andEqualTo("delflag", DeleteFlagEnum.IS_NOT_DELETE.getCode());
        return customerMiniUserMapper.selectByExample(example);
    }

    public int saveOne(CustomerMiniUser customerMiniUser){
        return customerMiniUserMapper.insertSelective(customerMiniUser);
    }

    public int updateOne(CustomerMiniUser customerMiniUser){
        return customerMiniUserMapper.updateByPrimaryKeySelective(customerMiniUser);
    }
    public void updateByUserId(CustomerMiniUser customerMiniUser) {
        Example example = new Example(CustomerMiniUser.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("userId", customerMiniUser.getUserId());
        customerMiniUserMapper.updateByExampleSelective(customerMiniUser, example);
    }
}