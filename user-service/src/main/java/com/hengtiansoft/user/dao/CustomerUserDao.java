package com.hengtiansoft.user.dao;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.hengtiansoft.common.enumeration.DeleteFlagEnum;
import com.hengtiansoft.common.rule.*;
import com.hengtiansoft.common.util.BeanUtils;
import com.hengtiansoft.common.util.StreamUtils;
import com.hengtiansoft.user.entity.dto.*;
import com.hengtiansoft.user.entity.mapper.CustomerUserMapper;
import com.hengtiansoft.user.entity.po.CustomerUser;
import com.hengtiansoft.user.entity.vo.CustomerUserMoreVO;
import com.hengtiansoft.user.entity.vo.UserListVO;
import com.hengtiansoft.user.entity.vo.UserTypeCntVO;
import com.hengtiansoft.user.enums.UserIsBuyEnum;
import com.hengtiansoft.user.enums.UserLogOffEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.*;

/**
 * 用户dao
 *
 * <AUTHOR>
 * @since 26.03.2020
 */
@Repository
public class CustomerUserDao {
    @Resource
    private CustomerUserMapper customerUserMapper;

    public List<CustomerUser> findAllByCondition(CustomerUserPageDTO dto) {

        return customerUserMapper.select(BeanUtils.copy(dto, CustomerUser::new));
    }

    public int updateOne(CustomerUser customerUser) {
       return customerUserMapper.updateByPrimaryKeySelective(customerUser);
    }

    public int updateByUnionId(String unionId, CustomerUser user){
        Example example = new Example(CustomerUser.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("unionId", unionId);
        return customerUserMapper.updateByExampleSelective(user, example);
    }

    public List<CustomerUser> findAllWithUnionId(){
        Example example = new Example(CustomerUser.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andNotEqualTo("unionId", Strings.EMPTY);
        criteria.andIsNotNull("unionId");
        example.orderBy("id").asc();
        return customerUserMapper.selectByExample(example);
    }

    public List<CustomerUser> findByOpenId(String openId){
        Example example = new Example(CustomerUser.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("openId", openId);
        criteria.andNotEqualTo("wechartOpenId", Strings.EMPTY);
        criteria.andIsNotNull("wechartOpenId");
        return customerUserMapper.selectByExample(example);
    }

    public List<CustomerUser> findByUnionIds(List<String> unionIds){
        Example example = new Example(CustomerUser.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andIn("unionId", unionIds);
        return customerUserMapper.selectByExample(example);
    }

    public int insertOne(CustomerUser customerUser) {
        return customerUserMapper.insertSelective(customerUser);
    }

    public CustomerUser findById(Long id) {
        return customerUserMapper.selectByPrimaryKey(id);
    }

    public List<CustomerUser> findByIds(List<Long> ids) {
        if(CollectionUtils.isEmpty(ids)){
            return new ArrayList<>();
        }
        Condition condition = new Condition(CustomerUser.class);
        condition.createCriteria().andIn("id", ids);
        return customerUserMapper.selectByCondition(condition);
    }

    public List<CustomerUser> findByPageDTO(UserQueryDTO dto, String orderByColumn) {
        Condition condition = new Condition(CustomerUser.class);
        Example.Criteria criteria = condition.createCriteria();
        if (!StringUtils.isEmpty(dto.getPhone())) {
            criteria.andEqualTo("phone", dto.getPhone());
        }
        if (CollectionUtils.isNotEmpty(dto.getIds())) {
            criteria.andIn("id", dto.getIds());
        }
        if(dto.getNickNameIsNotNull() != null && dto.getNickNameIsNotNull()){
            criteria.andIsNotNull("nickName");
        }
        if(dto.getOrderNickNameIsNull() != null && dto.getOrderNickNameIsNull()){
            criteria.andIsNull("orderNickName");
        }
        if(StringUtils.isNotBlank(dto.getNickName())){
            criteria.andEqualTo("nickName", dto.getNickName());
        }
        criteria.andEqualTo("logOff", UserLogOffEnum.UNREGISTERED.getCode());
        criteria.andEqualTo("delflag", DeleteFlagEnum.IS_NOT_DELETE.getCode());
        condition.orderBy(orderByColumn).desc();
        return customerUserMapper.selectByCondition(condition);
    }

    @DS("db3")
    public List<CustomerUser> findByPageDTO(UserDataDTO dto) {
        Condition condition = new Condition(CustomerUser.class);
        Example.Criteria criteria = condition.createCriteria();
        if (Objects.nonNull(dto.getUserId())) {
            criteria.andEqualTo("id", dto.getUserId());
        }
        if(Objects.nonNull(dto.getStartUpdateTime())){
            criteria.andGreaterThanOrEqualTo("updateTime", dto.getStartUpdateTime());
        }
        if(Objects.nonNull(dto.getEndUpdateTime())){
            criteria.andLessThanOrEqualTo("updateTime", dto.getEndUpdateTime());
        }
        criteria.andEqualTo("delflag", DeleteFlagEnum.IS_NOT_DELETE.getCode());
        condition.orderBy("updateTime").asc();
        return customerUserMapper.selectByCondition(condition);
    }

    public List<CustomerUser> findFissionUser(UserQueryDTO dto){
        Condition condition = new Condition(CustomerUser.class, true, true);
        Example.Criteria criteria = condition.createCriteria();
        if (Objects.nonNull(dto.getUserType())) {
            criteria.andEqualTo("userType", dto.getUserType());
        }
        if (Objects.nonNull(dto.getFreeTrialId())) {
            criteria.andEqualTo("activityId", dto.getFreeTrialId());
        }
        if (Objects.nonNull(dto.getUserId())) {
            criteria.andEqualTo("id", dto.getUserId());
        }
        if(StringUtils.isNotBlank(dto.getNickName())){
            criteria.andLike("orderNickName", "%" + dto.getNickName() + "%" );
        }
        if(StringUtils.isNotBlank(dto.getPhone())){
            criteria.andEqualTo("phone", dto.getPhone());
        }
        criteria.andEqualTo("delflag", DeleteFlagEnum.IS_NOT_DELETE.getCode());
        condition.setOrderByClause("first_login_time desc");
        return customerUserMapper.selectByCondition(condition);
    }

    public int cntFissionUser(Integer userType, Date time){
        Condition condition = new Condition(CustomerUser.class, true, true);
        Example.Criteria criteria = condition.createCriteria();
        if (Objects.nonNull(userType)) {
            criteria.andEqualTo("userType", userType);
        }
        if (Objects.nonNull(time)) {
            criteria.andGreaterThanOrEqualTo("firstLoginTime", time);
        }
        criteria.andEqualTo("delflag", DeleteFlagEnum.IS_NOT_DELETE.getCode());
        return customerUserMapper.selectCountByExample(condition);
    }

    public List<CustomerUser> findByEmpNos(List<String> empNoList) {
        List<CustomerUser> list = new LinkedList<>();
        if (!org.springframework.util.CollectionUtils.isEmpty(empNoList)) {
            Condition condition = new Condition(CustomerUser.class);
            condition.createCriteria().andIn("empNo", empNoList);
            list = customerUserMapper.selectByCondition(condition);
        }
        return list;
    }

    /**
     * 更新状态
     *
     * @param empNos
     * @param beforeStatus 更新前状态
     * @param afterStatus 更新后状态
     */
    public void updateStatus(List<String> empNos,Integer beforeStatus,Integer afterStatus) {
        List<List<String>> split = StreamUtils.split(empNos, 1000);
        split.forEach(data -> {
            Condition condition = new Condition(CustomerUser.class);
            condition.createCriteria().andIn("empNo", data).andEqualTo("status", beforeStatus).andEqualTo("delflag",
                DeleteFlagEnum.IS_NOT_DELETE.getCode());
            CustomerUser record = new CustomerUser();
            record.setStatus(afterStatus);
            customerUserMapper.updateByConditionSelective(record, condition);
        });
    }

    /**
     * 查询未同步的客户信息
     *
     * @return
     */
    public List<CustomerUser> findUnSyn() {
        Condition condition = new Condition(CustomerUser.class);
        condition.createCriteria().andEqualTo("synFlag", 0);
        condition.orderBy("createTime").desc();
        return customerUserMapper.selectByCondition(condition);
    }

    /**
     * 更新为已同步
     *
     * @param ids
     */
    public void updateSynFlag(List<Long> ids) {
        List<List<Long>> split = StreamUtils.split(ids, 1000);
        split.forEach(data -> {
            Condition condition = new Condition(CustomerUser.class);
            condition.createCriteria().andIn("id", data);
            CustomerUser record = new CustomerUser();
//            record.setSynFlag(1);
            customerUserMapper.updateByConditionSelective(record, condition);
        });
    }

    public List<CustomerUser> findByPhone(String phone) {
        Condition condition = new Condition(CustomerUser.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("phone", phone);
        criteria.andEqualTo("logOff", UserLogOffEnum.UNREGISTERED.getCode());
        return customerUserMapper.selectByCondition(condition);
    }

    public List<CustomerUser> findLikeByPhone(String phone) {
        Condition condition = new Condition(CustomerUser.class);
        condition.createCriteria().andLike("phone", phone + "%");
        return customerUserMapper.selectByCondition(condition);
    }

    public Integer findCountByDateRange(Date startDate,Date endDate){
        Condition condition = new Condition(CustomerUser.class);
        condition.createCriteria().andBetween("createTime",startDate,endDate);
        return customerUserMapper.selectCountByCondition(condition);
    }

    public Integer findAllCount(){
        return customerUserMapper.selectCount(new CustomerUser());
    }

    /**
     * 根据手机号或者用户名查找客户信息
     *
     * @return
     */
    public List<CustomerUser> findByUserNameOrPhone(String userName,String phone) {
        Condition condition = new Condition(CustomerUser.class);
        Example.Criteria criteria = condition.createCriteria();
        if(StringUtils.isNotEmpty(userName)){
            criteria.andEqualTo("userName", userName);
        }
        if(StringUtils.isNotBlank(phone)){
            criteria.andEqualTo("phone", phone);
        }

        if(StringUtils.isEmpty(userName) && StringUtils.isBlank(phone)){
            return new ArrayList<>(0);
        }
        return customerUserMapper.selectByCondition(condition);
    }

    public CustomerUser findTmallUserById(Long userId){
        Example exception = new Example(CustomerUser.class);
        Example.Criteria criteria = exception.createCriteria();
        criteria.andEqualTo("id", userId);
        criteria.andEqualTo("userType", 2);
        return customerUserMapper.selectOneByExample(exception);
    }

    public List<UserListVO> findList(UserListDTO userQueryDTO){
        return customerUserMapper.findList(userQueryDTO);
    }

    public Integer findListCount(UserListDTO userQueryDTO){
        return customerUserMapper.findListCount(userQueryDTO);
    }

    public List<UserListVO> privilegeFindList(UserQueryDTO userQueryDTO){
        List<UserListVO> customerUsers = customerUserMapper.findPrivilege(userQueryDTO);
        if (CollectionUtils.isEmpty(customerUsers)){
            return Collections.emptyList();
        }
        return customerUsers;
    }

    public void  saveList(List<CustomerUser> customerUsers){
        customerUserMapper.insertList(customerUsers);
    }

    public List<CustomerUser> findLastLogoffUser(String phone) {
        Condition condition = new Condition(CustomerUser.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("phone", phone);
        criteria.andEqualTo("logOff", UserLogOffEnum.LOGGED_OUT.getCode());
        condition.orderBy("id").desc();
        return customerUserMapper.selectByCondition(condition);
    }

    public List<CustomerUser> findByPhones(List<String> phones) {
        Condition condition = new Condition(CustomerUser.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andIn("phone", phones);
        criteria.andEqualTo("logOff", UserLogOffEnum.UNREGISTERED.getCode());
        condition.orderBy("id").desc();
        return customerUserMapper.selectByCondition(condition);
    }

    public List<CustomerUser> findByUnionId(String unionId){
        Condition condition = new Condition(CustomerUser.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("unionId", unionId);
        return customerUserMapper.selectByCondition(condition);
    }

    public List<CustomerUser> findByCondition(UserMatchDTO dto){
        Condition condition = new Condition(CustomerUser.class, true, true);
        Example.Criteria criteria = condition.createCriteria();
        if(CollectionUtils.isNotEmpty(dto.getOpenIdList())){
            criteria.andIn("openId", dto.getOpenIdList());
        }
        if(CollectionUtils.isNotEmpty(dto.getMd5OpenIdList())){
            criteria.andIn("md5OpenId", dto.getMd5OpenIdList());
        }
        if(StringUtils.isNotBlank(dto.getNickName())){
            criteria.andLike("orderNickName", "%" + dto.getNickName() + "%");
        }
        if(CollectionUtils.isNotEmpty(dto.getPhoneList())){
            criteria.andIn("phone", dto.getPhoneList());
        }
        if(CollectionUtils.isNotEmpty(dto.getGradeList())){
            criteria.andIn("grade", dto.getGradeList());
        }
        if(CollectionUtils.isNotEmpty(dto.getIds())){
            criteria.andIn("id", dto.getIds());
        }
        if(Objects.nonNull(dto.getRegisterTimeStart())){
            criteria.andGreaterThanOrEqualTo("firstLoginTime", dto.getRegisterTimeStart());
        }
        if(Objects.nonNull(dto.getRegisterTimeEnd())){
            criteria.andLessThanOrEqualTo("firstLoginTime", dto.getRegisterTimeEnd());
        }
        criteria.andEqualTo("logOff", UserLogOffEnum.UNREGISTERED.getCode());
        return customerUserMapper.selectByCondition(condition);
    }

    @DS("db3")
    public List<Long> dealRuleCompute(DealRule rule) {
        return customerUserMapper.dealRuleCompute(rule);
    }
    @DS("db3")
    public List<Long> amountRuleCompute(AmountRule rule) {
        return customerUserMapper.amountRuleCompute(rule);
    }

    public int updateBatch(List<CustomerUser> user4Updates) {
        return customerUserMapper.updateBatch(user4Updates);
    }
    @DS("db3")
    public List<Long> pointRuleCompute(PointRule rule) {
        return customerUserMapper.pointRuleCompute(rule);
    }
    @DS("db3")
    public List<Long> averageRuleCompute(AverageRule rule) {
        return customerUserMapper.averageRuleCompute(rule);
    }

    @DS("db3")
    public List<Long> haveDealRuleCompute(HaveDealRule rule) {
        return customerUserMapper.haveDealRuleCompute(rule);
    }

    public List<CustomerUser> findbyIsBuy(Long userId) {
        Condition condition = new Condition(CustomerUser.class);
        Example.Criteria criteria = condition.createCriteria();
        if(Objects.nonNull(userId)){
            criteria.andEqualTo("id", userId);
        }
        criteria.andEqualTo("isBuy", UserIsBuyEnum.NO.getCode());
        criteria.andEqualTo("logOff", UserLogOffEnum.UNREGISTERED.getCode());
        return customerUserMapper.selectByExample(condition);
    }
    @DS("db3")
    public List<Long> gradeRuleCompute(GradeRule rule) {
        return customerUserMapper.gradeRuleCompute(rule);
    }
    @DS("db3")
    public List<Long> shopBuyRuleCompute(ShopBuyRule rule) {
        return customerUserMapper.shopBuyRuleCompute(rule);
    }
    @DS("db3")
    public List<Long> shopNotBuyRuleCompute(ShopNotBuyRule rule) {
        return customerUserMapper.shopNotBuyRuleCompute(rule);
    }
    @DS("db3")
    public List<Long> realDealRuleCompute(RealDealRule rule) {
        return customerUserMapper.realDealRuleCompute(rule);
    }
    @DS("db3")
    public List<Long> realAmountRuleCompute(RealAmountRule rule) {
        return customerUserMapper.realAmountRuleCompute(rule);
    }
    @DS("db3")
    public List<Long> orderAverageRuleCompute(OrderAverageRule rule) {
        return customerUserMapper.orderAverageRuleCompute(rule);
    }
    @DS("db3")
    public List<Long> buybackCycleRuleCompute(BuybackCycleRule rule) {
        return customerUserMapper.buybackCycleRuleCompute(rule);
    }
    @DS("db3")
    public List<Long> shopVisitRuleCompute(ShopVisitRule rule) {
        return customerUserMapper.shopVisitRuleCompute(rule);
    }
    @DS("db3")
    public List<Long> shopNotVisitRuleCompute(ShopNotVisitRule rule) {
        return customerUserMapper.shopNotVisitRuleCompute(rule);
    }
    @DS("db3")
    public List<Long> shopSubmitRuleCompute(ShopSubmitRule rule) {
        return customerUserMapper.shopSubmitRuleCompute(rule);
    }
    @DS("db3")
    public List<Long> shopNotSubmitRuleCompute(ShopNotSubmitRule rule) {
        return customerUserMapper.shopNotSubmitRuleCompute(rule);
    }
    @DS("db3")
    public List<Long> buyCardRuleCompute(BuyCardRule rule) {
        return customerUserMapper.buyCardRuleCompute(rule);
    }
    @DS("db3")
    public List<Long> renewTimeRuleCompute(RenewTimeRule rule) {
        return customerUserMapper.renewTimeRuleCompute(rule);
    }
    @DS("db3")
    public List<Long> buyCateRuleCompute(BuyCateRule rule) {
        return customerUserMapper.buyCateRuleCompute(rule);
    }
    @DS("db3")
    public List<Long> bindCateRuleCompute(BindCateRule rule) {
        return customerUserMapper.bindCateRuleCompute(rule);
    }
    @DS("db3")
    public List<Long> firstBindRuleCompute(FirstBindRule rule) {
        return customerUserMapper.firstBindRuleCompute(rule);
    }
    @DS("db3")
    public List<Long> expireUserRuleCompute(ExpireUserRule rule) {
        return customerUserMapper.expireUserRuleCompute(rule);
    }
    @DS("db3")
    public List<Long> expireUserNotRuleCompute(ExpireUserRule rule) {
        return customerUserMapper.expireUserNotRuleCompute(rule);
    }
    @DS("db3")
    public List<Long> renewUserRuleCompute(RenewUserRule rule) {
        return customerUserMapper.renewUserRuleCompute(rule);
    }
    @DS("db3")
    public List<Long> renewUserNotRuleCompute(RenewUserRule rule) {
        return customerUserMapper.renewUserNotRuleCompute(rule);
    }
    @DS("db3")
    public List<Long> cardWillExpireRuleCompute(CardWillExpireRule rule) {
        return customerUserMapper.cardWillExpireRuleCompute(rule);
    }
    @DS("db3")
    public List<Long> recentNotDispatchRuleCompute(RecentNotDispatchRule rule) {
        return customerUserMapper.recentNotDispatchRuleCompute(rule);
    }
    @DS("db3")
    public List<Long> recentAddressRuleCompute(RecentAddressRule rule) {
        return customerUserMapper.recentAddressRuleCompute(rule);
    }
    @DS("db3")
    public List<Long> recentCardPlatformRuleCompute(RecentCardPlatformRule rule) {
        return customerUserMapper.recentCardPlatformRuleCompute(rule);
    }
    @DS("db3")
    public List<Long> mostPlatformRuleCompute(MostPlatformRule rule) {
        return customerUserMapper.mostPlatformRuleCompute(rule);
    }
    @DS("db3")
    public List<Long> autoLabelRuleCompute(AutoLabelRule rule) {
        return customerUserMapper.autoLabelRuleCompute(rule);
    }
    @DS("db3")
    public List<Long> autoLabelAndRuleCompute(AutoLabelRule rule) {
        return customerUserMapper.autoLabelAndRuleCompute(rule);
    }
    @DS("db3")
    public List<Long> wechatActivityUserRuleCompute(WechatActivityUserRule rule) {
        return customerUserMapper.wechatActivityUserRuleCompute(rule);
    }
    @DS("db3")
    public List<Long> wechatActivityUserNotRuleCompute(WechatActivityUserRule rule) {
        return customerUserMapper.wechatActivityUserNotRuleCompute(rule);
    }
    @DS("db3")
    public List<Long> manualFullReduceRuleCompute(ManualFullReduceRule rule) {
        return customerUserMapper.manualFullReduceRuleCompute(rule);
    }

    @DS("db3")
    public List<Long> manualPtActivityRuleCompute(ManualPtActivityRule rule) {
        return customerUserMapper.manualPtActivityRuleCompute(rule);
    }

    @DS("db3")
    public List<Long> joinFreeTrialRuleCompute(JoinMarketingRule rule) {
        return customerUserMapper.joinFreeTrialRuleCompute(rule);
    }

    @DS("db3")
    public List<Long> joinDiscountRuleCompute(JoinMarketingRule rule) {
        return customerUserMapper.joinDiscountRuleCompute(rule);
    }

    @DS("db3")
    public List<Long> joinPointMallRuleCompute(JoinMarketingRule rule) {
        return customerUserMapper.joinPointMallRuleCompute(rule);
    }

    @DS("db3")
    public List<Long> joinLuckyDrawRuleCompute(JoinMarketingRule rule) {
        return customerUserMapper.joinLuckyDrawRuleCompute(rule);
    }

    @DS("db3")
    public List<Long> joinCouponRuleCompute(JoinMarketingRule rule) {
        return customerUserMapper.joinCouponRuleCompute(rule);
    }

    @DS("db3")
    public List<Long> joinFullReduceRuleCompute(JoinMarketingRule rule) {
        return customerUserMapper.joinFullReduceRuleCompute(rule);
    }

    @DS("db3")
    public List<Long> joinFreeTasteRuleCompute(JoinMarketingRule rule) {
        return customerUserMapper.joinFreeTasteRuleCompute(rule);
    }

    @DS("db3")
    public List<Long> joinFullTrialRuleCompute(JoinMarketingRule rule) {
        return customerUserMapper.joinFullTrialRuleCompute(rule);
    }

    @DS("db3")
    public List<Long> joinPtRuleCompute(JoinMarketingRule rule) {
        return customerUserMapper.joinPtRuleCompute(rule);
    }

    @DS("db3")
    public List<Long> fansRuleCompute(FansRule rule) {
        return customerUserMapper.fansRuleCompute(rule);
    }

    @DS("db3")
    public List<Long> couponNotUsedRuleCompute(CouponNotUsedRule rule) {
        return customerUserMapper.couponNotUsedRuleCompute(rule);
    }

    @DS("db3")
    public Long testTimeout() {
        // return customerUserMapper.testTimeout();
        return 0L;
    }

    public List<CustomerUser> findByOrderNickNameLike(String orderNickName, String phone) {
        Condition condition = new Condition(CustomerUser.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andLike("orderNickName", orderNickName + "%");
        criteria.andEqualTo("logOff", UserLogOffEnum.UNREGISTERED.getCode());
        criteria.andNotEqualTo("phone", phone);
        return customerUserMapper.selectByExample(condition);
    }

    public List<Long> findAllUserIdByUserId(Long userId){
        return customerUserMapper.findAllUserIdByUserId(userId);
    }

    public List<UserBuyDateDTO> findMinBuyDate(List<Long> ids) {
        return customerUserMapper.findMinBuyDate(ids);
    }

    public List<CustomerUserMoreVO> findOrderInfo(List<Long> ids) {
        return customerUserMapper.findOrderInfo(ids);
    }

    public List<CustomerUserMoreVO> findOrderInfo90(List<Long> ids) {
        return customerUserMapper.findOrderInfo90(ids);
    }

    public List<CustomerUserMoreVO> findOrderTime(List<Long> ids) {
        return customerUserMapper.findOrderTime(ids);
    }

    public List<CustomerUser> findNullMD5OpenId(){
        return customerUserMapper.findNullMD5OpenId();
    }

    public List<UserTypeCntVO> findUserCnt(Integer userType){
        return customerUserMapper.findUserCnt(userType);
    }

    @DS("selectDB")
    public List<CustomerUser> findByConditionSql(UserMatchDTO dto) {
        return customerUserMapper.findByConditionSql(dto);
    }

    @DS("db3")
    public List<Long> birthdayUserCompute(UserBirthdayRule rule) {
        return customerUserMapper.birthdayUserCompute(rule);
    }

    @DS("db3")
    public List<Long> miniProgramRegisterTimeRuleCompute(MiniProgramRegisterTimeRule rule) {
        return customerUserMapper.miniProgramRegisterTimeRuleCompute(rule);
    }

    @DS("db3")
    public List<Long> joinMemberTimeRuleCompute(JoinMemberTimeRule rule) {
        return customerUserMapper.joinMemberTimeRuleCompute(rule);
    }

    @DS("db3")
    public List<Long> dispatchSignRuleCompute(DispatchSignRule rule) {
        return customerUserMapper.dispatchSignRuleCompute(rule);
    }

    @DS("db3")
    public List<Long> recentPayRuleCompute(RecentPayRule rule) {
        return customerUserMapper.recentPayRuleCompute(rule);
    }

    @DS("db3")
    public List<Long> payAmountRuleCompute(PayAmountRule rule) {
        return customerUserMapper.payAmountRuleCompute(rule);
    }

    @DS("db3")
    public List<Long> productBuyOfProductIdRuleCompute(ProductBuyRule rule) {
        return customerUserMapper.productBuyOfProductIdRuleCompute(rule);
    }

    @DS("db3")
    public List<Long> productBuyOfGroupIdRuleCompute(ProductBuyRule rule) {
        return customerUserMapper.productBuyOfGroupIdRuleCompute(rule);
    }

    @DS("db3")
    public List<Long> productBuyOfCategoryIdRuleCompute(ProductBuyRule rule) {
        return customerUserMapper.productBuyOfCategoryIdRuleCompute(rule);
    }

    @DS("db3")
    public List<Long> productNotBuyOfProductIdRuleCompute(ProductNotBuyRule rule) {
        return customerUserMapper.productNotBuyOfProductIdRuleCompute(rule);
    }

    @DS("db3")
    public List<Long> productNotBuyOfGroupIdRuleCompute(ProductNotBuyRule rule) {
        return customerUserMapper.productNotBuyOfGroupIdRuleCompute(rule);
    }

    @DS("db3")
    public List<Long> productNotBuyOfCategoryIdRuleCompute(ProductNotBuyRule rule) {
        return customerUserMapper.productNotBuyOfCategoryIdRuleCompute(rule);
    }

    @DS("db3")
    public List<Long> shopCartOfProductIdRuleCompute(ShopCartRule rule) {
        return customerUserMapper.shopCartOfProductIdRuleCompute(rule);
    }

    @DS("db3")
    public List<Long> shopCartOfGroupIdRuleCompute(ShopCartRule rule) {
        return customerUserMapper.shopCartOfGroupIdRuleCompute(rule);
    }

    @DS("db3")
    public List<Long> shopCartOfCategoryIdRuleCompute(ShopCartRule rule) {
        return customerUserMapper.shopCartOfCategoryIdRuleCompute(rule);
    }

    @DS("db3")
    public List<Long> bindNotBuyOfProductIdRuleCompute(BindNotBuyRule rule) {
        return customerUserMapper.bindNotBuyOfProductIdRuleCompute(rule);
    }

    @DS("db3")
    public List<Long> bindNotBuyOfGroupIdRuleCompute(BindNotBuyRule rule) {
        return customerUserMapper.bindNotBuyOfGroupIdRuleCompute(rule);
    }

    @DS("db3")
    public List<Long> bindNotBuyOfCategoryIdRuleCompute(BindNotBuyRule rule) {
        return customerUserMapper.bindNotBuyOfCategoryIdRuleCompute(rule);
    }

    @DS("db3")
    public List<Long> bindNotBuyOfCardCategoryIdRuleCompute(BindNotBuyRule rule) {
        return customerUserMapper.bindNotBuyOfCardCategoryIdRuleCompute(rule);
    }

    @DS("db3")
    public List<Long> bindBuyOfProductIdRuleCompute(BindBuyRule rule) {
        return customerUserMapper.bindBuyOfProductIdRuleCompute(rule);
    }

    @DS("db3")
    public List<Long> bindBuyOfGroupIdRuleCompute(BindBuyRule rule) {
        return customerUserMapper.bindBuyOfGroupIdRuleCompute(rule);
    }

    @DS("db3")
    public List<Long> bindBuyOfCategoryIdRuleCompute(BindBuyRule rule) {
        return customerUserMapper.bindBuyOfCategoryIdRuleCompute(rule);
    }

    @DS("db3")
    public List<Long> bindBuyOfCardCategoryIdRuleCompute(BindBuyRule rule) {
        return customerUserMapper.bindBuyOfCardCategoryIdRuleCompute(rule);
    }

    @DS("db3")
    public List<Long> bindBuyCardRuleCompute(BindBuyCardRule rule) {
        return customerUserMapper.bindBuyCardRuleCompute(rule);
    }

    @DS("db3")
    public List<Long> bindBuyProductOfProductIdRuleCompute(BindBuyProductRule rule) {
        return customerUserMapper.bindBuyProductOfProductIdRuleCompute(rule);
    }

    @DS("db3")
    public List<Long> bindBuyProductOfGroupIdRuleCompute(BindBuyProductRule rule) {
        return customerUserMapper.bindBuyProductOfGroupIdRuleCompute(rule);
    }

    @DS("db3")
    public List<Long> bindBuyProductOfCategoryIdRuleCompute(BindBuyProductRule rule) {
        return customerUserMapper.bindBuyProductOfCategoryIdRuleCompute(rule);
    }

    @DS("db3")
    public List<Long> bindCardRuleCompute(BindCardRule rule) {
        return customerUserMapper.bindCardRuleCompute(rule);
    }

    @DS("db3")
    public List<Long> privilegeNotSetRuleCompute(PrivilegeNotSetRule rule) {
        if(CollectionUtils.isEmpty(rule.getCardNumbers())){
            return new ArrayList<>();
        }
        return customerUserMapper.privilegeNotSetRuleCompute(rule);
    }

    @DS("db3")
    public List<Long> cardTotalRemainingCountRuleCompute(CardTotalRemainingCountRule rule) {
        return customerUserMapper.cardTotalRemainingCountRuleCompute(rule);
    }

    @DS("db3")
    public List<Long> findUpgradeUserIdsByPrivilegeLabelId(PrivilegeNotSetRule rule){
        return customerUserMapper.findUpgradeUserIdsByPrivilegeLabelId(rule);
    }

    @DS("db3")
    public List<CustomerUserByPhoneDTO> findUserIdGroupByPhone(String phone){
        return customerUserMapper.findUserIdGroupByPhone(phone);
    }

    @DS("db3")
    public List<CustomerUser> findNonMember(Date timeStart){
        return customerUserMapper.findNonMember(timeStart);
    }

    @DS("db3")
    public List<Long> lastDealRuleCompute(LastDealRule rule) {
        return customerUserMapper.lastDealRuleCompute(rule);
    }

    @DS("db3")
    public List<Long> productBuyOfCardCategoryIdRuleCompute(ProductBuyRule rule) {
        return customerUserMapper.productBuyOfCardCategoryIdRuleCompute(rule);
    }

    @DS("db3")
    public List<Long> shopCartOfCardCategoryIdRuleCompute(ShopCartRule rule) {
        return customerUserMapper.shopCartOfCardCategoryIdRuleCompute(rule);
    }

    @DS("db3")
    public List<Long> joinPrivilegeLabelActivityRuleCompute(JoinMarketingRule rule) {
        return customerUserMapper.joinPrivilegeLabelActivityRuleCompute(rule);
    }

    @DS("db3")
    public List<Long> productNotBuyOfCardCategoryIdRuleCompute(ProductNotBuyRule rule) {
        return customerUserMapper.productNotBuyOfCardCategoryIdRuleCompute(rule);
    }

    @DS("db3")
    public List<Long> notBindCardRuleCompute(NotBindUserRule rule) {
        return customerUserMapper.notBindCardRuleCompute(rule);
    }

    @DS("db3")
    public List<Long> bindBuyProductOfCardCategoryIdRuleCompute(BindBuyProductRule rule) {
        return customerUserMapper.bindBuyProductOfCardCategoryIdRuleCompute(rule);
    }

    @DS("db3")
    public List<CustomerUser> findByUserIds(List<Long> userIds) {
        if(CollectionUtils.isEmpty(userIds)){
            return new ArrayList<>();
        }
        Condition condition = new Condition(CustomerUser.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andIn("id", userIds);
        criteria.andEqualTo("delflag", DeleteFlagEnum.IS_NOT_DELETE.getCode());
        return customerUserMapper.selectByExample(condition);
    }

    @DS("db3")
    public CustomerUser findByNickName(String nickName, Long id){
        return customerUserMapper.findByNickName(nickName, id);
    }

}
