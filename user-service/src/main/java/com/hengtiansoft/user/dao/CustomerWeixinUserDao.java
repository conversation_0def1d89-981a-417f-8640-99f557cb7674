package com.hengtiansoft.user.dao;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.hengtiansoft.common.enumeration.DeleteFlagEnum;
import com.hengtiansoft.common.util.StreamUtils;
import com.hengtiansoft.user.entity.mapper.CustomerWeixinUserMapper;
import com.hengtiansoft.user.entity.po.CustomerWeixinUser;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Component
public class CustomerWeixinUserDao {

    @Resource
    private CustomerWeixinUserMapper customerWeixinUserMapper;

    public List<CustomerWeixinUser> findByOpenIds(List<String> openIds){
        if(CollectionUtils.isEmpty(openIds)){
            return new ArrayList<>(0);
        }
        Example example = new Example(CustomerWeixinUser.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andIn("openId", openIds);
        criteria.andEqualTo("delflag", DeleteFlagEnum.IS_NOT_DELETE.getCode());
        return customerWeixinUserMapper.selectByExample(example);
    }

    public CustomerWeixinUser findByUnionId(String unionId){
        Example example = new Example(CustomerWeixinUser.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("unionId", unionId);
        criteria.andEqualTo("delflag", DeleteFlagEnum.IS_NOT_DELETE.getCode());
        return StreamUtils.getFirst(customerWeixinUserMapper.selectByExample(example));
    }

    public List<CustomerWeixinUser> findByUnionIds(List<String> unionIds){
        Example example = new Example(CustomerWeixinUser.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andIn("unionId", unionIds);
        criteria.andEqualTo("delflag", DeleteFlagEnum.IS_NOT_DELETE.getCode());
        return customerWeixinUserMapper.selectByExample(example);
    }

    public void batchUpdateByOpenId(List<CustomerWeixinUser> userList){
        customerWeixinUserMapper.batchUpdate(userList);
    }

    public void batchInsert(List<CustomerWeixinUser> userList){
        customerWeixinUserMapper.insertList(userList);
    }

    @DS("db3")
    public List<CustomerWeixinUser> selectSubByTime(List<String> unionIds, Date startTime, Date endTime){
        if(CollectionUtils.isEmpty(unionIds)){
            return new ArrayList<>(0);
        }
        return customerWeixinUserMapper.selectSubByTime(unionIds, startTime, endTime);
    }

}
