package com.hengtiansoft.user.entity.mapper;

import com.hengtiansoft.common.baseMapper.CrudMapper;
import com.hengtiansoft.common.rule.*;
import com.hengtiansoft.user.entity.dto.*;
import com.hengtiansoft.user.entity.po.CustomerUser;
import com.hengtiansoft.user.entity.vo.CustomerUserMoreVO;
import com.hengtiansoft.user.entity.vo.UserListVO;
import com.hengtiansoft.user.entity.vo.UserTypeCntVO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface CustomerUserMapper extends CrudMapper<CustomerUser> {

    List<UserListVO> findList(UserListDTO userQueryDTO);
    Integer findListCount(UserListDTO userQueryDTO);

    List<UserListVO> findPrivilege(UserQueryDTO userQueryDTO);

    void saveList(@Param("list") List<CustomerUser> list);

    List<Long> dealRuleCompute(@Param("rule") DealRule rule);

    List<Long> amountRuleCompute(@Param("rule") AmountRule rule);

    int updateBatch(List<CustomerUser> user4Updates);

    List<Long> pointRuleCompute(@Param("rule") PointRule rule);

    List<Long> averageRuleCompute(@Param("rule") AverageRule rule);

    List<Long> haveDealRuleCompute(@Param("rule") HaveDealRule rule);

    List<Long> gradeRuleCompute(@Param("rule") GradeRule rule);

    List<Long> shopBuyRuleCompute(@Param("rule") ShopBuyRule rule);

    List<Long> shopNotBuyRuleCompute(@Param("rule") ShopNotBuyRule rule);

    List<Long> realDealRuleCompute(@Param("rule") RealDealRule rule);

    List<Long> realAmountRuleCompute(@Param("rule") RealAmountRule rule);

    List<Long> orderAverageRuleCompute(@Param("rule") OrderAverageRule rule);

    List<Long> buybackCycleRuleCompute(@Param("rule") BuybackCycleRule rule);

    List<Long> shopVisitRuleCompute(@Param("rule") ShopVisitRule rule);

    List<Long> shopNotVisitRuleCompute(@Param("rule") ShopNotVisitRule rule);

    List<Long> shopSubmitRuleCompute(@Param("rule") ShopSubmitRule rule);

    List<Long> shopNotSubmitRuleCompute(@Param("rule") ShopNotSubmitRule rule);

    List<Long> buyCardRuleCompute(@Param("rule") BuyCardRule rule);

    List<Long> renewTimeRuleCompute(@Param("rule") RenewTimeRule rule);

    List<Long> buyCateRuleCompute(@Param("rule") BuyCateRule rule);

    List<Long> bindCateRuleCompute(@Param("rule") BindCateRule rule);

    List<Long> firstBindRuleCompute(@Param("rule") FirstBindRule rule);

    List<Long> expireUserRuleCompute(@Param("rule") ExpireUserRule rule);

    List<Long> expireUserNotRuleCompute(@Param("rule") ExpireUserRule rule);

    List<Long> renewUserRuleCompute(@Param("rule") RenewUserRule rule);

    List<Long> renewUserNotRuleCompute(@Param("rule") RenewUserRule rule);

    List<Long> cardWillExpireRuleCompute(@Param("rule") CardWillExpireRule rule);

    List<Long> recentNotDispatchRuleCompute(@Param("rule") RecentNotDispatchRule rule);

    List<Long> recentAddressRuleCompute(@Param("rule") RecentAddressRule rule);

    List<Long> recentCardPlatformRuleCompute(@Param("rule") RecentCardPlatformRule rule);

    List<Long> mostPlatformRuleCompute(@Param("rule") MostPlatformRule rule);

    List<Long> autoLabelRuleCompute(@Param("rule") AutoLabelRule rule);

    List<Long> autoLabelAndRuleCompute(@Param("rule")AutoLabelRule rule);

    List<Long> wechatActivityUserRuleCompute(@Param("rule") WechatActivityUserRule rule);

    List<Long> wechatActivityUserNotRuleCompute(@Param("rule") WechatActivityUserRule rule);

    List<Long> manualFullReduceRuleCompute(@Param("rule") ManualFullReduceRule rule);

    List<Long> manualPtActivityRuleCompute(@Param("rule") ManualPtActivityRule rule);

    List<Long> joinFreeTrialRuleCompute(@Param("rule")JoinMarketingRule rule);

    List<Long> joinDiscountRuleCompute(@Param("rule")JoinMarketingRule rule);

    List<Long> joinPointMallRuleCompute(@Param("rule")JoinMarketingRule rule);

    List<Long> joinLuckyDrawRuleCompute(@Param("rule")JoinMarketingRule rule);

    List<Long> joinCouponRuleCompute(@Param("rule")JoinMarketingRule rule);

    List<Long> joinFullReduceRuleCompute(@Param("rule")JoinMarketingRule rule);

    List<Long> joinFreeTasteRuleCompute(@Param("rule")JoinMarketingRule rule);

    List<Long> joinFullTrialRuleCompute(@Param("rule")JoinMarketingRule rule);

    List<Long> joinPtRuleCompute(@Param("rule")JoinMarketingRule rule);

    List<Long> fansRuleCompute(@Param("rule")FansRule rule);

    // Long testTimeout();
    List<Long> findAllUserIdByUserId(@Param("userId") Long userId);

    List<UserBuyDateDTO> findMinBuyDate(@Param("ids") List<Long> ids);

    List<CustomerUserMoreVO> findOrderInfo(@Param("ids") List<Long> ids);
    List<CustomerUserMoreVO> findOrderInfo90(@Param("ids") List<Long> ids);

    List<CustomerUserMoreVO> findOrderTime(@Param("ids") List<Long> ids);

    List<CustomerUser> findNullMD5OpenId();

    List<UserTypeCntVO> findUserCnt(Integer userType);

    List<CustomerUser> findByConditionSql(UserMatchDTO dto);

    List<CustomerUserByPhoneDTO> findUserIdGroupByPhone(@Param("phone") String phone);

    List<Long> birthdayUserCompute(@Param("rule") UserBirthdayRule rule);

    List<Long> couponNotUsedRuleCompute(@Param("rule")CouponNotUsedRule rule);

    List<Long> miniProgramRegisterTimeRuleCompute(@Param("rule") MiniProgramRegisterTimeRule rule);

    List<Long> joinMemberTimeRuleCompute(@Param("rule") JoinMemberTimeRule rule);

    List<Long> dispatchSignRuleCompute(@Param("rule") DispatchSignRule rule);

    List<Long> bindNotBuyOfProductIdRuleCompute(@Param("rule") BindNotBuyRule rule);

    List<Long> bindNotBuyOfGroupIdRuleCompute(@Param("rule")BindNotBuyRule rule);

    List<Long> bindNotBuyOfCategoryIdRuleCompute(@Param("rule")BindNotBuyRule rule);

    List<Long> bindNotBuyOfCardCategoryIdRuleCompute(@Param("rule")BindNotBuyRule rule);

    List<Long> recentPayRuleCompute(@Param("rule")RecentPayRule rule);

    List<Long> payAmountRuleCompute(@Param("rule")PayAmountRule rule);

    List<Long> productBuyOfProductIdRuleCompute(@Param("rule")ProductBuyRule rule);

    List<Long> productBuyOfGroupIdRuleCompute(@Param("rule")ProductBuyRule rule);

    List<Long> productBuyOfCategoryIdRuleCompute(@Param("rule")ProductBuyRule rule);

    List<Long> productBuyOfCardCategoryIdRuleCompute(@Param("rule")ProductBuyRule rule);

    List<Long> shopCartOfProductIdRuleCompute(@Param("rule")ShopCartRule rule);

    List<Long> shopCartOfGroupIdRuleCompute(@Param("rule")ShopCartRule rule);

    List<Long> shopCartOfCategoryIdRuleCompute(@Param("rule")ShopCartRule rule);

    List<Long> productNotBuyOfProductIdRuleCompute(@Param("rule")ProductNotBuyRule rule);

    List<Long> productNotBuyOfGroupIdRuleCompute(@Param("rule")ProductNotBuyRule rule);

    List<Long> productNotBuyOfCategoryIdRuleCompute(@Param("rule")ProductNotBuyRule rule);

    List<Long> bindBuyCardRuleCompute(@Param("rule") BindBuyCardRule rule);

    List<Long> bindBuyProductOfProductIdRuleCompute(@Param("rule")BindBuyProductRule rule);

    List<Long> bindBuyProductOfGroupIdRuleCompute(@Param("rule")BindBuyProductRule rule);

    List<Long> bindBuyProductOfCategoryIdRuleCompute(@Param("rule")BindBuyProductRule rule);

    List<Long> bindBuyProductOfCardCategoryIdRuleCompute(BindBuyProductRule rule);

    List<Long> bindCardRuleCompute(@Param("rule") BindCardRule rule);

    List<Long> privilegeNotSetRuleCompute(@Param("rule") PrivilegeNotSetRule rule);

    List<Long> cardTotalRemainingCountRuleCompute(@Param("rule") CardTotalRemainingCountRule rule);

    List<Long> findUpgradeUserIdsByPrivilegeLabelId(@Param("rule")PrivilegeNotSetRule rule);

    List<CustomerUser> findNonMember(@Param("timeStart") Date timeStart);

    List<Long> lastDealRuleCompute(@Param("rule")LastDealRule rule);

    List<Long> shopCartOfCardCategoryIdRuleCompute(@Param("rule")ShopCartRule rule);

    List<Long> joinPrivilegeLabelActivityRuleCompute(@Param("rule")JoinMarketingRule rule);

    List<Long> productNotBuyOfCardCategoryIdRuleCompute(@Param("rule")ProductNotBuyRule rule);

    List<Long> notBindCardRuleCompute(@Param("rule")NotBindUserRule rule);

    List<Long> bindBuyOfProductIdRuleCompute(@Param("rule")BindBuyRule rule);

    List<Long> bindBuyOfGroupIdRuleCompute(@Param("rule")BindBuyRule rule);

    List<Long> bindBuyOfCategoryIdRuleCompute(@Param("rule")BindBuyRule rule);

    List<Long> bindBuyOfCardCategoryIdRuleCompute(@Param("rule")BindBuyRule rule);

    CustomerUser findByNickName(@Param("nickName")String nickName, @Param("id")Long id);

}