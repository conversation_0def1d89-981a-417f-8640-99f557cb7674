package com.hengtiansoft.user.manager;

import com.hengtiansoft.common.rule.AmountRule;
import com.hengtiansoft.common.rule.AverageRule;
import com.hengtiansoft.common.rule.DealRule;
import com.hengtiansoft.common.rule.PointRule;
import com.hengtiansoft.thirdpart.entity.dto.wechat.WxOpenIdResultDTO;
import com.hengtiansoft.thirdpart.entity.dto.wechat.WxUserInfo;
import com.hengtiansoft.user.entity.dto.*;
import com.hengtiansoft.user.entity.po.CustomerUser;
import com.hengtiansoft.user.entity.vo.CustomerUserVO;

import java.util.Date;
import java.util.List;

/**
 * 注册用户manager
 *
 * <AUTHOR>
 * @since 26.03.2020
 */
public interface CustomerUserManager {
    /**
     * 根据条件查询用户管理信息
     *
     * @param dto
     *            分页信息
     * @return 用户管理
     */
    List<CustomerUserVO> findAllByCondition(CustomerUserPageDTO dto);


    /**
     *
     * @param customerUserLoginDTO
     */
    int loginRegister(CustomerUserLoginDTO customerUserLoginDTO, Integer userType, WxOpenIdResultDTO wxOpenIdResultDTO);

    /**
     * 新增
     *
     * @param customerUserDTO
     *            新增dto
     * @return 主键
     */
    int insertOne(CustomerUserDTO customerUserDTO);

    /**
     * 修改
     *
     * @param customerUserDTO
     */
    void updateOne(CustomerUserDTO customerUserDTO);

    void updateOne(CustomerUser customerUser);

    /**
     * 查询用户信息
     *
     * @param id
     *            userid
     * @return 用户信息vo
     */
    CustomerUserVO findByCustomerUserId(Long id);

    CustomerUser findById(Long id);

    /**
     * 批量查用户
     *
     * @param ids
     *            用户ids
     * @return 用户集合
     */
    List<CustomerUserVO> findByIds(List<Long> ids);

    List<CustomerUser> findByIdList(List<Long> ids);

    /**
     * 模糊查询查询用户列表
     *
     * @param dto
     *            查询dto
     * @return 用户列表
     */
    List<CustomerUser> findByPageDTO(UserQueryDTO dto, String orderByColumn);

    /**
     * 更新为未启用
     * 
     * @param empNos
     * @param beforeStatus 更新前状态
     * @param afterStatus 更新后状态
     */
    void updateStatus(List<String> empNos,Integer beforeStatus,Integer afterStatus);

    /**
     * 查询未同步的客户
     * 
     * @return
     */
    List<CustomerUserVO> findUnSyn();

    /**
     * 更新为已同步
     * 
     * @param ids
     */
    void updateSynFlag(List<Long> ids);

    /**
     * 时间内增长的用户数
     *
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @return 总数
     */
    Integer findCountByDateRange(Date startDate, Date endDate);

    /**
     * 所有的用户数
     *
     * @return 用户数
     */
    Integer allUserCount();

    CustomerUser findTmallByserId(Long userId);

    /**
     * 校验用户是否在黑名单中
     *
     * @param userId 用户id
     */
    void verifyBlacklist(Long userId);

    CustomerUser findLastLogoffUser(String phone);

    List<CustomerUser> findByPhones(List<String> phones);

    List<Long> dealRuleCompute(DealRule rule);

    List<Long> amountRuleCompute(AmountRule rule);

    List<Long> pointRuleCompute(PointRule rule);

    List<Long> averageRuleCompute(AverageRule rule);

    List<CustomerUser> findbyIsBuy(Long userId);

    /**
     * 更新用户最后访问时间
     * @param userId
     */
    void updateVisitTime(Long userId);


    String getOrderNickName(String phone, String nickName, String userPic);

    List<Long> findAllUserIdByUserId(Long id);

    List<UserBuyDateDTO> findMinBuyDate(List<Long> ids);

    /**
     * 根据openid获取用户信息并更新库
     * @param openIds
     */
    List<WxUserInfo> insertOrUpdateWxUser(List<String> openIds);

    /**
     * 根据unionId同步公众号openId
     * @param wxUserInfoList
     */
    void syncWxOpenId(List<WxUserInfo> wxUserInfoList);

    /**
     * 从数据库同步公众号openid
     */
    void syncDbWxOpenId();

    /**
     * 根据小程序openId获取微信公众号openId
     * @param openid
     */
    String getWxOpenIdByMiniOpenId(String openid);

    int updateBatch(List<CustomerUser> user4Updates);

    void joinNascentMember(CustomerUser customerUser);
}
