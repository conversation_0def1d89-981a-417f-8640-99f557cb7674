package com.hengtiansoft.user.manager.impl;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.hengtiansoft.common.entity.dto.Pagination;
import com.hengtiansoft.common.entity.exception.BusinessException;
import com.hengtiansoft.common.enumeration.CustomerUserStatusEnum;
import com.hengtiansoft.common.enumeration.CustomerUserTypeEnum;
import com.hengtiansoft.common.enumeration.NascentFlagEnum;
import com.hengtiansoft.common.redis.RedisOperation;
import com.hengtiansoft.common.rule.AmountRule;
import com.hengtiansoft.common.rule.AverageRule;
import com.hengtiansoft.common.rule.DealRule;
import com.hengtiansoft.common.rule.PointRule;
import com.hengtiansoft.common.util.BeanUtils;
import com.hengtiansoft.common.util.DateUtil;
import com.hengtiansoft.common.util.PageUtils;
import com.hengtiansoft.common.util.StreamUtils;
import com.hengtiansoft.thirdpart.entity.dto.nascent.CustomerSaveDTO;
import com.hengtiansoft.thirdpart.entity.dto.wechat.WxOpenIdResultDTO;
import com.hengtiansoft.thirdpart.entity.dto.wechat.WxUserInfo;
import com.hengtiansoft.thirdpart.interfaces.NascentCustomerManager;
import com.hengtiansoft.thirdpart.interfaces.WeChatUserInfoManager;
import com.hengtiansoft.user.dao.*;
import com.hengtiansoft.user.entity.dto.*;
import com.hengtiansoft.user.entity.po.CustomerMiniUser;
import com.hengtiansoft.user.entity.po.CustomerUser;
import com.hengtiansoft.user.entity.po.CustomerWeixinUser;
import com.hengtiansoft.user.entity.po.UserVisitDetail;
import com.hengtiansoft.user.entity.vo.CustomerUserVO;
import com.hengtiansoft.user.enums.UserLogOffEnum;
import com.hengtiansoft.user.manager.CustomerUserManager;
import com.hengtiansoft.user.util.CustomerUserUtil;
import com.hengtiansoft.user.util.WxUserUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 用户管理manager
 *
 * <AUTHOR>
 * @since 26.03.2020
 */
@Repository
@Slf4j
public class CustomerUserManagerImpl implements CustomerUserManager {
    @Resource
    private CustomerUserDao customerUserDao;

    @Resource
    private CustomerWxUserDao customerWxUserDao;

    @Resource
    private CustomerMiniUserDao customerMiniUserDao;

    @Resource
    private CustomerCheckPhoneDao customerCheckPhoneDao;

    @Resource
    private RedisOperation redisOperation;

    @Resource
    private UserVisitDetailDao userVisitDetailDao;

    @Resource
    private WeChatUserInfoManager weChatUserInfoManager;

    @Resource
    private CustomerWeixinUserDao customerWeixinUserDao;

    @Resource
    private NascentCustomerManager nascentCustomerManager;

    @Override
    public List<CustomerUserVO> findAllByCondition(CustomerUserPageDTO dto) {

        List<CustomerUser> list = customerUserDao.findAllByCondition(dto);

        return BeanUtils.copyList(list, CustomerUserVO::new);
    }

    @Override
    public int insertOne(CustomerUserDTO customerUserDTO) {
        CustomerUser customerUser = CustomerUserUtil.buildInsert(customerUserDTO);
        int i = customerUserDao.insertOne(customerUser);
        customerUserDTO.setId(customerUser.getId());
        return i;
    }

    @Override
    public void updateOne(CustomerUserDTO customerUserDTO) {
        customerUserDao.updateOne(BeanUtils.copy(customerUserDTO, CustomerUser::new));
    }

    @Override
    public void updateOne(CustomerUser customerUser) {
        customerUserDao.updateOne(customerUser);
    }

    @Override
    public CustomerUserVO findByCustomerUserId(Long id) {

        return BeanUtils.copy(customerUserDao.findById(id), CustomerUserVO::new);
    }
    @Override
    public CustomerUser findById(Long id){
        return customerUserDao.findById(id);
    }

    @Override
    public List<CustomerUserVO> findByIds(List<Long> ids) {
        if(CollectionUtils.isEmpty(ids)){
            return Lists.newArrayList();
        }
        List<CustomerUser> list = customerUserDao.findByIds(ids);
        return BeanUtils.copyList(list, CustomerUserVO::new);
    }

    @Override
    public List<CustomerUser> findByIdList(List<Long> ids) {
        if(CollectionUtils.isEmpty(ids)){
            return Lists.newArrayList();
        }
        return customerUserDao.findByIds(ids);
    }

    @Override
    public List<CustomerUser> findByPageDTO(UserQueryDTO dto, String orderByColumn) {
        if(StringUtils.isBlank(orderByColumn)){
            orderByColumn = "createTime";
        }
        return customerUserDao.findByPageDTO(dto, orderByColumn);
    }

    @Override
    public void updateStatus(List<String> empNos, Integer beforeStatus, Integer afterStatus) {
        if (beforeStatus.equals(afterStatus)) {
            throw new BusinessException("更新前后状态不能一致");
        }
        customerUserDao.updateStatus(empNos, beforeStatus, afterStatus);

    }

    @Override
    public List<CustomerUserVO> findUnSyn() {
        List<CustomerUser> list = customerUserDao.findUnSyn();
        return BeanUtils.copyList(list, CustomerUserVO::new);
    }

    @Override
    public void updateSynFlag(List<Long> ids) {
        customerUserDao.updateSynFlag(ids);
    }

    @Override
    public Integer findCountByDateRange(Date startDate, Date endDate) {
        return customerUserDao.findCountByDateRange(startDate, endDate);
    }

    @Override
    public Integer allUserCount() {
        return customerUserDao.findAllCount();
    }


    @Override
    public int loginRegister(CustomerUserLoginDTO customerUserLoginDTO, Integer userType, WxOpenIdResultDTO wxOpenIdResultDTO) {
        if (StringUtils.isBlank(customerUserLoginDTO.getPhone())) {
            throw new BusinessException("手机号为空");
        }
        CustomerUserPageDTO dto = new CustomerUserPageDTO();
        dto.setPhone(customerUserLoginDTO.getPhone());
        dto.setLogOff(UserLogOffEnum.UNREGISTERED.getCode());
        List<CustomerUser> condition = customerUserDao.findAllByCondition(dto);
        Long userId;
        int insert = 0;
        if (CustomerUserTypeEnum.youzan_user.getCode().equals(userType)) {
            List<CustomerMiniUser> customerMiniUsers = new ArrayList<>();
            if (StringUtils.isNotBlank(customerUserLoginDTO.getOpenId())){
                 customerMiniUsers = customerMiniUserDao.findByOpenId(customerUserLoginDTO.getOpenId());
            }
            if (CollectionUtils.isEmpty(customerMiniUsers) && StringUtils.isNotBlank(customerUserLoginDTO.getUnionId())){
                customerMiniUsers = customerMiniUserDao.findByUnionId(customerUserLoginDTO.getUnionId());
            }
            log.info("有赞用户注册:[{}]", JSONObject.toJSONString(customerUserLoginDTO));
            if (CollectionUtils.isEmpty(condition)){
                CustomerUser customerUser = new CustomerUser();
                customerUser.setStatus(CustomerUserStatusEnum.ENABLE.getCode());
                // customerUser.setFirstLoginTime(new Date());
                // customerUser.setLatestLoginTime(new Date());
                customerUser.setPhone(customerUserLoginDTO.getPhone());
                customerUser.setDeviceId(customerUserLoginDTO.getToken());
                customerUser.setUserType(userType);
                customerUser.setDelflag(0);
                insert = customerUserDao.insertOne(customerUser);
                userId = customerUser.getId();
                if (CollectionUtils.isEmpty(customerMiniUsers)){
                    CustomerMiniUser customerMiniUser = new CustomerMiniUser();
                    customerMiniUser.setOpenId(customerUserLoginDTO.getOpenId());
                    customerMiniUser.setCity(wxOpenIdResultDTO.getCity());
                    customerMiniUser.setCountry(wxOpenIdResultDTO.getCountry());
                    customerMiniUser.setProvince(wxOpenIdResultDTO.getProvince());
                    customerMiniUser.setSex(Objects.nonNull(wxOpenIdResultDTO.getSex()) ? Integer.valueOf(wxOpenIdResultDTO.getSex()) : null);
                    customerMiniUser.setNickname(StringUtils.isBlank(wxOpenIdResultDTO.getNickname()) ? "" : wxOpenIdResultDTO.getNickname());
                    customerMiniUser.setUserId(userId);
                    customerMiniUser.setUnionId(wxOpenIdResultDTO.getUnionId());
                    customerMiniUser.setDelflag(0);
                    customerMiniUser.setPicUrl(StringUtils.isBlank(wxOpenIdResultDTO.getHeadimgurl()) ? "" : wxOpenIdResultDTO.getHeadimgurl());
                    customerMiniUserDao.saveOne(customerMiniUser);
                }
            }else {
                CustomerUser customerUser = condition.get(0);
                // customerUser.setLatestLoginTime(new Date());
                customerUser.setDeviceId(customerUserLoginDTO.getToken());
                customerUserDao.updateOne(customerUser);
            }
            return insert;
        } else {
            if (CollectionUtils.isEmpty(condition)) {

                CustomerUser customer = new CustomerUser();
                customer.setStatus(CustomerUserStatusEnum.ENABLE.getCode());
                // customer.setFirstLoginTime(new Date());
                // customer.setLatestLoginTime(new Date());
                customer.setPhone(customerUserLoginDTO.getPhone());
                customer.setDeviceId(customerUserLoginDTO.getToken());
                customer.setUserType(userType);
                customer.setDelflag(0);
                return customerUserDao.insertOne(customer);
            } else {
                int i = 1;
                for (CustomerUser customerUser : condition) {
                    customerUser.setUserType(userType);
                    customerUser.setDeviceId(customerUserLoginDTO.getToken());
                    // customerUser.setLatestLoginTime(new Date());
                    customerUserDao.updateOne(customerUser);
                }
                return i;
            }
        }
    }

    @Override
    public CustomerUser findTmallByserId(Long userId) {
        if (Objects.isNull(userId)){
            return new CustomerUser();
        }
        return customerUserDao.findTmallUserById(userId);
    }

    @Override
    public void verifyBlacklist(Long userId) {
        CustomerUser customerUser = customerUserDao.findById(userId);
        if(CustomerUserStatusEnum.DISABLE.getCode().equals(customerUser.getStatus())){
            throw new BusinessException("账户异常");
        }
    }

    @Override
    public CustomerUser findLastLogoffUser(String phone) {
        List<CustomerUser> customerUsers = customerUserDao.findLastLogoffUser(phone);
        return StreamUtils.getFirst(customerUsers);
    }

    @Override
    public List<CustomerUser> findByPhones(List<String> phones) {
        List<CustomerUser> customerUsers = customerUserDao.findByPhones(phones);
        return customerUsers;
    }

    @Override
    public List<Long> dealRuleCompute(DealRule rule) {
        return customerUserDao.dealRuleCompute(rule);
    }

    @Override
    public List<Long> amountRuleCompute(AmountRule rule) {
        return customerUserDao.amountRuleCompute(rule);
    }

    @Override
    public List<Long> pointRuleCompute(PointRule rule) {
        return customerUserDao.pointRuleCompute(rule);
    }

    @Override
    public List<Long> averageRuleCompute(AverageRule rule) {
        return customerUserDao.averageRuleCompute(rule);
    }

    @Override
    public List<CustomerUser> findbyIsBuy(Long userId) {
        return customerUserDao.findbyIsBuy(userId);
    }

    @Override
    public void updateVisitTime(Long userId) {
        String key = "userCache:visitTime:UserId:" + userId;
        if(!redisOperation.exist(key)){
            //更新访问时间
            Date t = new Date();
            Date endDate = DateUtil.setDefaultEndDate(t);
            UserVisitDetail userVisitDetail = new UserVisitDetail();
            userVisitDetail.setUserId(userId);
            userVisitDetail.setVisitTime(new Date());
            userVisitDetailDao.insert(userVisitDetail);
            if(endDate.before(t)){
                return;
            }
            redisOperation.setex(key, "1", endDate.getTime() - t.getTime(), TimeUnit.MILLISECONDS);
        }
    }


    /**
     * 查询【牛主人xxxx】后面模糊查询，看看有几个，要去除注销
     * 0个，直接用，1个加-1,2个加-2，以此类推
     * @return
     */
    @Override
    public String getOrderNickName(String phone, String nickName, String userPic) {
        if(Objects.equals(CustomerUserUtil.NICK_NAME, nickName)){
            String orderNickName = CustomerUserUtil.NICK_NAME + StringUtils.right(phone, 4);
            List<CustomerUser> customerUsers = customerUserDao.findByOrderNickNameLike(orderNickName, phone);

            if(CollectionUtils.isEmpty(customerUsers)){
                return orderNickName;
            }
            return orderNickName + "-" + customerUsers.size();
        }else{
            return nickName;
        }
    }

    @Override
    public List<Long> findAllUserIdByUserId(Long id) {
        return customerUserDao.findAllUserIdByUserId(id);
    }
    @Override
    public List<UserBuyDateDTO> findMinBuyDate(List<Long> ids) {
        return customerUserDao.findMinBuyDate(ids);
    }

    @Override
    public List<WxUserInfo> insertOrUpdateWxUser(List<String> openIds) {
        List<WxUserInfo> wxUserInfoList = weChatUserInfoManager.batchGetUserInfo(openIds);
        List<CustomerWeixinUser> wxUserList = customerWeixinUserDao.findByOpenIds(openIds);
        Map<String, CustomerWeixinUser> wxUserMap = StreamUtils.toMap(wxUserList, x -> x.getOpenId());

        List<CustomerWeixinUser> updateList = new ArrayList<>();
        List<CustomerWeixinUser> insertList = new ArrayList<>();

        for (WxUserInfo wxUserInfo: wxUserInfoList) {
            CustomerWeixinUser user = wxUserMap.get(wxUserInfo.getOpenId());
            if(null != user){
                CustomerWeixinUser wxUser = WxUserUtil.convert2PO(wxUserInfo);
                wxUser.setCreateTime(user.getCreateTime());
                updateList.add(wxUser);
            }else{
                insertList.add(WxUserUtil.convert2PO(wxUserInfo));
            }
        }

        if(CollectionUtils.isNotEmpty(updateList)){
            customerWeixinUserDao.batchUpdateByOpenId(updateList);
        }

        if(CollectionUtils.isNotEmpty(insertList)){
            customerWeixinUserDao.batchInsert(insertList);
        }
        return wxUserInfoList;
    }

    @Override
    public void syncWxOpenId(List<WxUserInfo> wxUserInfoList) {
        for (WxUserInfo wxUserInfo: wxUserInfoList) {
            if(StringUtils.isBlank(wxUserInfo.getUnionId()) || StringUtils.isBlank(wxUserInfo.getOpenId())){
                continue;
            }
            CustomerUser user = new CustomerUser();
            user.setWechartOpenId(wxUserInfo.getOpenId());
            customerUserDao.updateByUnionId(wxUserInfo.getUnionId(), user);
        }
    }

    @Override
    public void syncDbWxOpenId(){
        int pageNum = 1;
        int pageSize = 500;
        Integer pages;
        do {
            //分页查unionid有值的用户
            PageHelper.startPage(pageNum, pageSize);
            List<CustomerUser> customerUserList = customerUserDao.findAllWithUnionId();
            Pagination pagination = PageUtils.extract(customerUserList);
            pages = pagination.getPages();
            pageNum ++;

            List<String> unionIds = StreamUtils.toList(customerUserList, CustomerUser::getUnionId);
            //根据unionid从公众号用户表里查openid关联
            List<CustomerWeixinUser> customerWeixinUserList = customerWeixinUserDao.findByUnionIds(unionIds);
            Map<String, String> wxUnionWithOpenIdMap = StreamUtils.toMap(customerWeixinUserList, CustomerWeixinUser::getUnionId, CustomerWeixinUser::getOpenId);
            List<CustomerUser> user4Updates = new ArrayList<>();
            for (CustomerUser customerUser : customerUserList) {
                String wxOpenId = wxUnionWithOpenIdMap.get(customerUser.getUnionId());
                if (StringUtils.isBlank(wxOpenId)) {
                    continue;
                }
                CustomerUser user4Update = new CustomerUser();
                user4Update.setId(customerUser.getId());
                user4Update.setWechartOpenId(wxOpenId);
                user4Updates.add(user4Update);
            }
            if (CollectionUtils.isNotEmpty(user4Updates)) {
                customerUserDao.updateBatch(user4Updates);
            }
        }while (pageNum <= pages);
    }

    @Override
    public String getWxOpenIdByMiniOpenId(String openId) {
        if(StringUtils.isBlank(openId)){
            return Strings.EMPTY;
        }
        List<CustomerUser> customerUserList = customerUserDao.findByOpenId(openId);
        if(CollectionUtils.isEmpty(customerUserList)){
            return Strings.EMPTY;
        }
        return StreamUtils.getFirst(customerUserList).getWechartOpenId();
    }
    @Override
    public int updateBatch(List<CustomerUser> user4Updates) {
        return customerUserDao.updateBatch(user4Updates);
    }

    @Override
    public void joinNascentMember(CustomerUser customerUser) {
        CustomerSaveDTO saveDTO = new CustomerSaveDTO();
        saveDTO.setMobile(customerUser.getPhone());
        String customerName = StringUtils.isBlank(customerUser.getNickName()) ? (StringUtils.isBlank(customerUser.getUserName()) ? customerUser.getPhone() : customerUser.getUserName()):customerUser.getNickName();
        saveDTO.setCustomerName(customerName);
        try {
            if (Objects.nonNull(nascentCustomerManager.registerCustomer(saveDTO))) {
                CustomerUserDTO updateDTO = new CustomerUserDTO();
                updateDTO.setId(customerUser.getId());
                updateDTO.setNascentFlag(NascentFlagEnum.MEMBER.getCode());
                updateDTO.setJoinTime(new Date());
                this.updateOne(updateDTO);
            }
        }catch (Exception e){
            log.error("南讯入会失败. userId:{}", customerUser.getId(), e);
        }
    }
}
