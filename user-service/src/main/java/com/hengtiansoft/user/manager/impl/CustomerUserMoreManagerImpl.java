package com.hengtiansoft.user.manager.impl;

import com.google.common.collect.Lists;
import com.hengtiansoft.common.enumeration.DeleteFlagEnum;
import com.hengtiansoft.common.enumeration.FlagEnum;
import com.hengtiansoft.user.dao.CustomerUserDao;
import com.hengtiansoft.user.dao.CustomerUserMoreDao;
import com.hengtiansoft.user.entity.dto.CustomerUserMoreMatchDTO;
import com.hengtiansoft.user.entity.mapper.CustomerUserMoreMapper;
import com.hengtiansoft.user.entity.po.CustomerUser;
import com.hengtiansoft.user.entity.po.CustomerUserMore;
import com.hengtiansoft.user.entity.vo.CustomerUserMoreVO;
import com.hengtiansoft.user.manager.CustomerUserMoreManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class CustomerUserMoreManagerImpl implements CustomerUserMoreManager {
    @Resource
    private CustomerUserMoreDao customerUserMoreDao;
    @Resource
    private CustomerUserMoreMapper customerUserMoremapper;
    @Resource
    private CustomerUserDao customerUserDao;

    @Override
    public void updateByUserId(CustomerUserMore more) {
        Example example = new Example(CustomerUserMore.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("userId", more.getUserId());
        criteria.andEqualTo("delflag", DeleteFlagEnum.IS_NOT_DELETE.getCode());
        customerUserMoremapper.updateByExampleSelective(more, example);
    }

    @Override
    public List<CustomerUserMore> findByUserIds(List<Long> userIds) {
        if(CollectionUtils.isEmpty(userIds)){
            return Lists.newArrayList();
        }
        Example example = new Example(CustomerUserMore.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andIn("userId", userIds);
        criteria.andEqualTo("delflag", DeleteFlagEnum.IS_NOT_DELETE.getCode());
        return customerUserMoremapper.selectByExample(example);
    }

    @Override
    public CustomerUserMore findByUserId(Long userId) {
        if(userId == null){
            return null;
        }
        return customerUserMoremapper.findByUserId(userId);
    }

    @Override
    public void insertList(List<CustomerUserMore> inserList) {
        if(CollectionUtils.isEmpty(inserList)){
            return;
        }
        customerUserMoremapper.insertList(inserList);
    }

    @Override
    public void deleteByUserIds(List<Long> userIds) {
        if(CollectionUtils.isEmpty(userIds)){
            return;
        }
        Example example = new Example(CustomerUserMore.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andIn("userId", userIds);
        CustomerUserMore more = new CustomerUserMore();
        more.setDelflag(DeleteFlagEnum.IS_DELETE.getCode());
        customerUserMoremapper.updateByExampleSelective(more, example);
    }

/*    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateUserMore(List<CustomerUser> users) {
        // 订单统计、最近访问时间同步
        List<Long> userIds = StreamUtils.toList(users, CustomerUser::getId);
        List<CustomerUserMore> userMoreList = this.findByUserIds(userIds);
        List<Long> existsUserIds = StreamUtils.toList(userMoreList, CustomerUserMore::getUserId);
        Map<Long, CustomerUserMore> userMoreMap = StreamUtils.toMap(userMoreList, CustomerUserMore::getUserId);
        Map<Long, CustomerUser> userMap = StreamUtils.toMap(users, CustomerUser::getId);
        List<Long> insertIds = CollUtil.subtractToList(userIds, existsUserIds);
        List<Long> updateIds = CollUtil.subtractToList(userIds, insertIds);
        // 查询用户订单信息
        List<CustomerUserMoreVO> orderInfoVOS = customerUserDao.findOrderInfo(userIds);
        Map<Long, CustomerUserMoreVO> orderInfoMap = StreamUtils.toMap(orderInfoVOS, CustomerUserMoreVO::getUserId);
        List<CustomerUserMoreVO> orderTimvVOS = customerUserDao.findOrderTime(userIds);
        Map<Long, CustomerUserMoreVO> orderTimeMap = StreamUtils.toMap(orderTimvVOS, CustomerUserMoreVO::getUserId);
        List<CustomerUserMoreVO> orderInfo90VOS = customerUserDao.findOrderInfo90(userIds);
        Map<Long, CustomerUserMoreVO> orderInfo90Map = StreamUtils.toMap(orderInfo90VOS, CustomerUserMoreVO::getUserId);
        List<CustomerUserMore> inserList = Lists.newArrayList();
        for (Long insertId : insertIds) {
            CustomerUserMore customerUserMore = getCustomerUserMore(insertId, userMap, orderInfoMap, orderTimeMap, orderInfo90Map);
            inserList.add(customerUserMore);
        }
        for (Long updateId : updateIds) {
            CustomerUserMore userMore = userMoreMap.get(updateId);
            CustomerUserMore customerUserMore = getCustomerUserMore(updateId, userMap, orderInfoMap, orderTimeMap, orderInfo90Map);
            customerUserMore.setLastVisitTime(userMore.getLastVisitTime());
            customerUserMore.setReviewCnt(userMore.getReviewCnt());
            customerUserMore.setHelpCnt(userMore.getHelpCnt());
            customerUserMore.setLikeCnt(userMore.getLikeCnt());
            inserList.add(customerUserMore);
        }
        this.deleteByUserIds(updateIds);
        if(CollectionUtils.isNotEmpty(inserList)){
            this.insertList(inserList);
        }
    }*/

    @Override
    public void updateOrderTime(Long userId) {
        CustomerUser customerUser = customerUserDao.findById(userId);
        List<CustomerUserMore> userMoreList = this.findByUserIds(Lists.newArrayList(userId));
        List<CustomerUserMoreVO> orderTimeList = customerUserDao.findOrderTime(Lists.newArrayList(userId));
        if(CollectionUtils.isNotEmpty(userMoreList)){
            CustomerUserMore userMore = userMoreList.get(0);
            if(CollectionUtils.isNotEmpty(orderTimeList)){
                CustomerUserMoreVO orderTimeVO = orderTimeList.get(0);
                userMore.setLastOrderTime(orderTimeVO.getLastOrderTime());
                this.update(userMore);
            }
        }else{
            CustomerUserMore userMore = new CustomerUserMore();
            userMore.setUserId(userId);
            if(CollectionUtils.isNotEmpty(orderTimeList)){
                CustomerUserMoreVO orderTimeVO = orderTimeList.get(0);
                userMore.setLastOrderTime(orderTimeVO.getLastOrderTime());
            }
            userMore.setLastVisitTime(customerUser.getLatestVisitTime());
            userMore.setOperator("system");
            Date now = new Date();
            userMore.setCreateTime(now);
            userMore.setUpdateTime(now);
            userMore.setDelflag(DeleteFlagEnum.IS_NOT_DELETE.getCode());
            this.insert(userMore);
        }
    }

    @Override
    public void updateConsumeAmount(Long userId, BigDecimal consumeAmount) {
        CustomerUser customerUser = customerUserDao.findById(userId);
        List<CustomerUserMore> userMoreList = this.findByUserIds(Lists.newArrayList(userId));
        if(CollectionUtils.isNotEmpty(userMoreList)){
            CustomerUserMore userMore = userMoreList.get(0);
            if(Objects.nonNull(userMore.getConsumeAmount()) && userMore.getConsumeAmount().compareTo(consumeAmount) == 0){
                return;
            }
            CustomerUserMore update4Po = new CustomerUserMore();
            update4Po.setId(userMore.getId());
            update4Po.setConsumeAmount(consumeAmount);
            customerUserMoremapper.updateByPrimaryKeySelective(update4Po);
        }else{
            CustomerUserMore userMore = new CustomerUserMore();
            userMore.setConsumeAmount(consumeAmount);
            userMore.setUserId(userId);
            userMore.setLastVisitTime(customerUser.getLatestVisitTime());
            userMore.setOperator("system");
            Date now = new Date();
            userMore.setCreateTime(now);
            userMore.setUpdateTime(now);
            userMore.setDelflag(DeleteFlagEnum.IS_NOT_DELETE.getCode());
            this.insert(userMore);
        }
    }

    @Override
    public void update(CustomerUserMore userMore) {
        customerUserMoremapper.updateByPrimaryKey(userMore);
    }

    @Override
    public void insert(CustomerUserMore userMore) {
        customerUserMoremapper.insertSelective(userMore);
    }

    @Override
    public int realDeleteByTime(Date time) {
        return customerUserMoremapper.realDeleteByTime(time);
    }

    @Override
    public void updateBatch(List<CustomerUserMore> user4Updates) {
        customerUserMoremapper.updateBatch(user4Updates);
    }

    @Override
    public void updateBatchById(List<CustomerUserMore> user4Updates) {
        customerUserMoremapper.updateBatchById(user4Updates);
    }

    @NotNull
    private static CustomerUserMore getCustomerUserMore(Long insertId, Map<Long, CustomerUser> userMap, Map<Long, CustomerUserMoreVO> orderInfoMap, Map<Long, CustomerUserMoreVO> orderTimeMap, Map<Long, CustomerUserMoreVO> orderInfo90Map) {
        CustomerUser customerUser = userMap.get(insertId);
        CustomerUserMoreVO orderInfoVO = orderInfoMap.get(insertId);
        CustomerUserMoreVO orderTimeVO = orderTimeMap.get(insertId);
        CustomerUserMoreVO orderInfo90VO = orderInfo90Map.get(insertId);
        CustomerUserMore customerUserMore = new CustomerUserMore();
        customerUserMore.setUserId(insertId);
        if(orderInfoVO != null){
            customerUserMore.setTotalAmount(orderInfoVO.getTotalAmount());
            customerUserMore.setOrderCnt(orderInfoVO.getOrderCnt());
            customerUserMore.setAverageUser(orderInfoVO.getAverageUser() == null ? null :orderInfoVO.getAverageUser().setScale(2, RoundingMode.HALF_UP));
            customerUserMore.setLastPayTime(orderInfoVO.getLastPayTime());
        }
        if(orderTimeVO != null){
            customerUserMore.setLastOrderTime(orderTimeVO.getLastOrderTime());
        }
        if(orderInfo90VO != null){
            customerUserMore.setOrderGmv90(orderInfo90VO.getTotalAmount());
            customerUserMore.setOrderSingle90(FlagEnum.YES.getCode());
        }else{
            customerUserMore.setOrderSingle90(FlagEnum.NO.getCode());
        }
        customerUserMore.setLastVisitTime(customerUser.getLatestVisitTime());
        customerUserMore.setOperator("system");
        Date now = new Date();
        customerUserMore.setCreateTime(now);
        customerUserMore.setUpdateTime(now);
        customerUserMore.setDelflag(DeleteFlagEnum.IS_NOT_DELETE.getCode());
        return customerUserMore;
    }

    @Override
    public List<CustomerUserMoreMatchDTO> findByList(List<String> phones, List<String> openIds, List<String> md5OpenId) {
        return customerUserMoremapper.findByList(phones, openIds, md5OpenId);
    }
}
