package com.hengtiansoft.user.util;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.hengtiansoft.common.entity.vo.CustomerUserVO;
import com.hengtiansoft.common.enumeration.*;
import com.hengtiansoft.common.util.SafeUtil;
import com.hengtiansoft.thirdpart.entity.dto.wechat.WxOpenIdResultDTO;
import com.hengtiansoft.thirdpart.entity.dto.youshu.YoushuUserInfoReq;
import com.hengtiansoft.thirdpart.util.AliyunOSSUtils;
import com.hengtiansoft.user.entity.dto.CustomerUserDTO;
import com.hengtiansoft.user.entity.dto.CustomerUserMoreMatchDTO;
import com.hengtiansoft.user.entity.dto.UserBuyDateDTO;
import com.hengtiansoft.user.entity.dto.UserExcelDTO;
import com.hengtiansoft.user.entity.po.CustomerUser;
import com.hengtiansoft.user.entity.po.CustomerUserMore;
import com.hengtiansoft.user.entity.vo.*;
import com.hengtiansoft.user.enums.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
public class CustomerUserUtil {
    public static final String NICK_NAME = "牛主人";
    public static final String USER_PIC = "/static/avatar.png";
    // public static final String USER_PIC = "https://milkcard-dev.oss-cn-hangzhou.aliyuncs.com/static/avatar.png";
    public static final String WX_NICK_NAME = "微信用户";

    private static final Map<Integer, String> productTypeMap = new HashMap<>();
    static {
        productTypeMap.put(1, "电子奶卡");
        productTypeMap.put(2, "实体奶卡");
        productTypeMap.put(3, "周期购");
        productTypeMap.put(4, "单品");
    }

    private static final Map<Integer, String> couponUsedMap = new HashMap<>();
    static {
        couponUsedMap.put(0, "全部未使用");
        couponUsedMap.put(1, "部分未使用");
        couponUsedMap.put(2, "全部已使用");
    }

    public static CustomerUser buildUpdate(UserExcelDTO userExcelDTO, CustomerUser old, String operater){
        CustomerUser customerUser = new CustomerUser();
        customerUser.setId(old.getId());
        customerUser.setPhone(userExcelDTO.getPhone());
        customerUser.setStatus(CustomerUserStatusEnum.getEnumByDesc(userExcelDTO.getStatus()).getCode());
        if(CustomerUserStatusEnum.DISABLE.getDesc().equals(userExcelDTO.getStatus())){
            customerUser.setRemark(userExcelDTO.getRemark());
        }
        customerUser.setOperater(operater);
        return customerUser;
    }

    public static CustomerUser buildInsert(UserExcelDTO userExcelDTO, String operater) {
        CustomerUser customerUser = new CustomerUser();
        customerUser.setPhone(userExcelDTO.getPhone());
        customerUser.setStatus(CustomerUserStatusEnum.getEnumByDesc(userExcelDTO.getStatus()).getCode());
        if(CustomerUserStatusEnum.DISABLE.getDesc().equals(userExcelDTO.getStatus())){
            customerUser.setRemark(userExcelDTO.getRemark());
        }
        customerUser.setOperater(operater);
        return customerUser;

    }

    public static CustomerUser buildInsert(CustomerUserDTO customerUserDTO){
        CustomerUser customerUser = new CustomerUser();
        customerUser.setPhone(customerUserDTO.getPhone());
        customerUser.setUserName(customerUserDTO.getUserName());
        customerUser.setUserPic(customerUserDTO.getUserPic());
        customerUser.setStatus(customerUserDTO.getStatus());
        customerUser.setUserAccount(customerUserDTO.getUserAccount());
        customerUser.setPassword(customerUserDTO.getPassword());
        customerUser.setOpenId(customerUserDTO.getOpenId());
        customerUser.setUnionId(customerUserDTO.getUnionId());
        customerUser.setUserType(customerUserDTO.getUserType());
        customerUser.setFirstLoginTime(customerUserDTO.getFirstLoginTime());
        customerUser.setLatestLoginTime(customerUserDTO.getLatestLoginTime());
        customerUser.setPrivilegeFlag(customerUserDTO.getPrivilegeFlag());
        customerUser.setNascentFlag(customerUserDTO.getNascentFlag());
        customerUser.setJoinTime(customerUserDTO.getJoinTime());
        customerUser.setNickName(customerUserDTO.getNickName());
        customerUser.setLogOff(customerUserDTO.getLogOff());
        customerUser.setRemark(customerUserDTO.getRemark());
        customerUser.setId(customerUserDTO.getId());
        customerUser.setOrderNickName(customerUserDTO.getOrderNickName());
        customerUser.setActivityId(customerUserDTO.getActivityId());
        customerUser.setMd5OpenId(customerUserDTO.getMd5OpenId());
        return customerUser;
    }

    public static UserInfoVO convert2VO(CustomerUser customerUser){
        UserInfoVO userInfoVO = new UserInfoVO();
        userInfoVO.setId(customerUser.getId());
        userInfoVO.setPhone(customerUser.getPhone());
        userInfoVO.setUserName(customerUser.getUserName());
        userInfoVO.setPrivilegeFlag(customerUser.getPrivilegeFlag());
        userInfoVO.setStatus(customerUser.getStatus());
        userInfoVO.setLatestLoginTime(customerUser.getLatestLoginTime());
        userInfoVO.setJoinTime(customerUser.getJoinTime());
        userInfoVO.setOrderNickName(customerUser.getOrderNickName());
        userInfoVO.setAvatarUrl(customerUser.getUserPic());
        log.info("转换用户信息 . 数据库信息 --> customerUser:{}", JSON.toJSONString(customerUser));
        if(StringUtils.isBlank(customerUser.getNickName()) || Objects.equals(WX_NICK_NAME, customerUser.getNickName())){
            userInfoVO.setNickName(NICK_NAME);
        }
        if(StringUtils.isBlank(customerUser.getUserPic())){
            userInfoVO.setAvatarUrl(AliyunOSSUtils.ossUrl + USER_PIC);
        }
        return userInfoVO;
    }

    public static void replaceNickAndPic(WxOpenIdResultDTO wxOpenIdResultDTO, CustomerUserDTO customerUserDTO) {
        log.info("转换用户信息 . 微信信息 --> wxOpenIdResultDTO:{}", JSON.toJSONString(wxOpenIdResultDTO));
        if(Objects.equals(WX_NICK_NAME, wxOpenIdResultDTO.getNickname())){
            customerUserDTO.setNickName(NICK_NAME);
            customerUserDTO.setUserPic(AliyunOSSUtils.ossUrl + USER_PIC);
        }else{
            customerUserDTO.setNickName(wxOpenIdResultDTO.getNickname());
            customerUserDTO.setUserPic(wxOpenIdResultDTO.getUserPic());
        }
    }


    public static YoushuUserInfoReq buildYoushuUserInfo(CustomerUser customerUser){
        YoushuUserInfoReq req = new YoushuUserInfoReq();
        String openId = customerUser.getOpenId();
        req.setOpen_id(StringUtils.isBlank(openId) ? DigestUtils.md5Hex("ryytn").substring(0, 28) : openId);
//        req.setOpen_id(DigestUtils.md5Hex(Objects.isNull(customerUser.getOpenId()) ? "ryytn" : customerUser.getOpenId()).substring(0,28));
        return req;
    }

    public static CustomerUser buildBatchUpdatePoint(CustomerUser userFromDb, Integer point, Integer grade) {
        CustomerUser customerUser = new CustomerUser();
        customerUser.setId(userFromDb.getId());
        customerUser.setPoint(point);
        customerUser.setGrade(grade);
        return customerUser;
    }

    public static CustomerUserVO convert2AdapterVO(CustomerUser customerUser) {
        CustomerUserVO customerUserVO = new CustomerUserVO();
        customerUserVO.setId(customerUser.getId());
        customerUserVO.setPhone(customerUser.getPhone());
        customerUserVO.setUserName(customerUser.getUserName());
        customerUserVO.setUserPic(customerUser.getUserPic());
        customerUserVO.setStatus(customerUser.getStatus());
        customerUserVO.setUserAccount(customerUser.getUserAccount());
        customerUserVO.setPassword(customerUser.getPassword());
        customerUserVO.setOpenId(customerUser.getOpenId());
        customerUserVO.setWechartOpenId(customerUser.getWechartOpenId());
        customerUserVO.setUnionId(customerUser.getUnionId());
        customerUserVO.setUserType(customerUser.getUserType());
        customerUserVO.setFirstLoginTime(customerUser.getFirstLoginTime());
        customerUserVO.setDeviceId(customerUser.getDeviceId());
        customerUserVO.setLatestLoginTime(customerUser.getLatestLoginTime());
        customerUserVO.setPrivilegeFlag(customerUser.getPrivilegeFlag());
        customerUserVO.setNascentFlag(customerUser.getNascentFlag());
        customerUserVO.setJoinTime(customerUser.getJoinTime());
        customerUserVO.setNickName(customerUser.getNickName());
        customerUserVO.setLogOff(customerUser.getLogOff());
        customerUserVO.setRemark(customerUser.getRemark());
        customerUserVO.setPoint(customerUser.getPoint());
        return customerUserVO;

    }

    public static CustomerUser buildPoint(CustomerUser customerUser, Long point4Update) {
        CustomerUser customerUser4Update = new CustomerUser();
        customerUser4Update.setId(customerUser.getId());
        customerUser4Update.setPoint(point4Update.intValue());
        return customerUser4Update;
    }

    public static CustomerUser buildUpdateOrderNickName(CustomerUser customerUser, String orderNickName) {
        CustomerUser customerUser4Update = new CustomerUser();
        customerUser4Update.setId(customerUser.getId());
        customerUser4Update.setOrderNickName(orderNickName);
        return customerUser4Update;
    }

    public static CustomerUser convert2PO(com.hengtiansoft.user.entity.vo.CustomerUserVO customerUserVO) {
        CustomerUser customerUser = new CustomerUser();
        customerUser.setPhone(customerUserVO.getPhone());
        customerUser.setUserName(customerUserVO.getUserName());
        customerUser.setUserPic(customerUserVO.getUserPic());
        customerUser.setStatus(customerUserVO.getStatus());
        customerUser.setUserAccount(customerUserVO.getUserAccount());
        customerUser.setPassword(customerUserVO.getPassword());
        customerUser.setOpenId(customerUserVO.getOpenId());
        customerUser.setUnionId(customerUserVO.getUnionId());
        customerUser.setFirstLoginTime(customerUserVO.getFirstLoginTime());
        customerUser.setLatestLoginTime(customerUserVO.getLatestLoginTime());
        customerUser.setPrivilegeFlag(customerUserVO.getPrivilegeFlag());
        customerUser.setNascentFlag(customerUserVO.getNascentFlag());
        customerUser.setJoinTime(customerUserVO.getJoinTime());
        customerUser.setNickName(customerUserVO.getNickName());
        customerUser.setOrderNickName(customerUserVO.getOrderNickName());
        customerUser.setGrade(customerUserVO.getGrade());
        customerUser.setId(customerUserVO.getId());
        return customerUser;
    }

    public static CustomerUser buildUpdateMinBuyDate(UserBuyDateDTO user) {
        CustomerUser customerUser4Update = new CustomerUser();
        customerUser4Update.setId(user.getId());
        customerUser4Update.setMinBuyDate(user.getMinBuyDate());
        return customerUser4Update;
    }

    public static List<UserExcelVO> convert2ExcelVO(List<UserListVO> userVOS) {
        if(CollectionUtils.isEmpty(userVOS)){
            return Lists.newArrayList();
        }
        List<UserExcelVO> result = Lists.newArrayList();
        for (UserListVO userVO : userVOS) {
            UserExcelVO userExcelVO = new UserExcelVO();
            userExcelVO.setPhone(userVO.getPhone());
            userExcelVO.setGradeStr(UserGradeEnum.getEnum(userVO.getGrade()) == null ? null : UserGradeEnum.getEnum(userVO.getGrade()).getDesc());
            userExcelVO.setPoint(userVO.getPoint());
            userExcelVO.setOrderCnt(userVO.getOrderCnt());
            userExcelVO.setTotalAmount(userVO.getTotalAmount());
            CustomerUserStatusEnum statusEnum = CustomerUserStatusEnum.getEnumOrNull(userVO.getStatus());
            if(statusEnum != null){
                userExcelVO.setStatusStr(statusEnum.getExportName());
            }
            userExcelVO.setFirstLoginTime(userVO.getFirstLoginTime());
            userExcelVO.setLastPayTime(userVO.getLastPayTime());
            userExcelVO.setWhiteListStr(FlagEnum.getEnumOrNull(userVO.getWhiteList()) != null ? FlagEnum.getEnumOrNull(userVO.getWhiteList()).getDesc() : null);
            userExcelVO.setRemark(userVO.getRemark());
            result.add(userExcelVO);
        }
        return result;
    }

    public static UserMatchVO convert2MatchVO(CustomerUser user, CustomerUserMore userMore){
        UserMatchVO userMatch = new UserMatchVO();
        userMatch.setId(user.getId());
        userMatch.setOpenId(user.getOpenId());
        userMatch.setMd5OpenId(user.getMd5OpenId());
        userMatch.setNickName(user.getOrderNickName());
        userMatch.setPhone(user.getPhone());
        userMatch.setGrade(user.getGrade());
        userMatch.setRegisterTime(user.getFirstLoginTime());

        userMatch.setFirstLoginTime(user.getFirstLoginTime());
        userMatch.setPoint(user.getPoint());
        userMatch.setStatus(user.getStatus());
        userMatch.setMessageSubscribePay(user.getMessageSubscribePay());
        if(Objects.nonNull(userMore)){
            userMatch.setRecentOrderGap(Objects.isNull(userMore.getRecentOrderGap()) ? 0 : userMore.getRecentOrderGap());
            userMatch.setMonthlyRepeat(userMore.getMonthlyRepeat());
            userMatch.setRecentContactTime(userMore.getRecentContactTime());
            userMatch.setJoinCnt(SafeUtil.nullSafe(UserCommonCntEnum.getEnumByCount(userMore.getJoinCnt()), UserCommonCntEnum::getCode));
            userMatch.setDiscountCnt(SafeUtil.nullSafe(UserCommonCntEnum.getEnumByCount(userMore.getDiscountCnt()), UserCommonCntEnum::getCode));
            userMatch.setLifeCycle(SafeUtil.nullSafe(UserLifeCycleEnum.getEnum(userMore.getLifeCycle()), UserLifeCycleEnum::getCode));
            userMatch.setOrderCnt(Objects.isNull(userMore.getOrderCnt()) ? 0 : userMore.getOrderCnt());
            userMatch.setTotalAmount(Objects.isNull(userMore.getTotalAmount()) ? BigDecimal.ZERO : userMore.getTotalAmount());
            userMatch.setLastVisitTime(userMore.getLastVisitTime());
            userMatch.setLastPayTime(userMore.getLastPayTime());
            userMatch.setRecentContactCnt(SafeUtil.nullSafe(UserCommonCntEnum.getEnumByCount(userMore.getRecentContactCnt()), UserCommonCntEnum::getCode));
            userMatch.setBuyCardCategoryIds(userMore.getBuyCardCategoryIds());
            userMatch.setBuyGroupIds(userMore.getBuyGroupIds());
            userMatch.setBuyItemCategoryIds(userMore.getBuyItemCategoryIds());
            userMatch.setCouponUsed(userMore.getCouponUsed());

            userMatch.setJoinContentTypeList(StringUtils.isNotEmpty(userMore.getJoinContentType())? Arrays.asList(userMore.getJoinContentType().split(",")) : Lists.newArrayList());
            userMatch.setJoinActivityTypeList(StringUtils.isNotEmpty(userMore.getJoinActivityType())? Arrays.asList(userMore.getJoinActivityType().split(",")) : Lists.newArrayList());
            userMatch.setJoinDiscountTypeList(StringUtils.isNotEmpty(userMore.getJoinDiscountType())? Arrays.asList(userMore.getJoinDiscountType().split(",")) : Lists.newArrayList());
            userMatch.setUserInteraction(StringUtils.isNotEmpty(userMore.getUserInteractType())? Arrays.asList(userMore.getUserInteractType().split(",")) : Lists.newArrayList());
        }
        if(StringUtils.isNotBlank(user.getBuyProductType())){
            userMatch.setBuyProductTypeList(Arrays.stream(user.getBuyProductType().split(","))
                    .map(Integer::valueOf)
                    .collect(Collectors.toList()));
        }
        return userMatch;
    }

    public static List<UserMatchExportVO> convert2MatchExportVO(List<UserMatchVO> list) {
        List<UserMatchExportVO> exportVOList = new ArrayList<>();
        for (UserMatchVO userMatchVO: list) {
            UserMatchExportVO exportVO = new UserMatchExportVO();
            exportVO.setOpenId(userMatchVO.getOpenId());
            exportVO.setMd5OpenId(userMatchVO.getMd5OpenId());
            exportVO.setPhone(userMatchVO.getPhone());
            exportVO.setNickName(userMatchVO.getNickName());
            if(Objects.nonNull(userMatchVO.getGrade())){
                exportVO.setGradeStr(UserGradeEnum.getDescByCode(userMatchVO.getGrade()));
            }
            exportVO.setPoint(userMatchVO.getPoint());
            exportVO.setOrderCntStr((Objects.isNull(userMatchVO.getOrderCnt()) ? 0 : userMatchVO.getOrderCnt()) + "次");
            exportVO.setTotalAmountStr((Objects.isNull(userMatchVO.getTotalAmount()) ? BigDecimal.ZERO.setScale(2, BigDecimal.ROUND_HALF_UP): userMatchVO.getTotalAmount().setScale(2, BigDecimal.ROUND_HALF_UP)) + "元");
            exportVO.setRecentOrderGapStr((Objects.isNull(userMatchVO.getRecentOrderGap()) ? 0 : userMatchVO.getRecentOrderGap()) + "天");
            exportVO.setMonthlyRepeatStr(SafeUtil.nullSafe(FlagEnum.getEnumOrNull(userMatchVO.getMonthlyRepeat()), FlagEnum::getDesc));
            exportVO.setStatusStr(SafeUtil.nullSafe(CustomerUserStatusEnum.getEnumOrNull(userMatchVO.getStatus()), CustomerUserStatusEnum::getExportName));
            exportVO.setFirstLoginTime(userMatchVO.getFirstLoginTime());
            exportVO.setLastVisitTime(userMatchVO.getLastVisitTime());
            exportVO.setLastPayTime(userMatchVO.getLastPayTime());
            exportVO.setRecentContactTime(userMatchVO.getRecentContactTime());
            exportVO.setRecentContactCntStr(SafeUtil.nullSafe(UserCommonCntEnum.getEnumOrNull(userMatchVO.getRecentContactCnt()), UserCommonCntEnum::getDesc));
            exportVO.setJoinCntStr(SafeUtil.nullSafe(UserCommonCntEnum.getEnumOrNull(userMatchVO.getJoinCnt()), UserCommonCntEnum::getDesc));
            exportVO.setDiscountCntStr(SafeUtil.nullSafe(UserCommonCntEnum.getEnumOrNull(userMatchVO.getDiscountCnt()), UserCommonCntEnum::getDesc));
            exportVO.setLifeCycleStr(SafeUtil.nullSafe(UserLifeCycleEnum.getEnumOrNull(userMatchVO.getLifeCycle()), UserLifeCycleEnum::getDesc));
            exportVO.setPayAmount(userMatchVO.getPayAmount());
            exportVO.setOffiaccountFansStr(SafeUtil.nullSafe(FlagEnum.getEnumOrNull(userMatchVO.getOffiaccountFans()), FlagEnum::getDesc));
            if(CollectionUtils.isNotEmpty(userMatchVO.getJoinContentTypeList())){
                exportVO.setJoinContentTypeListStr(userMatchVO.getJoinContentTypeList()
                        .stream()
                        .map(StatisticIndexEnum::getDescByCode)
                        .filter(StringUtils::isNotBlank)
                        .collect(Collectors.joining(", ")));
            }
            if(CollectionUtils.isNotEmpty(userMatchVO.getJoinActivityTypeList())){
                exportVO.setJoinActivityTypeListStr(userMatchVO.getJoinActivityTypeList()
                        .stream()
                        .map(StatisticIndexEnum::getDescByCode)
                        .filter(StringUtils::isNotBlank)
                        .collect(Collectors.joining(", ")));
            }
            if(CollectionUtils.isNotEmpty(userMatchVO.getJoinDiscountTypeList())){
                exportVO.setJoinDiscountTypeListStr(userMatchVO.getJoinDiscountTypeList()
                        .stream()
                        .map(StatisticIndexEnum::getDescByCode)
                        .filter(StringUtils::isNotBlank)
                        .collect(Collectors.joining(", ")));
            }
            exportVO.setMessageSubscribeReachStr(SafeUtil.nullSafe(FlagEnum.getEnumOrNull(userMatchVO.getMessageSubscribeReach()), FlagEnum::getDesc));
            exportVO.setMessageSubscribeReachTime(userMatchVO.getMessageSubscribeReachTime());
            if(CollectionUtils.isNotEmpty(userMatchVO.getUserInteraction())){
                exportVO.setUserInteractionStr(userMatchVO.getUserInteraction()
                        .stream()
                        .map(StatisticIndexEnum::getDescByCode)
                        .filter(StringUtils::isNotBlank)
                        .collect(Collectors.joining(", ")));
            }
            exportVO.setMessageSubscribePayStr(null != userMatchVO.getMessageSubscribePay() ? Objects.equals(userMatchVO.getMessageSubscribePay(), 1)? "回访并付款" : "回访未付款" : "");
            exportVO.setCouponUsedStr(Optional.ofNullable(userMatchVO.getCouponUsed()).map(couponUsedMap::get).orElse(""));
            exportVO.setBirthdayStr(SafeUtil.nullSafe(FlagEnum.getEnumOrNull(userMatchVO.getBirthday()), FlagEnum::getDesc));
            exportVO.setBindCardStr(SafeUtil.nullSafe(FlagEnum.getEnumOrNull(userMatchVO.getBindCard()), FlagEnum::getDesc));
            exportVO.setBindCardTime(userMatchVO.getBindCardTime());
            exportVO.setAddBuyStr(SafeUtil.nullSafe(FlagEnum.getEnumOrNull(userMatchVO.getAddBuy()), FlagEnum::getDesc));
            if(CollectionUtils.isNotEmpty(userMatchVO.getBuyProductTypeList())){
                exportVO.setBuyProductTypeListStr(userMatchVO.getBuyProductTypeList()
                        .stream()
                        .map(productTypeMap::get)
                        .filter(Objects::nonNull)
                        .collect(Collectors.joining(", ")));
            }
            exportVO.setBuyCardCategoryNamesStr(StringUtils.join(userMatchVO.getBuyCardCategoryNames(), ","));
            exportVO.setBuyItemCategoryNamesStr(StringUtils.join(userMatchVO.getBuyItemCategoryNames(),","));
            exportVO.setBuyGroupNamesStr(StringUtils.join(userMatchVO.getBuyGroupNames(), ","));
            exportVOList.add(exportVO);
        }
        return exportVOList;
    }

    public static CustomerUserMore buildInsert(CustomerUserMoreMatchDTO matchUser) {
        CustomerUserMore customerUserMore = new CustomerUserMore();
        customerUserMore.setUserId(matchUser.getId());
        customerUserMore.setOperator("system");
        Date now = new Date();
        customerUserMore.setCreateTime(now);
        customerUserMore.setUpdateTime(now);
        customerUserMore.setDelflag(DeleteFlagEnum.IS_NOT_DELETE.getCode());
        customerUserMore.setTotalAmount(BigDecimal.ZERO);
        customerUserMore.setOrderCnt(0);
        customerUserMore.setRecentOrderGap(0);
        customerUserMore.setRecentContactCnt(0);
        customerUserMore.setJoinCnt(0);
        customerUserMore.setDiscountCnt(0);
        return customerUserMore;
    }
}
