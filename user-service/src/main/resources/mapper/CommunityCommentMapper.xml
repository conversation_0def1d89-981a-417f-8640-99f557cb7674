<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hengtiansoft.user.entity.mapper.CommunityCommentMapper">
  <resultMap id="BaseResultMap" type="com.hengtiansoft.user.entity.po.CommunityComment">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="post_id" jdbcType="BIGINT" property="postId" />
    <result column="post_user_id" jdbcType="BIGINT" property="postUserId" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="parent_id" jdbcType="BIGINT" property="parentId" />
    <result column="reply_to_user_id" jdbcType="BIGINT" property="replyToUserId" />
    <result column="author_comment" jdbcType="TINYINT" property="authorComment" />
    <result column="top_comment" jdbcType="TINYINT" property="topComment" />
    <result column="like_count" jdbcType="INTEGER" property="likeCount" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="delflag" jdbcType="TINYINT" property="delflag" />
    <result column="content" jdbcType="LONGVARCHAR" property="content" />
  </resultMap>

  <!-- 批量查询帖子的最新一级评论 -->
  <select id="findLatestFirstLevelCommentsByPostIds" resultMap="BaseResultMap">
    SELECT cc.* FROM community_comment cc
    INNER JOIN (
      SELECT post_id, MAX(create_time) as latest_time
      FROM community_comment
      WHERE post_id IN
      <foreach collection="postIds" item="postId" open="(" close=")" separator=",">
        #{postId}
      </foreach>
      AND parent_id = 0
      AND delflag = 0
      GROUP BY post_id
    ) latest ON cc.post_id = latest.post_id AND cc.create_time = latest.latest_time
    WHERE cc.parent_id = 0
    AND cc.delflag = 0
  </select>

  <!-- 批量统计多个评论的回复数量 -->
  <select id="batchCountReplies" resultType="com.hengtiansoft.user.entity.vo.CommentReplyCountVO">
    SELECT parent_id as parentId, COUNT(1) as count
    FROM community_comment
    WHERE parent_id IN
    <foreach collection="commentIds" item="commentId" open="(" close=")" separator=",">
      #{commentId}
    </foreach>
    AND delflag = 0
    GROUP BY parent_id
  </select>

  <!-- 增加评论点赞数 -->
  <update id="incrementLikeCount">
    UPDATE
      community_comment
    SET
      like_count = like_count + 1
    WHERE
      id = #{commentId}
      AND delflag = 0
  </update>

  <!-- 减少评论点赞数 -->
  <update id="decrementLikeCount">
    UPDATE
      community_comment
    SET
      like_count = CASE WHEN like_count > 0 THEN like_count - 1 ELSE 0 END
    WHERE
      id = #{commentId}
      AND delflag = 0
  </update>

  <!-- 批量查询每个一级评论下的前几条二级评论 -->
  <select id="findTopRepliesByParentIds" resultMap="BaseResultMap">
    SELECT * FROM (
      SELECT cc.*,
             @row_num := IF(@current_parent_id = cc.parent_id, @row_num + 1, 1) AS row_num,
             @current_parent_id := cc.parent_id
      FROM community_comment cc,
           (SELECT @row_num := 0, @current_parent_id := 0) AS vars
      WHERE cc.parent_id IN
      <foreach collection="parentIds" item="parentId" open="(" close=")" separator=",">
        #{parentId}
      </foreach>
      AND cc.delflag = 0
      ORDER BY cc.parent_id, cc.create_time asc
    ) AS ranked
    WHERE ranked.row_num <![CDATA[<=]]> #{limit}
  </select>

  <!-- 批量统计用户发表的评论数量 -->
  <select id="batchCountCommentsByUserIds" resultType="com.hengtiansoft.user.entity.vo.CommunityUserCommentCountVO">
    SELECT
      user_id AS userId,
      COUNT(id) AS commentCount
    FROM
      community_comment
    WHERE
      delflag = 0
      AND user_id IN
    <foreach collection="userIds" item="userId" open="(" separator="," close=")">
      #{userId}
    </foreach>
    GROUP BY user_id
  </select>

  <!-- 批量统计用户帖子收到的评论数量 -->
  <select id="batchCountCommentReceivedByUserIds" resultType="com.hengtiansoft.user.entity.vo.CommunityUserCommentCountVO">
    select userId,count(1) as commentCount from (
    SELECT
      id,
      post_user_id as userId
      FROM
      community_comment
      WHERE
      delflag = 0
      AND post_user_id IN
    <foreach collection="userIds" item="userId" open="(" separator="," close=")">
          #{userId}
    </foreach>
    UNION ALL
    SELECT
    id,
    reply_to_user_id as userId
    FROM
    community_comment
    WHERE
    delflag = 0
    AND reply_to_user_id IN
    <foreach collection="userIds" item="userId" open="(" separator="," close=")">
      #{userId}
    </foreach>
    UNION ALL
    SELECT
    id,
    parent_user_id as userId
    FROM
    community_comment
    WHERE
    delflag = 0
    AND parent_user_id IN
    <foreach collection="userIds" item="userId" open="(" separator="," close=")">
      #{userId}
    </foreach>
    ) t
    group by userId
  </select>
  <select id="findFirstCommentByPostId" resultType="com.hengtiansoft.user.entity.po.CommunityComment">
    select * from community_comment where post_id = #{postId} and parent_id = 0 order by id asc limit 1
  </select>
</mapper>