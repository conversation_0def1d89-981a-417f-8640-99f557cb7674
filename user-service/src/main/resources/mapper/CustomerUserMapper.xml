<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hengtiansoft.user.entity.mapper.CustomerUserMapper">
  <resultMap id="BaseResultMap" type="com.hengtiansoft.user.entity.po.CustomerUser"/>

  <select id="findList" parameterType="com.hengtiansoft.user.entity.dto.UserListDTO" resultType="com.hengtiansoft.user.entity.vo.UserListVO">
    select cu.id,cu.user_name,cu.status,cu.first_login_time,cu.latest_login_time,cu.phone as userAccount,cu.remark,cu.user_Pic,
           cu.phone, cu.grade, cu.point, cu.nick_name, cu.join_time, cum.last_pay_time, cum.order_cnt, cum.total_amount,
           cum.average_user, cum.last_order_time, cum.last_visit_time, cu.order_nick_name, cu.nascent_flag, cu.white_list
    from customer_user cu
    left join customer_user_more cum on cum.user_id=cu.id and cum.delflag=0
    <include refid="query_where"/>
     order by cu.update_time desc,cu.create_time desc
  </select>

  <select id="findListCount" resultType="java.lang.Integer">
    select count(*) from (
    select cu.id,cu.user_name,cu.status,cu.first_login_time,cu.latest_login_time,cu.phone as userAccount,cu.remark,cu.user_Pic,
    cu.phone, cu.grade, cu.point, cu.nick_name, cu.join_time, cum.last_pay_time, cum.order_cnt, cum.total_amount,
    cum.average_user, cum.last_order_time, cum.last_visit_time, cu.order_nick_name, cu.nascent_flag, cu.white_list
    from customer_user cu
    left join customer_user_more cum on cum.user_id=cu.id and cum.delflag=0
    <include refid="query_where"/>
    ) tmp
  </select>

  <select id="findByConditionSql" resultType="com.hengtiansoft.user.entity.po.CustomerUser">
    select cu.*,cum.message_subscribe_pay as messageSubscribePay,cum.coupon_used as couponUsed,cum.buy_product_type as buyProductType
    from customer_user cu
    left join customer_user_more cum on cum.user_id=cu.id and cum.delflag=0
    <include refid="query_where_sql"/>
    order by cu.update_time desc,cu.create_time desc
  </select>

  <sql id="query_where_sql">

    <where>
        <if test="openIdList != null and openIdList.size > 0">
          and cu.open_id in
          <foreach collection="openIdList" item="item" open="(" separator="," close=")" index="index">
            #{item}
          </foreach>
        </if>
        <if test="md5OpenIdList != null and md5OpenIdList.size > 0">
          and cu.md5_open_id in
          <foreach collection="md5OpenIdList" item="item" open="(" separator="," close=")" index="index">
            #{item}
          </foreach>
        </if>
        <if test="nickName !=null and nickName !=''">
            and cu.order_nick_name like CONCAT('%',#{nickName},'%')
        </if>
        <if test="phoneList != null and phoneList.size > 0">
            and cu.phone in
            <foreach collection="phoneList" item="item" open="(" separator="," close=")" index="index">
                #{item}
            </foreach>
        </if>
        <if test="gradeList != null and gradeList.size > 0">
            and cu.grade in
            <foreach collection="gradeList" item="item" open="(" separator="," close=")" index="index">
                #{item}
            </foreach>
        </if>
        <if test="ids != null and ids.size > 0">
            and cu.id in
            <foreach collection="ids" item="item" open="(" separator="," close=")" index="index">
                #{item}
            </foreach>
        </if>
        <if test="registerTimeStart !=null">
            and cu.first_login_time <![CDATA[>=]]> #{registerTimeStart}
        </if>
        <if test="registerTimeEnd !=null">
            and cu.first_login_time <![CDATA[<=]]> #{registerTimeEnd}
        </if>

       <if test="lastPayTimeStart != null">
            and cum.last_pay_time <![CDATA[>=]]> #{lastPayTimeStart}
        </if>
        <if test="lastPayTimeEnd != null">
            and cum.last_pay_time <![CDATA[<=]]> #{lastPayTimeEnd}
        </if>
        <if test="lastVisitTimeStart != null">
            and cum.last_visit_time <![CDATA[>=]]> #{lastVisitTimeStart}
        </if>
        <if test="lastVisitTimeEnd != null">
            and cum.last_visit_time <![CDATA[<=]]> #{lastVisitTimeEnd}
        </if>
        <if test="status != null">
            and cu.status = #{status}
        </if>
        <if test="monthlyRepeat != null">
            and cum.monthly_repeat = #{monthlyRepeat}
        </if>

        <if test="isOpenId != null and isOpenId ==0 ">
            and cu.open_id is null
        </if>
        <if test="isOpenId != null and isOpenId ==1 ">
            and cu.open_id is not null
        </if>
        <if test="isMd5OpenId != null and isMd5OpenId ==0">
            and cu.md5_open_id is null
        </if>
        <if test="isMd5OpenId != null and isMd5OpenId ==1">
            and cu.md5_open_id is not null
        </if>
        <if test="isPhone != null and isPhone ==0">
            and cu.phone is null
        </if>
        <if test="isPhone != null and isPhone ==1">
            and cu.phone is not null
        </if>
        <if test="birthday != null and birthday ==0">
            <if test="queryTimeStart != null">
                and (EXTRACT(MONTH FROM cu.birth_day) <![CDATA[<]]> EXTRACT(MONTH FROM #{queryTimeStart})
                or (EXTRACT(MONTH FROM cu.birth_day) = EXTRACT(MONTH FROM #{queryTimeStart})
                and DAYOFMONTH(cu.birth_day) <![CDATA[<]]> DAYOFMONTH(#{queryTimeStart})))
            </if>
            <if test="queryTimeEnd != null">
                and (EXTRACT(MONTH FROM cu.birth_day) > EXTRACT(MONTH FROM #{queryTimeEnd})
                or (EXTRACT(MONTH FROM cu.birth_day) = EXTRACT(MONTH FROM #{queryTimeEnd})
                and DAYOFMONTH(cu.birth_day) > DAYOFMONTH(#{queryTimeEnd})))
            </if>
        </if>
        <if test="birthday != null and birthday ==1">
            <if test="queryTimeStart != null">
                and (EXTRACT(MONTH FROM cu.birth_day) > EXTRACT(MONTH FROM #{queryTimeStart})
                or (EXTRACT(MONTH FROM cu.birth_day) = EXTRACT(MONTH FROM #{queryTimeStart})
                and DAYOFMONTH(cu.birth_day) >= DAYOFMONTH(#{queryTimeStart})))
            </if>
            <if test="queryTimeEnd != null">
                and (EXTRACT(MONTH FROM cu.birth_day) <![CDATA[<]]> EXTRACT(MONTH FROM #{queryTimeEnd})
                or (EXTRACT(MONTH FROM cu.birth_day) = EXTRACT(MONTH FROM #{queryTimeEnd})
                and DAYOFMONTH(cu.birth_day) <![CDATA[<=]]> DAYOFMONTH(#{queryTimeEnd})))
            </if>
        </if>
        <if test="joinCntMin != null">
            and cum.join_cnt <![CDATA[>=]]> #{joinCntMin}
        </if>
        <if test="joinCntMax != null">
            and cum.join_cnt <![CDATA[<]]> #{joinCntMax}
        </if>
        <if test="discountCntMin != null">
            and cum.discount_cnt <![CDATA[>=]]> #{discountCntMin}
        </if>
        <if test="discountCntMax != null">
            and cum.discount_cnt <![CDATA[<]]> #{discountCntMax}
        </if>
        <if test="recentContactCntMin != null">
            and cum.recent_contact_cnt <![CDATA[>=]]> #{recentContactCntMin}
        </if>
        <if test="recentContactCntMax != null">
            and cum.recent_contact_cnt <![CDATA[<]]> #{recentContactCntMax}
        </if>
        <if test="recentOrderGapMin != null">
            and cum.recent_order_gap <![CDATA[>=]]> #{recentOrderGapMin}
        </if>
        <if test="recentOrderGapMax != null">
            and cum.recent_order_gap <![CDATA[<]]> #{recentOrderGapMax}
        </if>
        <if test="orderCntMin != null">
            and cum.order_cnt <![CDATA[>=]]> #{orderCntMin}
        </if>
        <if test="orderCntMax != null">
            and cum.order_cnt <![CDATA[<]]> #{orderCntMax}
        </if>
        <if test="totalAmountMin != null">
            and cum.total_amount <![CDATA[>=]]> #{totalAmountMin}
        </if>
        <if test="totalAmountMax != null">
            and cum.total_amount <![CDATA[<]]> #{totalAmountMax}
        </if>
        <if test="lifeCycle != null">
            and cum.life_cycle = #{lifeCycle}
        </if>
        <if test="buyProductTypeList != null and buyProductTypeList.size != 0">
            and (
            <foreach collection="buyProductTypeList" index="index" item="item" open="" separator="or" close="">
                 find_in_set(#{item}, cum.buy_product_type)
            </foreach>
            )
        </if>
        <if test="buyCardCategoryIds != null and buyCardCategoryIds.size != 0">
            and cu.id in(
            select cump.user_id from customer_user_more_product cump where
             (
            <foreach collection="buyCardCategoryIds" index="index" item="item" open="" separator="or" close="">
                find_in_set(#{item}, cump.card_category_id)
            </foreach>
            )
            )
        </if>
        <if test="buyGroupIds != null and buyGroupIds.size != 0">
            and cu.id in(
            select cump.user_id from customer_user_more_product cump where
            (
            <foreach collection="buyGroupIds" index="index" item="item" open="" separator="or" close="">
                find_in_set(#{item}, cump.group_id)
            </foreach>
            )
            )
        </if>
        <if test="buyItemCategoryIds != null and buyItemCategoryIds.size != 0">
            and cu.id in(
            select cump.user_id from customer_user_more_product cump where
            (
            <foreach collection="buyItemCategoryIds" index="index" item="item" open="" separator="or" close="">
                find_in_set(#{item}, cump.cate_id)
            </foreach>
            )
            )
        </if>
        <if test="lifeCycleList != null and lifeCycleList.size != 0">
            and cum.life_cycle in
            <foreach close=")" collection="lifeCycleList" index="index" item="item" open="(" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="messageSubscribePay != null">
            and cum.message_subscribe_pay = #{messageSubscribePay}
        </if>
        <if test="couponUsed != null">
            and cum.coupon_used = #{couponUsed}
        </if>
        <if test="joinContentTypeList != null and joinContentTypeList.size() > 0">
            and (
            <foreach collection="joinContentTypeList" index="index" item="item" open="" separator="or" close="">
                find_in_set(#{item}, cum.join_content_type)
            </foreach>
            )
        </if>
        <if test="joinActivityTypeList != null and joinActivityTypeList.size() > 0">
            and (
            <foreach collection="joinActivityTypeList" index="index" item="item" open="" separator="or" close="">
                find_in_set(#{item}, cum.join_activity_type)
            </foreach>
            )
        </if>
        <if test="joinDiscountTypeList != null and joinDiscountTypeList.size() > 0">
            and (
            <foreach collection="joinDiscountTypeList" index="index" item="item" open="" separator="or" close="">
                find_in_set(#{item}, cum.join_discount_type)
            </foreach>
            )
        </if>
        <if test="userInteraction != null and userInteraction.size() > 0">
            and (
            <foreach collection="userInteraction" index="index" item="item" open="" separator="or" close="">
                find_in_set(#{item}, cum.user_interact_type)
            </foreach>
            )
        </if>
        <if test="visitTimeStart != null or visitTimeEnd != null">
            and EXISTS (select 1 from user_visit_detail uvd where uvd.user_id = cu.id
            <if test="visitTimeStart != null">
                and uvd.visit_time >= #{visitTimeStart}
            </if>
            <if test="visitTimeEnd != null">
                and uvd.visit_time <![CDATA[<=]]> #{visitTimeEnd}
            </if>
            )
        </if>
      and cu.delflag = 0
      and cu.log_off = 0
    </where>
  </sql>



  <sql id="query_where">
    <if test="privilegeIdList != null and privilegeIdList.size > 0">
      inner join privilege_item_user piu on piu.value_id = cu.id and piu.type=2 and piu.delflag=0
    </if>
    <where>
      <if test="id !=null">
        and cu.id=#{id}
      </if>
      <if test="whiteList !=null">
        and cu.white_list=#{whiteList}
      </if>
      <if test="privilegeIdList != null and privilegeIdList.size > 0">
        and piu.lable_id in
        <foreach collection="privilegeIdList" item="item" open="(" separator="," close=")" index="index">
          #{item}
        </foreach>
      </if>
      <if test="phone !=null and phone !=''">
        and cu.phone=#{phone}
      </if>
      <if test="status !=null">
        and cu.status=#{status}
      </if>
      <if test="gradeList !=null and gradeList.size > 0">
        and  cu.grade in
        <foreach collection="gradeList" item="item" open="(" separator="," close=")" index="index">
          #{item}
        </foreach>
      </if>
      <!--注册开始时间-->
      <if test="joinTimeStart !=null">
        and cu.join_time <![CDATA[>=]]> #{joinTimeStart}
      </if>
      <!--注册结束时间-->
      <if test="joinTimeEnd !=null">
        and cu.join_time <![CDATA[<=]]> #{joinTimeEnd}
      </if>
      <!--登录开始时间-->
      <if test="firstLoginTimeStart !=null">
        and cu.first_login_time <![CDATA[>=]]> #{firstLoginTimeStart}
      </if>
      <!--登录结束时间-->
      <if test="firstLoginTimeEnd !=null">
        and cu.first_login_time <![CDATA[<=]]> #{firstLoginTimeEnd}
      </if>
      <!--支付开始时间-->
      <if test="payTimeStart !=null">
        and cum.last_pay_time <![CDATA[>=]]> #{payTimeStart}
      </if>
      <!--支付结束时间-->
      <if test="payTimeEnd !=null">
        and cum.last_pay_time <![CDATA[<=]]> #{payTimeEnd}
      </if>
      <if test="ids !=null and ids.size > 0">
        and  cu.id in
        <foreach collection="ids" item="id" open="(" separator="," close=")" index="index">
          #{id}
        </foreach>
      </if>
      <if test="labelId !=null">
            and cu.phone in (select phone from people where delflag = 0 and label_id = #{labelId})
      </if>
      <if test="privilegeLabelId != null">
          and cu.id in (select value_id from privilege_item_user
                        where delflag = 0
                        and type = 2
                        and privilege_lable_id = #{privilegeLabelId}
                        <if test="privilegeUserStatus !=null">
                        and status = #{privilegeUserStatus}
                        </if>
                        )
      </if>
      and cu.delflag = 0
      and cu.log_off = 0
    </where>
    <if test="privilegeIdList != null and privilegeIdList.size > 0">
      group by cu.id
    </if>
  </sql>

  <select id="findPrivilege" parameterType="com.hengtiansoft.user.entity.dto.UserQueryDTO" resultType="com.hengtiansoft.user.entity.vo.UserListVO">
    select cu.id,cu.user_name,cu.status,cu.first_login_time,cu.latest_login_time,cu.phone as userAccount, cu.privilege_flag as privilegeFlag from customer_user cu
    <where>
      <if test="phone !=null and phone !=''">
        and cu.phone = #{phone}
      </if>
      <if test="ids !=null and ids.size > 0">
        and  cu.id in
        <foreach collection="ids" item="id" open="(" separator="," close=")" index="index">
          #{id}
        </foreach>
      </if>
      and cu.delflag=0
      and cu.privilege_flag = 1
      and cu.log_off = 0
    </where>
    order by cu.create_time desc
  </select>
  
  <insert id="saveList"  parameterType="java.util.List">
    insert into customer_user(id, phone, user_name,
    user_type, user_pic, status,
    user_account, open_id, wechart_open_id,
    union_id, first_login_time, delflag)
    values
    <foreach collection="list" item="it" separator=",">
      (#{it.id},#{it.phone},#{it.userName}, #{it.userType}, #{it.userPic}, #{it.status}, #{it.userAccount},
      #{it.openId}, #{it.wechartOpenId}, #{it.unionId}, #{it.firstLoginTime}, #{it.delflag})
    </foreach>
  </insert>

  <select id="dealRuleCompute" resultType="java.lang.Long">
    select distinct cu.id
    from customer_user cu
                 left join order_info oi on oi.user_id=cu.id and oi.channel='lpk'
                                                 and oi.order_type in (0,1,2,3,4)
                                                 and oi.parent_type in (1,2)
                                                 and oi.order_status not in(0,4)
                                                <if test="rule.timeStart !=null ">
                                                    and oi.pay_time >= #{rule.timeStart}
                                                </if>
                                                <if test="rule.timeEnd !=null ">
                                                    and oi.pay_time <![CDATA[<=]]> #{rule.timeEnd}
                                                </if>
                                                 and oi.delflag = 0
    where cu.log_off = 0 and cu.delflag = 0
    group by cu.id
    having 1=1
    <if test="rule.dealMin !=null ">
      and count(oi.id) >= #{rule.dealMin}
    </if>
    <if test="rule.dealMax !=null ">
      and count(oi.id) <![CDATA[<=]]> #{rule.dealMax}
    </if>
    <if test="rule.amountMin !=null ">
          and sum(oi.real_amount) >= #{rule.amountMin}
    </if>
    <if test="rule.amountMax !=null ">
          and sum(oi.real_amount) <![CDATA[<=]]> #{rule.amountMax}
    </if>
  </select>

  <select id="amountRuleCompute" resultType="java.lang.Long">
    select cu.id
    from customer_user cu
                 inner join order_info oi on oi.user_id=cu.id and oi.channel='lpk' and oi.order_type in (0,1,2,3,4)
    where oi.parent_type in (1,2) and cu.log_off = 0
    and oi.create_time >= #{rule.timeStart}
    and oi.create_time <![CDATA[<=]]> #{rule.timeEnd}
    group by cu.id
    having 1=1
    <if test="rule.amountMin !=null ">
      and sum(oi.real_amount) >= #{rule.amountMin}
    </if>
    <if test="rule.amountMax !=null ">
      and sum(oi.real_amount) <![CDATA[<=]]> #{rule.amountMax}
    </if>
  </select>

  <update id="updateBatch" parameterType="java.util.List">
    update customer_user
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="point = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.point != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.point,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="grade = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.grade != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.grade,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="order_nick_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.orderNickName != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.orderNickName,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="min_buy_date = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.minBuyDate != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.minBuyDate}
          </if>
        </foreach>
      </trim>
      <trim prefix="is_buy = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isBuy != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.isBuy,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="wechart_open_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.wechartOpenId != null and item.wechartOpenId !=''">
            when id = #{item.id,jdbcType=INTEGER} then #{item.wechartOpenId}
          </if>
        </foreach>
      </trim>
      <trim prefix="birth_day = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.birthDay != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.birthDay}
          </if>
        </foreach>
      </trim>
      <trim prefix="white_list = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.whiteList != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.whiteList}
          </if>
        </foreach>
      </trim>
      <trim prefix="md5_open_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.md5OpenId != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.md5OpenId}
          </if>
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=INTEGER}
    </foreach>
  </update>

  <select id="pointRuleCompute" resultType="java.lang.Long">
    select cu.id
    from customer_user cu
    where cu.log_off = 0
    <if test="rule.pointMin != null ">
      and cu.point >= #{rule.pointMin}
    </if>
    <if test="rule.pointMax != null ">
      and cu.point <![CDATA[<=]]> #{rule.pointMax}
    </if>
  </select>

  <select id="averageRuleCompute" resultType="java.lang.Long">
    select cu.id
    from customer_user cu
                 inner join order_info oi on oi.user_id=cu.id and oi.channel='lpk' and oi.order_type in (0,1,2,3,4)
    where oi.parent_type in (1,2) and cu.log_off = 0
    and oi.create_time >= #{rule.timeStart}
    and oi.create_time <![CDATA[<=]]> #{rule.timeEnd}
    group by cu.id
    having 1=1
    <if test="rule.averageAmountMin !=null ">
      and sum(oi.real_amount) <![CDATA[>=]]> #{rule.averageAmountMin}
    </if>
    <if test="rule.averageAmountMax !=null ">
      and sum(oi.real_amount) <![CDATA[<=]]> #{rule.averageAmountMax}
    </if>
  </select>

    <select id="shopCartOfProductIdRuleCompute" resultType="java.lang.Long">
        select distinct cu.id
        from customer_user cu
        inner join shop_cart sc on sc.user_id=cu.id
        where cu.log_off=0
        <if test="rule.timeStart !=null ">
            and sc.create_time >= #{rule.timeStart}
        </if>
        <if test="rule.timeEnd !=null ">
            and sc.create_time <![CDATA[<=]]> #{rule.timeEnd}
        </if>
        <if test="rule.targetIds != null and rule.targetIds.size > 0">
            and sc.product_id in
            <foreach collection="rule.targetIds" item="item" open="(" separator="," close=")" index="index">
                #{item}
            </foreach>
        </if>
    </select>

  <select id="productBuyOfProductIdRuleCompute" resultType="java.lang.Long">
    select distinct cu.id
    from customer_user cu
                 inner join order_info oi on oi.user_id=cu.id and oi.channel='lpk' and oi.order_type in (0,1,2,3,4)
                 inner join order_sku os on os.order_no=oi.order_no
    where oi.parent_type in (1,3) and cu.log_off = 0
    and oi.order_status not in (4,0)
    <if test="rule.timeStart !=null ">
      and oi.pay_time >= #{rule.timeStart}
    </if>
    <if test="rule.timeEnd !=null ">
      and oi.pay_time <![CDATA[<=]]> #{rule.timeEnd}
    </if>
    <if test="rule.realType !=null and rule.realType == 1">
      and oi.order_status != 5
    </if>
    <if test="rule.targetIds != null and rule.targetIds.size > 0">
      and os.product_id in
      <foreach collection="rule.targetIds" item="item" open="(" separator="," close=")" index="index">
          #{item}
      </foreach>
    </if>
  </select>

    <select id="productBuyOfGroupIdRuleCompute" resultType="java.lang.Long" timeout="900">
        select distinct cu.id
        from customer_user cu
        inner join order_info oi on oi.user_id=cu.id and oi.channel='lpk' and oi.order_type in (0,1,2,3,4)
        inner join order_sku os on os.order_no=oi.order_no
        inner join item_product_group ipg on ipg.product_id = os.product_id
        where oi.parent_type in (1,3) and cu.log_off = 0
        and oi.order_status not in (4,0)
        <if test="rule.timeStart !=null ">
            and oi.pay_time >= #{rule.timeStart}
        </if>
        <if test="rule.timeEnd !=null ">
            and oi.pay_time <![CDATA[<=]]> #{rule.timeEnd}
        </if>
        <if test="rule.realType !=null and rule.realType == 1">
            and oi.order_status != 5
        </if>
        <if test="rule.targetIds != null and rule.targetIds.size > 0">
            and ipg.group_id in
            <foreach collection="rule.targetIds" item="item" open="(" separator="," close=")" index="index">
                #{item}
            </foreach>
        </if>
    </select>
    <select id="productBuyOfCardCategoryIdRuleCompute" resultType="java.lang.Long" timeout="900">
        select distinct cu.id
        from customer_user cu
        inner join order_info oi on oi.user_id=cu.id and oi.channel='lpk' and oi.order_type in (0,1,2,3,4)
        inner join order_sku os on os.order_no=oi.order_no
        inner join order_sku_card osc on osc.order_sku_id = os.id
        inner join card c on c.card_number = osc.card_number
        where oi.parent_type in (1,3) and cu.log_off = 0
        and oi.order_status not in (4,0)
        <if test="rule.timeStart !=null ">
            and oi.pay_time >= #{rule.timeStart}
        </if>
        <if test="rule.timeEnd !=null ">
            and oi.pay_time <![CDATA[<=]]> #{rule.timeEnd}
        </if>
        <if test="rule.realType !=null and rule.realType == 1">
            and oi.order_status != 5
        </if>
        <if test="rule.targetIds != null and rule.targetIds.size > 0">
            and c.category_id in
            <foreach collection="rule.targetIds" item="item" open="(" separator="," close=")" index="index">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="shopCartOfGroupIdRuleCompute" resultType="java.lang.Long">
        select distinct cu.id
        from customer_user cu
        inner join shop_cart sc on sc.user_id=cu.id
        inner join item_product_group ipg on ipg.product_id = sc.product_id
        where cu.log_off=0
        <if test="rule.timeStart !=null ">
            and sc.create_time >= #{rule.timeStart}
        </if>
        <if test="rule.timeEnd !=null ">
            and sc.create_time <![CDATA[<=]]> #{rule.timeEnd}
        </if>
        <if test="rule.targetIds != null and rule.targetIds.size > 0">
            and ipg.group_id in
            <foreach collection="rule.targetIds" item="item" open="(" separator="," close=")" index="index">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="productBuyOfCategoryIdRuleCompute" resultType="java.lang.Long">
        select distinct cu.id
        from customer_user cu
        inner join order_info oi on oi.user_id=cu.id and oi.channel='lpk' and oi.order_type in (0,1,2,3,4)
        inner join order_sku os on os.order_no=oi.order_no
        inner join item_product ip on ip.id = os.product_id
        where oi.parent_type in (1,3) and cu.log_off = 0
        and oi.order_status not in (4,0)
        <if test="rule.timeStart !=null ">
            and oi.pay_time >= #{rule.timeStart}
        </if>
        <if test="rule.timeEnd !=null ">
            and oi.pay_time <![CDATA[<=]]> #{rule.timeEnd}
        </if>
        <if test="rule.realType !=null and rule.realType == 1">
            and oi.order_status != 5
        </if>
        <if test="rule.targetIds != null and rule.targetIds.size > 0">
            and ip.cate_id in
            <foreach collection="rule.targetIds" item="item" open="(" separator="," close=")" index="index">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="shopCartOfCategoryIdRuleCompute" resultType="java.lang.Long">
        select distinct cu.id
        from customer_user cu
        inner join shop_cart sc on sc.user_id=cu.id
        inner join item_product ip on ip.id = sc.product_id
        where cu.log_off=0
        <if test="rule.timeStart !=null ">
            and sc.create_time >= #{rule.timeStart}
        </if>
        <if test="rule.timeEnd !=null ">
            and sc.create_time <![CDATA[<=]]> #{rule.timeEnd}
        </if>
        <if test="rule.targetIds != null and rule.targetIds.size > 0">
            and ip.cate_id in
            <foreach collection="rule.targetIds" item="item" open="(" separator="," close=")" index="index">
                #{item}
            </foreach>
        </if>
    </select>

  <select id="productNotBuyOfProductIdRuleCompute" resultType="java.lang.Long">
    select distinct cu.id
    from customer_user cu
    left join (
      select cu.id
      from customer_user cu
      inner join order_info oi on oi.user_id=cu.id and oi.channel='lpk' and oi.order_type in (0,1,2,3,4)
      inner join order_sku os on os.order_no=oi.order_no
      where oi.parent_type in (1,3) and cu.log_off = 0
      and oi.order_status not in (4,0)
      <if test="rule.timeStart !=null ">
          and oi.pay_time >= #{rule.timeStart}
      </if>
      <if test="rule.timeEnd !=null ">
          and oi.pay_time <![CDATA[<=]]> #{rule.timeEnd}
      </if>
      <if test="rule.targetIds != null and rule.targetIds.size > 0">
          and os.product_id in
          <foreach collection="rule.targetIds" item="item" open="(" separator="," close=")" index="index">
              #{item}
          </foreach>
      </if>
      ) tmp on tmp.id=cu.id
    where cu.log_off = 0 and tmp.id is null;
  </select>

  <select id="productNotBuyOfGroupIdRuleCompute" resultType="java.lang.Long" timeout="900">
    select distinct cu.id
    from customer_user cu
    left join (
      select cu.id
      from customer_user cu
      inner join order_info oi on oi.user_id=cu.id and oi.channel='lpk' and oi.order_type in (0,1,2,3,4)
      inner join order_sku os on os.order_no=oi.order_no
      inner join item_product_group ipg on ipg.product_id = os.product_id
      where oi.parent_type in (1,3) and cu.log_off = 0 and ipg.delflag = 0
      and oi.order_status not in (4,0)
      <if test="rule.timeStart !=null ">
          and oi.pay_time >= #{rule.timeStart}
      </if>
      <if test="rule.timeEnd !=null ">
          and oi.pay_time <![CDATA[<=]]> #{rule.timeEnd}
      </if>
      <if test="rule.targetIds != null and rule.targetIds.size > 0">
          and ipg.group_id in
          <foreach collection="rule.targetIds" item="item" open="(" separator="," close=")" index="index">
              #{item}
          </foreach>
      </if>
    ) tmp on tmp.id=cu.id
    where cu.log_off = 0 and tmp.id is null;
  </select>

  <select id="productNotBuyOfCategoryIdRuleCompute" resultType="java.lang.Long">
    select distinct cu.id
    from customer_user cu
    left join (
      select cu.id
      from customer_user cu
      inner join order_info oi on oi.user_id=cu.id and oi.channel='lpk' and oi.order_type in (0,1,2,3,4)
      inner join order_sku os on os.order_no=oi.order_no
      inner join item_product ip on ip.id = os.product_id
      where oi.parent_type in (1,3) and cu.log_off = 0
      and oi.order_status not in (4,0)
      <if test="rule.timeStart !=null ">
          and oi.pay_time >= #{rule.timeStart}
      </if>
      <if test="rule.timeEnd !=null ">
          and oi.pay_time <![CDATA[<=]]> #{rule.timeEnd}
      </if>
      <if test="rule.targetIds != null and rule.targetIds.size > 0">
          and ip.cate_id in
          <foreach collection="rule.targetIds" item="item" open="(" separator="," close=")" index="index">
              #{item}
          </foreach>
      </if>
    ) tmp on tmp.id=cu.id
    where cu.log_off = 0 and tmp.id is null;
  </select>

  <select id="haveDealRuleCompute" resultType="java.lang.Long">
    select cu.id
    from customer_user cu
    inner join order_info oi on oi.user_id=cu.id and oi.channel='lpk' and oi.order_type in (0,1,2,3,4)
    where oi.parent_type in (1,2) and cu.log_off = 0
    and oi.create_time >= #{rule.timeStart}
    and oi.create_time <![CDATA[<=]]> #{rule.timeEnd}
    group by cu.id
  </select>

  <select id="gradeRuleCompute" resultType="java.lang.Long">
    select id
    from customer_user
    where log_off=0
    and grade in
    <foreach collection="rule.grades" item="item" open="(" separator="," close=")" index="index">
      #{item}
    </foreach>
  </select>

  <select id="shopBuyRuleCompute" resultType="java.lang.Long">
    select cu.id
    from customer_user cu
                 inner join order_info oi on oi.user_id=cu.id
    where oi.parent_type in (1,2) and cu.log_off = 0  and oi.channel= 'lpk' and oi.order_type in (0,1,2,3,4)
    and oi.pay_time >= #{rule.timeStart}
    and oi.pay_time <![CDATA[<=]]> #{rule.timeEnd}
    group by cu.id
  </select>

  <select id="shopNotBuyRuleCompute" resultType="java.lang.Long" timeout="900">
    select cu_out.id from
      customer_user cu_out
        left join(
        select cu.id
        from customer_user cu
               inner join order_info oi on oi.user_id=cu.id
        where oi.parent_type in (1,2) and cu.log_off = 0 and oi.channel= 'lpk' and oi.order_type in (0,1,2,3,4)
          and oi.pay_time >= #{rule.timeStart}
          and oi.pay_time <![CDATA[<=]]> #{rule.timeEnd}
      ) tmp on tmp.id = cu_out.id
    where tmp.id is null and cu_out.log_off = 0
  </select>

  <select id="realDealRuleCompute" resultType="java.lang.Long">
    select DISTINCT cu.id
    from customer_user cu
                 inner join order_info oi on oi.user_id=cu.id and oi.channel= 'lpk' and oi.order_type in (0,1,2,3,4)
    where oi.parent_type in (1,2) and cu.log_off = 0
    and oi.order_status not in (4, 0)
    and oi.order_refund_status in (0, 2)
      <if test="rule.timeStart !=null ">
          and oi.pay_time >= #{rule.timeStart}
      </if>
      <if test="rule.timeEnd !=null ">
          and oi.pay_time <![CDATA[<=]]> #{rule.timeEnd}
      </if>
    group by cu.id
    having 1=1
    <if test="rule.realDealMin !=null ">
      and count(1) >= #{rule.realDealMin}
    </if>
    <if test="rule.realDealMax !=null ">
      and count(1) <![CDATA[<=]]> #{rule.realDealMax}
    </if>
  </select>

  <select id="realAmountRuleCompute" resultType="java.lang.Long">
    select DISTINCT cu.id
    from customer_user cu
                 inner join order_info oi on oi.user_id=cu.id and oi.channel= 'lpk' and oi.order_type in (0,1,2,3,4)
    where oi.parent_type in (1,2) and cu.log_off = 0
    and oi.order_status not in (4, 0)
    and oi.order_refund_status in (0, 2)
    and oi.pay_time >= #{rule.timeStart}
    and oi.pay_time <![CDATA[<=]]> #{rule.timeEnd}
    group by cu.id
    having 1=1
    <if test="rule.realAmountMin !=null ">
      and sum(oi.real_amount) >= #{rule.realAmountMin}
    </if>
    <if test="rule.realAmountMax !=null ">
      and sum(oi.real_amount) <![CDATA[<=]]> #{rule.realAmountMax}
    </if>
  </select>

  <select id="orderAverageRuleCompute" resultType="java.lang.Long">
    select distinct cu.id
    from customer_user cu
                 inner join order_info oi on oi.user_id=cu.id and oi.channel= 'lpk' and oi.order_type in (0,1,2,3,4)
    where oi.parent_type in (1,2) and cu.log_off = 0
    and oi.order_status not in (4,0)
    group by cu.id
    having 1=1
    <if test="rule.orderAverageMin !=null ">
      and sum(real_amount)/count(*) >= #{rule.orderAverageMin}
    </if>
    <if test="rule.orderAverageMax !=null ">
      and sum(real_amount)/count(*) <![CDATA[<=]]> #{rule.orderAverageMax}
    </if>
  </select>

  <select id="buybackCycleRuleCompute" resultType="java.lang.Long">
    select distinct cu.id
    from customer_user cu
    inner join order_info oi on oi.user_id=cu.id and oi.channel= 'lpk' and oi.order_type in (0,1,2,3,4)
    where oi.parent_type in (1,2) and cu.log_off = 0
    and oi.order_status not in (4,0)
    group by cu.id
    having 1=1
    <if test="rule.buybackCycleMin !=null ">
      and (TIMESTAMPDIFF(MINUTE, min(oi.pay_time), max(oi.pay_time))/60/24 + 1)/ (count(*) -1) >= #{rule.buybackCycleMin}
    </if>
    <if test="rule.buybackCycleMax !=null ">
      and (TIMESTAMPDIFF(MINUTE, min(oi.pay_time), max(oi.pay_time))/60/24 + 1)/ (count(*) -1) <![CDATA[<=]]> #{rule.buybackCycleMax}
    </if>
  </select>

  <select id="shopVisitRuleCompute" resultType="java.lang.Long">
    select distinct cu.id
    from customer_user cu
                 inner join user_visit_detail uvd on uvd.user_id=cu.id
    where cu.log_off=0
    and uvd.visit_time >= #{rule.timeStart}
    and uvd.visit_time <![CDATA[<=]]> #{rule.timeEnd};
  </select>

  <select id="shopNotVisitRuleCompute" resultType="java.lang.Long" timeout="900">
    select distinct cu_out.id
    from customer_user cu_out
      left join (
      select cu.id
      from customer_user cu
                   inner join user_visit_detail uvd on uvd.user_id=cu.id
      where cu.log_off=0
      and uvd.visit_time >= #{rule.timeStart}
      and uvd.visit_time <![CDATA[<=]]> #{rule.timeEnd}
    ) cutmp on cutmp.id = cu_out.id
    where cutmp.id is null and cu_out.log_off=0;
  </select>

  <select id="shopSubmitRuleCompute" resultType="java.lang.Long">
    select distinct cu.id
    from customer_user cu
    inner join order_info oi on oi.user_id=cu.id and oi.channel= 'lpk' and oi.order_type in (0,1,2,3,4)
    where oi.parent_type in (1,2) and cu.log_off = 0
    and oi.pay_time >= #{rule.timeStart}
    and oi.pay_time <![CDATA[<=]]> #{rule.timeEnd}
  </select>

  <select id="shopNotSubmitRuleCompute" resultType="java.lang.Long" timeout="600">
    select distinct cu.id
    from customer_user cu
    left join (
        select cu.id
        from customer_user cu
                     inner join order_info oi on oi.user_id=cu.id and oi.channel= 'lpk' and oi.order_type in (0,1,2,3,4)
        where oi.parent_type in (1,2) and cu.log_off = 0  and cu.delflag = 0 and oi.delflag = 0
        and oi.pay_time >= #{rule.timeStart}
        and oi.pay_time <![CDATA[<=]]> #{rule.timeEnd}
    ) cutmp on cutmp.id=cu.id
    where cutmp.id is null and cu.log_off=0 and cu.delflag = 0
  </select>

  <select id="buyCardRuleCompute" resultType="java.lang.Long">
    <if test="rule.buyCard != null">
        select distinct cu.id
        from customer_user cu
        inner join customer_user_label cul on cul.user_id=cu.id
        where cu.log_off = 0
        and cul.is_buy_card=#{rule.buyCard}
    </if>
    <if test="rule.buyCard == null">
        select distinct c.user_id
        from card c
        left join card_category cc on cc.id= c.category_id
        inner join customer_user cu on cu.id=c.user_id
        where cu.log_off = 0
        and c.card_type in (0,1)
        and cc.id in
        <foreach collection="rule.cardCategoryIds" item="item" open="(" separator="," close=")" index="index">
            #{item}
        </foreach>
        and c.receive_time >= #{rule.timeStart}
        and c.receive_time <![CDATA[<=]]> #{rule.timeEnd}
    </if>
  </select>

  <select id="renewTimeRuleCompute" resultType="java.lang.Long">
    select cu.id
    from customer_user cu
                 inner join customer_user_label cul on cul.user_id=cu.id
    where cu.log_off = 0
    <if test="rule.renewTimeMin != null ">
      and cul.milk_renew_times >= #{rule.renewTimeMin}
    </if>
    <if test="rule.renewTimeMax != null ">
      and cul.milk_renew_times <![CDATA[<=]]> #{rule.renewTimeMax}
    </if>
  </select>

  <select id="buyCateRuleCompute" resultType="java.lang.Long">
    select cu.id
    from customer_user cu
                 inner join customer_user_label cul on cul.user_id=cu.id
    where cu.log_off = 0
    and cul.card_buy_cate = #{rule.buyCate}
  </select>

  <select id="bindCateRuleCompute" resultType="java.lang.Long">
    select cu.id
    from customer_user cu
                 inner join customer_user_label cul on cul.user_id=cu.id
    where cu.log_off = 0
    and cul.card_bind_cate = #{rule.bindCate}
  </select>

  <select id="firstBindRuleCompute" resultType="java.lang.Long">
      select temp.id from  (select cu.id, c.receive_time
      from customer_user cu
      inner join card c on c.user_id=cu.id
      where cu.log_off = 0
      GROUP BY c.user_id
      order by c.receive_time asc) temp
      where temp.receive_time <![CDATA[<=]]> #{rule.timeEnd}
      and temp.receive_time >= #{rule.timeStart}
  </select>

<!--  <select id="expireUserRuleCompute" resultType="java.lang.Long">
    select distinct cu1.id
    from (select cu.id, sum(c.remaining_count) as sumRemainingCount
          from customer_user cu
                       inner join card c on c.user_id=cu.id
          where c.card_status in (1,2) and c.card_type in (0,1) and c.activate_time is not null and cu.log_off=0
          group by cu.id
          having sumRemainingCount = 0) cu1
    inner join (
        select cu.id
        from customer_user cu
                     inner join card c on c.user_id=cu.id
        where c.card_status in (1,2) and c.card_type in (0,1)
        group by cu.id
        having count(case when c.activate_time is not null then 1 else null end) = count(*)
        ) cu2 on cu1.id=cu2.id;
  </select>-->

    <select id="expireUserRuleCompute" resultType="java.lang.Long">
        SELECT
        distinct cu.id
        FROM
        (
        SELECT
        c.user_id,
        count( 1 ) AS allCnt1
        FROM
        card c
        INNER JOIN (
        SELECT
        t.user_id,
        max( t.dispatch_date ) AS lastDispatchDate,
        sum( t.milk_amount ) AS cnt
        FROM
        card t1
        INNER JOIN milk_dispatch_plan t ON t1.card_number = t.card_number
        WHERE
        t.delflag = 0
        AND t1.card_type IN ( 0, 1 )
        AND t.plan_status IN ( 1, 2, 3, 6 )
        <if test="rule.cardCategoryIds != null and rule.cardCategoryIds.size() > 0">
            AND t1.category_id IN
            <foreach collection="rule.cardCategoryIds" item="item" open="(" separator="," close=")" index="index">
                #{item}
            </foreach>
        </if>
        GROUP BY
        t.user_id
        ) tt ON tt.user_id = c.user_id
        WHERE tt.lastDispatchDate is not null and c.activate_time is not null
        <if test="rule.timeEnd != null">
            AND c.activate_time <![CDATA[<=]]> #{rule.timeEnd}
        </if>
        <if test="rule.cardCategoryIds != null and rule.cardCategoryIds.size() > 0">
            AND c.category_id IN
            <foreach collection="rule.cardCategoryIds" item="item" open="(" separator="," close=")" index="index">
                #{item}
            </foreach>
        </if>
        <if test="rule.timeStart != null">
            AND tt.lastDispatchDate >= #{rule.timeStart}
        </if>
        <if test="rule.timeEnd != null">
            AND tt.lastDispatchDate <![CDATA[<=]]> #{rule.timeEnd}
        </if>
        GROUP BY
        c.user_id
        HAVING
        sum( c.card_count ) - sum( tt.cnt ) = 0
        ) cu1
        INNER JOIN (
            SELECT user_id, count( 1 ) AS allCnt2 FROM card
            WHERE card_type IN ( 0, 1 ) and receive_time is not null
            <if test="rule.timeEnd != null">
                AND receive_time <![CDATA[<=]]> #{rule.timeEnd}
            </if>
            <if test="rule.cardCategoryIds != null and rule.cardCategoryIds.size() > 0">
                AND category_id in
                <foreach collection="rule.cardCategoryIds" item="item" open="(" separator="," close=")" index="index">
                    #{item}
                </foreach>
            </if>
            GROUP BY user_id ) cu2 ON cu1.user_id = cu2.user_id
        INNER JOIN customer_user cu on cu.id = cu2.user_id
        WHERE cu1.allCnt1 = cu2.allCnt2 and cu.log_off = 0
    </select>

  <select id="expireUserNotRuleCompute" resultType="java.lang.Long">
    select distinct cu_out.id
    from customer_user cu_out
    inner join  customer_user_label cul on cul.user_id=cu_out.id
    left join (
      select distinct cu1.id
      from (select cu.id, sum(c.remaining_count) as sumRemainingCount
            from customer_user cu
                         inner join card c on c.user_id=cu.id
            where c.card_status in (1,2) and c.card_type in (0,1) and c.activate_time is not null and cu.log_off=0
            group by cu.id
            having sumRemainingCount = 0) cu1
                   inner join (
              select cu.id
              from customer_user cu
                           inner join card c on c.user_id=cu.id
              where c.card_status in (1,2) and c.card_type in (0,1)
              group by cu.id
              having count(case when c.activate_time is not null then 1 else null end) = count(*)
              ) cu2 on cu1.id=cu2.id
      )cuTmp on cu_out.id=cuTmp.id
    where cuTmp.id is null and cu_out.log_off=0 and cul.milk_renew_times >=0;
  </select>

  <select id="renewUserRuleCompute" resultType="java.lang.Long">
    select cu.id
    from customer_user cu
                 inner join customer_user_label cul on cul.user_id=cu.id
                 inner join (
            select cu.id, sum(c.remaining_count) as sumRemainingCount
            from customer_user cu
                         inner join card c on c.user_id=cu.id
            where c.card_status in (1,2) and cu.log_off=0
            <if test="rule.timeStart != null">
              and c.receive_time <![CDATA[>=]]> #{rule.timeStart}
            </if>
          <if test="rule.timeEnd != null">
              and c.receive_time <![CDATA[<=]]> #{rule.timeEnd}
          </if>
            group by cu.id
            having sumRemainingCount > 0
            ) cuTmp on cuTmp.id = cu.id
    where cu.log_off = 0
    and cul.milk_renew_times > 0;
  </select>

  <select id="renewUserNotRuleCompute" resultType="java.lang.Long">
    select *
    from customer_user cu_out
    inner join customer_user_label cul on cul.user_id=cu_out.id
    left join (
        select cu.id
        from customer_user cu
                     inner join customer_user_label cul on cul.user_id=cu.id
                     inner join (
                select cu.id, sum(c.remaining_count) as sumRemainingCount
                from customer_user cu
                             inner join card c on c.user_id=cu.id
                where c.card_status in (1,2)and cu.log_off=0
                group by cu.id
                having sumRemainingCount > 0
                ) cuTmp on cuTmp.id = cu.id
        where cu.log_off = 0
          and cul.milk_renew_times > 0
        ) cuTmp on cu_out.id=cuTmp.id
    where cuTmp.id is null and cu_out.log_off=0 and cul.milk_renew_times >=0;
  </select>

  <select id="cardWillExpireRuleCompute" resultType="java.lang.Long">
    select distinct cu.id
    from customer_user cu
                 inner join card c on c.user_id=cu.id
                 inner join card_category cc on cc.id=c.category_id
                 inner join milk_dispatch_plan plan on plan.card_number = c.card_number
                 inner join (
            select id,card_number, max(dispatch_date) as dispatch_date, sum(case when plan_status=1 then milk_amount else 0 END) as sumMilkAmount
            from milk_dispatch_plan
            where delflag =0 and plan_status not in (7,8,9)
            group by card_number
            ) planTmp on planTmp.card_number=plan.card_number
    where plan.dispatch_date = planTmp.dispatch_date
    and cc.id in
      <foreach collection="rule.cardCategoryIds" item="categoryId" open="(" separator="," close=")">
          #{categoryId}
      </foreach>
    and c.card_status IN (1, 2) and c.card_type in (0, 1) and cu.log_off=0
    and planTmp.dispatch_date >= #{rule.timeStart}
    and planTmp.dispatch_date <![CDATA[<=]]> #{rule.timeEnd}
    and plan.delflag =0 and plan.plan_status not in (7,8,9)
    and c.remaining_count - ifnull(planTmp.sumMilkAmount, 0) = 0;
  </select>

  <select id="recentNotDispatchRuleCompute" resultType="java.lang.Long">
    select cu.id
    from customer_user cu
                 inner join card c on c.user_id=cu.id
                 left join (
            select cum.id
            from customer_user cum
                         inner join card c on c.user_id=cum.id
                         inner join milk_dispatch_plan mdp on mdp.card_number=c.card_number
            where c.card_status in (1,2) and c.card_type in (0, 1) and cum.log_off=0 and c.remaining_count >0
            and mdp.dispatch_date >= #{rule.timeStart}
            and mdp.dispatch_date <![CDATA[<=]]> #{rule.timeEnd}
    ) temp on temp.id=cu.id
    where c.card_status in (1,2) and c.card_type in (0, 1) and cu.log_off=0 and c.remaining_count >0
    and temp.id is null;
  </select>
    <select id="recentAddressRuleCompute" resultType="java.lang.Long" timeout="600">
        select distinct cu.id
        from customer_user cu
        inner join (
        select t.* from (SELECT
        oi.*
        FROM
        order_info oi
        WHERE
        oi.pay_time >= #{rule.timeStart}
        AND oi.pay_time <![CDATA[<=]]> #{rule.timeEnd}
        and oi.order_status = 3
        and oi.channel = 'lpk' and oi.parent_type in(1,2) and oi.order_type in (0,1,2,3,4)
        order by oi.confirm_time desc)t group by t.user_id)tt on tt.user_id = cu.id
        inner join order_address oa on oa.order_no = tt.order_no
        and cu.log_off = 0
        and
        <foreach collection="rule.addresses" item="item" open="(" separator=" or " close=")" index="index">
            (
            <if test="item.province != null and item.province!='' ">
             oa.province = #{item.province}
            </if>
            <if test="item.city != null and item.city!='' ">
                and oa.city = #{item.city}
            </if>
            <if test="item.district != null and item.district!='' ">
                and oa.district = #{item.district}
            </if>
                )
        </foreach>
    </select>
<!--  <select id="recentAddressRuleCompute" resultType="java.lang.Long" timeout="900">
    select distinct cu.id
    from customer_user cu
    inner join (
    select oi.user_id, max(oi.create_time) as maxTime, oa.province, oa.city, oa.district
    from order_info oi
    inner join order_address oa on oa.order_no=oi.order_no
    where oi.order_status = 3 and oi.channel= 'lpk' and oi.order_type in (0,1,2,3,4)
    group by oi.user_id
    union
    select user_id, max(dispatch_date) as maxTime, province, city, district
    from milk_dispatch_plan
    where plan_status = 3
    group by user_id) tmp on tmp.user_id=cu.id
    where tmp.maxTime in (
    select max(maxTime)
    from (
    select oi.user_id, max(oi.create_time) as maxTime, oa.province, oa.city, oa.district
    from order_info oi
    inner join order_address oa on oa.order_no=oi.order_no
    where oi.order_status = 3 and oi.channel= 'lpk' and oi.order_type in (0,1,2,3,4)
    group by oi.user_id
    union
    select user_id, max(dispatch_date) as maxTime, province, city, district
    from milk_dispatch_plan
    where plan_status = 3
    group by user_id) tmp_in
    group by tmp_in.user_id
    )
    and cu.log_off=0
    and
    <foreach collection="rule.addresses" item="item" open="(" separator=" or " close=")" index="index">
      (<if test="item.province != null and item.province!='' ">
      tmp.province = #{item.province}
      </if>
      <if test="item.city != null and item.city!='' ">
        and tmp.city = #{item.city}
      </if>
      <if test="item.district != null and item.district!='' ">
        and tmp.district = #{item.district}
      </if>)
    </foreach>
  </select>-->

  <select id="recentCardPlatformRuleCompute" resultType="java.lang.Long">
    select cu.id
    from customer_user cu
                 inner join order_info oi on oi.user_id=cu.id and oi.channel= 'lpk'
    where cu.log_off = 0 and oi.create_time in (
            select max(create_time)
            from order_info
            where order_refund_status in (0, 2) and order_status not in (4, 0)
            and order_type=1
            group by user_id
            )
    and oi.order_refund_status in (0, 2) and oi.order_status not in (4, 0)
    and oi.order_type=1
    and oi.platform in
    <foreach collection="rule.platforms" item="item" open="(" separator="," close=")" index="index">
      #{item}
    </foreach>
  </select>

  <select id="mostPlatformRuleCompute" resultType="java.lang.Long">
    select cu.id
    from customer_user cu
                 inner join order_info oi on oi.user_id=cu.id and oi.channel= 'lpk'
    where
    oi.order_status not in (4, 0) and cu.log_off = 0
    and oi.order_refund_status in (0, 2)
    and oi.order_type=1
    and oi.platform in
    <foreach collection="rule.platforms" item="item" open="(" separator="," close=")" index="index">
      #{item}
    </foreach>
  </select>

  <select id="autoLabelRuleCompute" resultType="java.lang.Long">
    select distinct user_id
    from people
    where delflag=0
    and label_id in
    <foreach collection="rule.labelIds" item="item" open="(" separator="," close=")" index="index">
      #{item}
    </foreach>
  </select>

  <select id="autoLabelAndRuleCompute" resultType="java.lang.Long">
    select user_id
    from people
    where delflag=0
    and label_id in
    <foreach collection="rule.labelIds" item="item" open="(" separator="," close=")" index="index">
      #{item}
    </foreach>
    group by user_id
    having count(*)>= #{rule.size};
  </select>

  <select id="wechatActivityUserRuleCompute" resultType="java.lang.Long">
    select distinct cu.id
    from customer_user cu
                 inner join mini_message_subscribe mms on mms.user_id = cu.id
    where mms.template_id=#{rule.templateId} and mms.send_flag=2
      and cu.log_off=0;
  </select>

  <select id="wechatActivityUserNotRuleCompute" resultType="java.lang.Long">
    select distinct cu_out.id
    from customer_user cu_out
                 left join (
            select cu.id
            from customer_user cu
                         inner join mini_message_subscribe mms on mms.user_id = cu.id
            where 1=1
              and mms.template_id=#{rule.templateId} and mms.send_flag=2
              and cu.log_off=0
            ) cuTmp on cuTmp.id=cu_out.id
    where cuTmp.id is null and cu_out.log_off=0;
  </select>

  <select id="manualFullReduceRuleCompute" resultType="java.lang.Long">
    select distinct cu.id
    from customer_user cu
        inner join order_info oi on oi.user_id=cu.id
    where oi.parent_type in (1,3) and cu.log_off = 0
    # 下单就算，不剔除售后
    and oi.order_status not in (4, 0)
    and oi.full_reduce_id = #{rule.fullReduceId}
  </select>

    <select id="manualPtActivityRuleCompute" resultType="java.lang.Long">
        select distinct cu.id
        from customer_user cu
                 inner join pt_sub_order pso on pso.user_id = cu.id
        where cu.log_off = 0
    and pso.pt_activity_id = #{rule.ptActivityId}
    and pso.pt_sub_order_status in ('paid','cancel')
    </select>

    <select id="joinFreeTrialRuleCompute" resultType="java.lang.Long">
        select distinct cu.id
        from customer_user cu
                 inner join free_trial_user ftu on ftu.user_id = cu.id
        where  cu.log_off = 0 and ftu.delflag = 0
        <if test="rule.timeStart != null">
            and ftu.apply_time >= #{rule.timeStart}
        </if>
        <if test="rule.timeEnd != null">
            and ftu.apply_time <![CDATA[<=]]> #{rule.timeEnd}
        </if>
    </select>

    <select id="joinDiscountRuleCompute" resultType="java.lang.Long">
        select distinct cu.id
        from customer_user cu
                 inner join order_info oi on oi.user_id = cu.id
        where  cu.log_off = 0 and oi.delflag = 0 and oi.channel= 'lpk' and oi.parent_type in(1,3) and oi.order_type in (0,1,2,3,4) and oi.order_status not in(0,4)
        <if test="rule.timeStart != null">
            and oi.pay_time >= #{rule.timeStart}
        </if>
        <if test="rule.timeEnd != null">
            and oi.pay_time <![CDATA[<=]]> #{rule.timeEnd}
        </if>
          and oi.discount_activity_id is not null
    </select>

    <select id="joinPointMallRuleCompute" resultType="java.lang.Long">
        select distinct cu.id
        from customer_user cu
                 inner join swap_order so on so.user_id = cu.id
        where  cu.log_off = 0 and so.delflag = 0 and so.channel= 2
        <if test="rule.timeStart != null">
            and so.pay_time >= #{rule.timeStart}
        </if>
        <if test="rule.timeEnd != null">
            and so.pay_time <![CDATA[<=]]> #{rule.timeEnd}
        </if>
    </select>

    <select id="joinLuckyDrawRuleCompute" resultType="java.lang.Long">
        select distinct cu.id
        from customer_user cu
                 inner join lucky_record lr on lr.user_id = cu.id
        where  cu.log_off = 0 and lr.delflag = 0
        <if test="rule.timeStart !=null ">
            and lr.prize_date >= #{rule.timeStart}
        </if>
        <if test="rule.timeEnd !=null ">
            and lr.prize_date <![CDATA[<=]]> #{rule.timeEnd}
        </if>
    </select>

    <select id="joinCouponRuleCompute" resultType="java.lang.Long">
        select distinct cu.id
        from customer_user cu
                 inner join order_info oi on oi.user_id = cu.id
        where  cu.log_off = 0 and oi.delflag = 0 and oi.channel= 'lpk' and oi.order_type in (0,1,2,3,4)
          and (oi.use_coupon_status = 1 or oi.use_coupon_gift_status= 1) and oi.order_status not in(0,4)
        <if test="rule.timeStart !=null ">
            and oi.pay_time >= #{rule.timeStart}
        </if>
        <if test="rule.timeEnd !=null ">
            and oi.pay_time <![CDATA[<=]]> #{rule.timeEnd}
        </if>
    </select>

    <select id="joinFullReduceRuleCompute" resultType="java.lang.Long">
        select distinct cu.id
        from customer_user cu
                 inner join order_info oi on oi.user_id = cu.id
        where  cu.log_off = 0 and oi.delflag = 0 and oi.channel= 'lpk' and oi.order_type in (0,1,2,3,4) and oi.parent_type in(1,3) and oi.order_status not in(0,4)
        <if test="rule.timeStart !=null ">
            and oi.pay_time >= #{rule.timeStart}
        </if>
        <if test="rule.timeEnd !=null ">
            and oi.pay_time <![CDATA[<=]]> #{rule.timeEnd}
        </if>
          and oi.full_reduce_id is not null
    </select>

    <select id="joinFreeTasteRuleCompute" resultType="java.lang.Long">
        select distinct cu.id
        from customer_user cu
                 inner join order_info oi on oi.user_id = cu.id
        where  cu.log_off = 0 and oi.delflag = 0 and oi.channel= 'lpk' and oi.order_type in (0,1,2,3,4) and oi.parent_type in(1,3) and oi.order_status not in(0,4)
        <if test="rule.timeStart !=null ">
            and oi.pay_time >= #{rule.timeStart}
        </if>
        <if test="rule.timeEnd !=null ">
            and oi.pay_time <![CDATA[<=]]> #{rule.timeEnd}
        </if>
          and oi.free_taste_id is not null
    </select>

    <select id="joinFullTrialRuleCompute" resultType="java.lang.Long">
        select distinct cu.id
        from customer_user cu
                 inner join order_info oi on oi.user_id = cu.id
        where  cu.log_off = 0 and oi.delflag = 0 and oi.channel= 'lpk' and oi.order_type in (0,1,2,3,4) and oi.parent_type in(1,3) and oi.order_status not in(0,4)
        <if test="rule.timeStart !=null ">
            and oi.pay_time >= #{rule.timeStart}
        </if>
        <if test="rule.timeEnd !=null ">
            and oi.pay_time <![CDATA[<=]]> #{rule.timeEnd}
        </if>
          and oi.full_trial_id is not null
    </select>
    <select id="bindBuyCardRuleCompute" resultType="java.lang.Long">
        select distinct c.user_id
        from card c
        inner join customer_user cu on cu.id=c.user_id
        where cu.log_off = 0
        and c.card_type in (0,1)
        and c.receive_time >= #{rule.bindTimeStart}
        and c.receive_time <![CDATA[<=]]> #{rule.bindTimeEnd}
        and c.receive_time is not null
        and exists(
            select 1 from order_info oi
                     inner join order_sku_card osc on oi.order_no=osc.order_no
                     inner join card ca on ca.card_number=osc.card_number
                     inner join card_category cc on cc.id=ca.category_id
            where oi.delflag = 0
            and cc.id in
            <foreach collection="rule.cardCategoryIds" item="item" open="(" separator="," close=")" index="index">
                #{item}
            </foreach>
            and oi.user_id = c.user_id
            and oi.order_type = 1
            and oi.channel='lpk'
            and oi.pay_time is not null
            and oi.pay_time >= #{rule.timeStart}
            and oi.pay_time <![CDATA[<=]]> #{rule.timeEnd}
        )
    </select>

    <select id="joinPtRuleCompute" resultType="java.lang.Long">
        select distinct cu.id
        from customer_user cu
                 inner join pt_sub_order pso on pso.user_id = cu.id
                 inner join order_info oi on oi.order_no = pso.order_no
        where  cu.log_off = 0 and pso.delflag = 0 and oi.delflag = 0 and oi.channel= 'lpk' and oi.order_type in (0,1,2,3,4) and oi.order_status not in(0,4)
        <if test="rule.timeStart !=null ">
            and oi.pay_time >= #{rule.timeStart}
        </if>
        <if test="rule.timeEnd !=null ">
            and oi.pay_time <![CDATA[<=]]> #{rule.timeEnd}
        </if>
    </select>
    <select id="findAllUserIdByUserId" resultType="java.lang.Long">
    SELECT
      distinct cu.id
    FROM
      customer_user cu
    WHERE
      cu.phone IN ( SELECT c.phone FROM customer_user c WHERE c.id = #{userId} );
  </select>
  <select id="findMinBuyDate" resultType="com.hengtiansoft.user.entity.dto.UserBuyDateDTO">
    select cu.id, min(oi.create_time) as minBuyDate  from customer_user cu
        inner join order_info oi on oi.user_id=cu.id
    where oi.parent_type in (1,2) and oi.channel='lpk'
    <if test="ids != null and ids.size > 0">
      and cu.id in
        <foreach collection="ids" item="item" open="(" separator="," close=")" index="index">
            #{item}
        </foreach>
    </if>
    group by cu.id
  </select>
  <select id="findOrderInfo" resultType="com.hengtiansoft.user.entity.vo.CustomerUserMoreVO">
    select cu.id as userId, sum(oi.real_amount) as totalAmount, count(*) as orderCnt, sum(oi.real_amount)/count(*) as averageUser, max(oi.pay_time) as lastPayTime
    from customer_user cu
    inner join order_info oi on oi.user_id = cu.id
    where oi.channel='lpk' and oi.order_status not in (0, 4) and oi.parent_type in (1,2 ) and oi.order_type in (0,1,2,3,4)
      and cu.id in
      <foreach collection="ids" item="item" open="(" separator="," close=")" index="index">
          #{item}
      </foreach>
    group by cu.id;
  </select>
  <select id="findOrderTime" resultType="com.hengtiansoft.user.entity.vo.CustomerUserMoreVO">
    select cu.id as userId, max(oi.create_time) as lastOrderTime
    from customer_user cu
           inner join order_info oi on oi.user_id = cu.id
    where oi.channel='lpk' and oi.parent_type in (1,2 ) and oi.order_type in (0,1,2,3,4)
      and cu.id in
      <foreach collection="ids" item="item" open="(" separator="," close=")" index="index">
        #{item}
      </foreach>
    group by cu.id;
  </select>

  <select id="findNullMD5OpenId" resultType="com.hengtiansoft.user.entity.po.CustomerUser">
    select * from customer_user cu where cu.open_id is not null and cu.open_id != '' and (cu.md5_open_id is null or cu.md5_open_id = '')
  </select>
  <select id="findOrderInfo90" resultType="com.hengtiansoft.user.entity.vo.CustomerUserMoreVO">
    select cu.id as userId, sum(oi.real_amount) as totalAmount, count(*) as orderCnt, sum(oi.real_amount)/count(*) as averageUser, max(oi.pay_time) as lastPayTime
    from customer_user cu
    inner join order_info oi on oi.user_id = cu.id
    where oi.channel='lpk' and oi.order_status not in (0, 4) and oi.parent_type in (1,2 ) and oi.order_type in (0,1,2,3,4)
    and oi.create_time >= DATE_SUB(NOW(), INTERVAL 90 DAY)
    and cu.id in
    <foreach collection="ids" item="item" open="(" separator="," close=")" index="index">
      #{item}
    </foreach>
    group by cu.id;
  </select>
  <select id="findUserCnt" resultType="com.hengtiansoft.user.entity.vo.UserTypeCntVO">
    SELECT
      cu.activity_id as activityId,
      count(1) as cnt
    FROM
      customer_user cu
    WHERE cu.delflag = 0
    AND cu.user_type = #{userType}
    group by cu.activity_id
  </select>
  <select id="findUserIdGroupByPhone" resultType="com.hengtiansoft.user.entity.dto.CustomerUserByPhoneDTO">
      SELECT
          phone,
          GROUP_CONCAT( DISTINCT id SEPARATOR ',' ) as userIds,
          MIN(first_login_time) as firstLoginTime
      FROM
          customer_user cu
      WHERE
          cu.delflag = 0
          and cu.phone is not null and cu.phone !=''
      <if test="phone != null and phone !=''">
          and cu.phone=#{phone}
      </if>
      GROUP BY
          cu.phone
  </select>
    <select id="fansRuleCompute" resultType="java.lang.Long">
        SELECT DISTINCT
            cu.id
        FROM
            customer_user cu
                LEFT JOIN customer_weixin_user cwu ON cwu.subscribe = 1 AND cwu.delflag = 0 AND cwu.union_id = cu.union_id
        WHERE
            cu.log_off = 0
        <if test="rule.isFans == 0 ">
            and cwu.id IS NULL
        </if>
        <if test="rule.isFans == 1 ">
            and cwu.id IS NOT NULL
        </if>
    </select>
    <select id="birthdayUserCompute" resultType="java.lang.Long">
        select distinct cu.id
        from customer_user cu
        where cu.birth_day is not null
          and cu.log_off = 0
          <if test="rule.month != null">
          and month(cu.birth_day) = #{rule.month}
          </if>
          <if test="rule.timeStart != null">
              and cu.birth_day >= #{rule.timeStart}
              and cu.birth_day <![CDATA[<=]]> #{rule.timeEnd}
          </if>
    </select>
    <select id="miniProgramRegisterTimeRuleCompute" resultType="java.lang.Long">
        select distinct cu.id
        from customer_user cu
        where cu.first_login_time is not null
          and cu.log_off = 0
          and cu.first_login_time >= #{rule.timeStart}
          and cu.first_login_time <![CDATA[<=]]> #{rule.timeEnd}
    </select>
    <select id="joinMemberTimeRuleCompute" resultType="java.lang.Long">
        select distinct cu.id
        from customer_user cu
        where cu.join_time is not null
          and cu.log_off = 0
          and cu.join_time >= #{rule.timeStart}
          and cu.join_time <![CDATA[<=]]> #{rule.timeEnd}
    </select>
    <select id="couponNotUsedRuleCompute" resultType="java.lang.Long">
        SELECT DISTINCT
            cu.id
        FROM
            customer_user cu
                INNER JOIN coupon_info ci ON cu.id = ci.user_id
        WHERE
            ci.delflag = 0
          AND ci.log_off = 0
          AND ci.status in(1, 3)
          AND cu.log_off = 0
        <if test="rule.ruleIds != null and rule.ruleIds.size() > 0">
            AND ci.rule_id in
            <foreach collection="rule.ruleIds" item="item" open="(" separator="," close=")" index="index">
                #{item}
            </foreach>
        </if>
    </select>
    <select id="dispatchSignRuleCompute" resultType="java.lang.Long">
        select DISTINCT user_id
        from milk_dispatch_plan plan
        LEFT JOIN customer_user cu on plan.user_id = cu.id
        where plan.delflag = 0 and cu.delflag = 0 and cu.log_off = 0
        and plan.confirm_time is not null
        and plan.confirm_time >= #{rule.timeStart} and plan.confirm_time <![CDATA[<=]]> #{rule.timeEnd}
    </select>
    <select id="recentPayRuleCompute" resultType="java.lang.Long">
        select distinct cu.id
        from customer_user cu
                 inner join order_info oi on oi.user_id = cu.id
        where  cu.log_off = 0 and oi.delflag = 0 and oi.channel= 'lpk' and oi.order_type in (0,1,2,3,4) and oi.parent_type in(1,2)
          and oi.pay_time >= #{rule.timeStart}
          and oi.pay_time <![CDATA[<=]]> #{rule.timeEnd}
    </select>
    <select id="payAmountRuleCompute" resultType="java.lang.Long" timeout="900">
        select cu.id
        from customer_user cu
        inner join order_info oi on oi.user_id=cu.id and oi.channel='lpk' and oi.order_type in (0,1,2,3,4)
        where oi.parent_type in (1,2) and cu.log_off = 0 and oi.order_status not in(0,4)
        <if test="rule.timeStart !=null ">
            and oi.pay_time >= #{rule.timeStart}
        </if>
        <if test="rule.timeEnd !=null ">
            and oi.pay_time <![CDATA[<=]]> #{rule.timeEnd}
        </if>
        group by cu.id
        having 1=1
        <if test="rule.payAmountMin !=null ">
            and sum(oi.real_amount) >= #{rule.payAmountMin}
        </if>
        <if test="rule.payAmountMax !=null ">
            and sum(oi.real_amount) <![CDATA[<=]]> #{rule.payAmountMax}
        </if>
    </select>
    <select id="bindNotBuyOfProductIdRuleCompute" resultType="java.lang.Long" timeout="900">
        SELECT DISTINCT c.user_id
        FROM card c
        INNER JOIN customer_user cu ON cu.id = c.user_id
        WHERE
        cu.delflag = 0
        AND cu.log_off = 0
        <if test="rule.bindTimeStart !=null ">
            AND c.receive_time >= #{rule.bindTimeStart}
        </if>
        <if test="rule.bindTimeEnd !=null ">
            AND c.receive_time <![CDATA[<=]]> #{rule.bindTimeEnd}
        </if>
        <if test="rule.cardCategoryIds != null and rule.cardCategoryIds.size() > 0">
            and c.category_id in
            <foreach collection="rule.cardCategoryIds" item="item" open="(" separator="," close=")" index="index">
                #{item}
            </foreach>
        </if>
        AND c.receive_time IS NOT NULL
        AND NOT EXISTS (
        SELECT 1
        FROM order_info oi
        inner join order_sku os on os.order_no=oi.order_no
        WHERE
        oi.delflag = 0
        AND oi.channel='lpk'
        and oi.order_type in (0,1,2,3,4)
        and oi.parent_type in(1,3)
        and oi.order_status not in (4,0)
        AND oi.user_id = c.user_id
        <if test="rule.timeStart !=null ">
            AND oi.pay_time >= #{rule.timeStart}
        </if>
        <if test="rule.timeEnd !=null ">
            AND oi.pay_time <![CDATA[<=]]> #{rule.timeEnd}
        </if>
        <if test="rule.targetIds != null and rule.targetIds.size() > 0">
            and os.product_id in
            <foreach collection="rule.targetIds" item="item" open="(" separator="," close=")" index="index">
                #{item}
            </foreach>
        </if>
        )
    </select>
    <select id="bindNotBuyOfGroupIdRuleCompute" resultType="java.lang.Long" timeout="900">
        SELECT DISTINCT c.user_id
        FROM card c
        INNER JOIN customer_user cu ON cu.id = c.user_id
        WHERE
        cu.delflag = 0
        AND cu.log_off = 0
        <if test="rule.bindTimeStart !=null ">
            AND c.receive_time >= #{rule.bindTimeStart}
        </if>
        <if test="rule.bindTimeEnd !=null ">
            AND c.receive_time <![CDATA[<=]]> #{rule.bindTimeEnd}
        </if>
        <if test="rule.cardCategoryIds != null and rule.cardCategoryIds.size() > 0">
            and c.category_id in
            <foreach collection="rule.cardCategoryIds" item="item" open="(" separator="," close=")" index="index">
                #{item}
            </foreach>
        </if>
        AND c.receive_time IS NOT NULL
        AND NOT EXISTS (
        SELECT 1
        FROM order_info oi
        inner join order_sku os on os.order_no=oi.order_no
        inner join item_product_group ipg on ipg.product_id = os.product_id
        WHERE
        oi.delflag = 0
        AND oi.channel='lpk'
        and oi.order_type in (0,1,2,3,4)
        and oi.parent_type in(1,3)
        and oi.order_status not in (4,0)
        AND oi.user_id = c.user_id
        <if test="rule.timeStart !=null ">
            AND oi.pay_time >= #{rule.timeStart}
        </if>
        <if test="rule.timeEnd !=null ">
            AND oi.pay_time <![CDATA[<=]]> #{rule.timeEnd}
        </if>
        <if test="rule.targetIds != null and rule.targetIds.size() > 0">
            and ipg.group_id in
            <foreach collection="rule.targetIds" item="item" open="(" separator="," close=")" index="index">
                #{item}
            </foreach>
        </if>
        )
    </select>
    <select id="bindNotBuyOfCategoryIdRuleCompute" resultType="java.lang.Long" timeout="900">
        SELECT DISTINCT c.user_id
        FROM card c
        INNER JOIN customer_user cu ON cu.id = c.user_id
        WHERE
        cu.delflag = 0
        AND cu.log_off = 0
        <if test="rule.bindTimeStart !=null ">
            AND c.receive_time >= #{rule.bindTimeStart}
        </if>
        <if test="rule.bindTimeEnd !=null ">
            AND c.receive_time <![CDATA[<=]]> #{rule.bindTimeEnd}
        </if>
        <if test="rule.cardCategoryIds != null and rule.cardCategoryIds.size() > 0">
            and c.category_id in
            <foreach collection="rule.cardCategoryIds" item="item" open="(" separator="," close=")" index="index">
                #{item}
            </foreach>
        </if>
        AND c.receive_time IS NOT NULL
        AND NOT EXISTS (
        SELECT 1
        FROM order_info oi
        inner join order_sku os on os.order_no=oi.order_no
        inner join item_product ip on ip.id = os.product_id
        WHERE
        oi.delflag = 0
        AND oi.channel='lpk'
        and oi.order_type in (0,1,2,3,4)
        and oi.parent_type in(1,3)
        and oi.order_status not in (4,0)
        AND oi.user_id = c.user_id
        <if test="rule.timeStart !=null ">
            AND oi.pay_time >= #{rule.timeStart}
        </if>
        <if test="rule.timeEnd !=null ">
            AND oi.pay_time <![CDATA[<=]]> #{rule.timeEnd}
        </if>
        <if test="rule.targetIds != null and rule.targetIds.size() > 0">
            and ip.cate_id in
            <foreach collection="rule.targetIds" item="item" open="(" separator="," close=")" index="index">
                #{item}
            </foreach>
        </if>
        )
    </select>
    <select id="bindNotBuyOfCardCategoryIdRuleCompute" resultType="java.lang.Long" timeout="900">
        SELECT DISTINCT c.user_id
        FROM card c
        INNER JOIN customer_user cu ON cu.id = c.user_id
        WHERE
        cu.delflag = 0
        AND cu.log_off = 0
        <if test="rule.bindTimeStart !=null ">
            AND c.receive_time >= #{rule.bindTimeStart}
        </if>
        <if test="rule.bindTimeEnd !=null ">
            AND c.receive_time <![CDATA[<=]]> #{rule.bindTimeEnd}
        </if>
        <if test="rule.cardCategoryIds != null and rule.cardCategoryIds.size() > 0">
            and c.category_id in
            <foreach collection="rule.cardCategoryIds" item="item" open="(" separator="," close=")" index="index">
                #{item}
            </foreach>
        </if>
        AND c.receive_time IS NOT NULL
        AND NOT EXISTS (
        SELECT 1
        FROM order_info oi
        inner join order_sku os on os.order_no=oi.order_no
        inner join order_sku_card osc on osc.order_sku_id = os.id
        inner join card cc on cc.card_number = osc.card_number
        WHERE
        oi.delflag = 0
        AND oi.channel='lpk'
        and oi.order_type in (0,1,2,3,4)
        and oi.parent_type in(1,3)
        and oi.order_status not in (4,0)
        AND oi.user_id = c.user_id
        <if test="rule.timeStart !=null ">
            AND oi.pay_time >= #{rule.timeStart}
        </if>
        <if test="rule.timeEnd !=null ">
            AND oi.pay_time <![CDATA[<=]]> #{rule.timeEnd}
        </if>
        <if test="rule.targetIds != null and rule.targetIds.size() > 0">
            and cc.category_id in
            <foreach collection="rule.targetIds" item="item" open="(" separator="," close=")" index="index">
                #{item}
            </foreach>
        </if>
        )
    </select>
    <select id="bindBuyOfProductIdRuleCompute" resultType="java.lang.Long" timeout="900">
        SELECT DISTINCT c.user_id
        FROM card c
        INNER JOIN customer_user cu ON cu.id = c.user_id
        WHERE
        cu.delflag = 0
        AND cu.log_off = 0
        <if test="rule.bindTimeStart !=null ">
            AND c.receive_time >= #{rule.bindTimeStart}
        </if>
        <if test="rule.bindTimeEnd !=null ">
            AND c.receive_time <![CDATA[<=]]> #{rule.bindTimeEnd}
        </if>
        <if test="rule.cardCategoryIds != null and rule.cardCategoryIds.size() > 0">
            AND c.category_id in
            <foreach collection="rule.cardCategoryIds" item="item" open="(" separator="," close=")" index="index">
                #{item}
            </foreach>
        </if>
        AND c.receive_time IS NOT NULL
        AND EXISTS (
        SELECT 1
        FROM order_info oi
        inner join order_sku os on os.order_no=oi.order_no
        WHERE
        oi.delflag = 0
        AND oi.user_id = c.user_id
        AND oi.pay_time IS NOT NULL
        AND oi.channel='lpk'
        and oi.order_type in (0,1,2,3,4)
        and oi.parent_type in(1,3)
        and oi.order_status not in (4,0)
        <if test="rule.timeStart !=null ">
            AND oi.pay_time >= #{rule.timeStart}
        </if>
        <if test="rule.timeEnd !=null ">
            AND oi.pay_time <![CDATA[<=]]> #{rule.timeEnd}
        </if>
        <if test="rule.targetIds != null and rule.targetIds.size() > 0">
            and os.product_id in
            <foreach collection="rule.targetIds" item="item" open="(" separator="," close=")" index="index">
                #{item}
            </foreach>
        </if>
        group by oi.user_id
        having 1=1
        <if test="rule.buyCntMin !=null ">
            and count(oi.id) >= #{rule.buyCntMin}
        </if>
        <if test="rule.buyCntMax !=null ">
            and count(oi.id) <![CDATA[<=]]> #{rule.buyCntMax}
        </if>
        <if test="rule.realAmountMin !=null ">
            and sum(oi.real_amount) >= #{rule.realAmountMin}
        </if>
        <if test="rule.realAmountMax !=null ">
            and sum(oi.real_amount) <![CDATA[<=]]> #{rule.realAmountMax}
        </if>
        )
    </select>
    <select id="bindBuyOfGroupIdRuleCompute" resultType="java.lang.Long" timeout="900">
        SELECT DISTINCT c.user_id
        FROM card c
        INNER JOIN customer_user cu ON cu.id = c.user_id
        WHERE
        cu.delflag = 0
        AND cu.log_off = 0
        <if test="rule.bindTimeStart !=null ">
            AND c.receive_time >= #{rule.bindTimeStart}
        </if>
        <if test="rule.bindTimeEnd !=null ">
            AND c.receive_time <![CDATA[<=]]> #{rule.bindTimeEnd}
        </if>
        <if test="rule.cardCategoryIds != null and rule.cardCategoryIds.size() > 0">
            AND c.category_id in
            <foreach collection="rule.cardCategoryIds" item="item" open="(" separator="," close=")" index="index">
                #{item}
            </foreach>
        </if>
        AND c.receive_time IS NOT NULL
        AND EXISTS (
        SELECT 1
        FROM order_info oi
        inner join order_sku os on os.order_no=oi.order_no
        inner join item_product_group ipg on ipg.product_id = os.product_id
        WHERE
        oi.delflag = 0
        AND oi.user_id = c.user_id
        AND oi.pay_time IS NOT NULL
        AND oi.channel='lpk'
        and oi.order_type in (0,1,2,3,4)
        and oi.parent_type in(1,3)
        and oi.order_status not in (4,0)
        <if test="rule.timeStart !=null ">
            AND oi.pay_time >= #{rule.timeStart}
        </if>
        <if test="rule.timeEnd !=null ">
            AND oi.pay_time <![CDATA[<=]]> #{rule.timeEnd}
        </if>
        <if test="rule.targetIds != null and rule.targetIds.size() > 0">
            and ipg.group_id in
            <foreach collection="rule.targetIds" item="item" open="(" separator="," close=")" index="index">
                #{item}
            </foreach>
        </if>
        group by oi.user_id
        having 1=1
        <if test="rule.buyCntMin !=null ">
            and count(oi.id) >= #{rule.buyCntMin}
        </if>
        <if test="rule.buyCntMax !=null ">
            and count(oi.id) <![CDATA[<=]]> #{rule.buyCntMax}
        </if>
        <if test="rule.realAmountMin !=null ">
            and sum(oi.real_amount) >= #{rule.realAmountMin}
        </if>
        <if test="rule.realAmountMax !=null ">
            and sum(oi.real_amount) <![CDATA[<=]]> #{rule.realAmountMax}
        </if>
        )
    </select>
    <select id="bindBuyOfCategoryIdRuleCompute" resultType="java.lang.Long" timeout="900">
        SELECT DISTINCT c.user_id
        FROM card c
        INNER JOIN customer_user cu ON cu.id = c.user_id
        WHERE
        cu.delflag = 0
        AND cu.log_off = 0
        <if test="rule.bindTimeStart !=null ">
            AND c.receive_time >= #{rule.bindTimeStart}
        </if>
        <if test="rule.bindTimeEnd !=null ">
            AND c.receive_time <![CDATA[<=]]> #{rule.bindTimeEnd}
        </if>
        <if test="rule.cardCategoryIds != null and rule.cardCategoryIds.size() > 0">
            AND c.category_id in
            <foreach collection="rule.cardCategoryIds" item="item" open="(" separator="," close=")" index="index">
                #{item}
            </foreach>
        </if>
        AND c.receive_time IS NOT NULL
        AND EXISTS (
        SELECT 1
        FROM order_info oi
        inner join order_sku os on os.order_no=oi.order_no
        inner join item_product ip on ip.id = os.product_id
        WHERE
        oi.delflag = 0
        AND oi.user_id = c.user_id
        AND oi.pay_time IS NOT NULL
        AND oi.channel='lpk'
        and oi.order_type in (0,1,2,3,4)
        and oi.parent_type in(1,3)
        and oi.order_status not in (4,0)
        <if test="rule.timeStart !=null ">
            AND oi.pay_time >= #{rule.timeStart}
        </if>
        <if test="rule.timeEnd !=null ">
            AND oi.pay_time <![CDATA[<=]]> #{rule.timeEnd}
        </if>
        <if test="rule.targetIds != null and rule.targetIds.size() > 0">
            and ip.cate_id in
            <foreach collection="rule.targetIds" item="item" open="(" separator="," close=")" index="index">
                #{item}
            </foreach>
        </if>
        group by oi.user_id
        having 1=1
        <if test="rule.buyCntMin !=null ">
            and count(oi.id) >= #{rule.buyCntMin}
        </if>
        <if test="rule.buyCntMax !=null ">
            and count(oi.id) <![CDATA[<=]]> #{rule.buyCntMax}
        </if>
        <if test="rule.realAmountMin !=null ">
            and sum(oi.real_amount) >= #{rule.realAmountMin}
        </if>
        <if test="rule.realAmountMax !=null ">
            and sum(oi.real_amount) <![CDATA[<=]]> #{rule.realAmountMax}
        </if>
        )
    </select>
    <select id="bindBuyOfCardCategoryIdRuleCompute" resultType="java.lang.Long" timeout="900">
        SELECT DISTINCT c.user_id
        FROM card c
        INNER JOIN customer_user cu ON cu.id = c.user_id
        WHERE
        cu.delflag = 0
        AND cu.log_off = 0
        <if test="rule.bindTimeStart !=null ">
            AND c.receive_time >= #{rule.bindTimeStart}
        </if>
        <if test="rule.bindTimeEnd !=null ">
            AND c.receive_time <![CDATA[<=]]> #{rule.bindTimeEnd}
        </if>
        <if test="rule.cardCategoryIds != null and rule.cardCategoryIds.size() > 0">
            AND c.category_id in
            <foreach collection="rule.cardCategoryIds" item="item" open="(" separator="," close=")" index="index">
                #{item}
            </foreach>
        </if>
        AND c.receive_time IS NOT NULL
        AND EXISTS (
        SELECT 1
        FROM order_info oi
        inner join order_sku os on os.order_no=oi.order_no
        inner join order_sku_card osc on osc.order_sku_id = os.id
        inner join card cc on cc.card_number = osc.card_number
        WHERE
        oi.delflag = 0
        AND oi.user_id = c.user_id
        AND oi.pay_time IS NOT NULL
        AND oi.channel='lpk'
        and oi.order_type in (0,1,2,3,4)
        and oi.parent_type in(1,3)
        and oi.order_status not in (4,0)
        <if test="rule.timeStart !=null ">
            AND oi.pay_time >= #{rule.timeStart}
        </if>
        <if test="rule.timeEnd !=null ">
            AND oi.pay_time <![CDATA[<=]]> #{rule.timeEnd}
        </if>
        <if test="rule.targetIds != null and rule.targetIds.size() > 0">
            and cc.category_id in
            <foreach collection="rule.targetIds" item="item" open="(" separator="," close=")" index="index">
                #{item}
            </foreach>
        </if>
        group by oi.user_id
        having 1=1
        <if test="rule.buyCntMin !=null ">
            and count(oi.id) >= #{rule.buyCntMin}
        </if>
        <if test="rule.buyCntMax !=null ">
            and count(oi.id) <![CDATA[<=]]> #{rule.buyCntMax}
        </if>
        <if test="rule.realAmountMin !=null ">
            and sum(oi.real_amount) >= #{rule.realAmountMin}
        </if>
        <if test="rule.realAmountMax !=null ">
            and sum(oi.real_amount) <![CDATA[<=]]> #{rule.realAmountMax}
        </if>
        )
    </select>
    <select id="bindBuyProductOfProductIdRuleCompute" resultType="java.lang.Long" timeout="900">
        select distinct cu.id
        from customer_user cu
        inner join order_info oi on oi.user_id=cu.id and oi.channel='lpk' and oi.order_type in (0,1,2,3,4)
        inner join order_sku os on os.order_no=oi.order_no
        where oi.parent_type in (1,3) and cu.log_off = 0
        and oi.order_status not in (4,0)
        and oi.pay_time >= #{rule.timeStart}
        and oi.pay_time <![CDATA[<=]]> #{rule.timeEnd}
        <if test="rule.targetIds != null and rule.targetIds.size() > 0">
            and os.product_id in
            <foreach collection="rule.targetIds" item="item" open="(" separator="," close=")" index="index">
                #{item}
            </foreach>
        </if>
        and EXISTS(select 1 from card c where c.user_id = cu.id and c.card_type in(1,0) and c.receive_time is not null)
    </select>
    <select id="bindBuyProductOfGroupIdRuleCompute" resultType="java.lang.Long" timeout="900">
        select distinct cu.id
        from customer_user cu
        inner join order_info oi on oi.user_id=cu.id and oi.channel='lpk' and oi.order_type in (0,1,2,3,4)
        inner join order_sku os on os.order_no=oi.order_no
        inner join item_product_group ipg on ipg.product_id = os.product_id
        where oi.parent_type in (1,3) and cu.log_off = 0
        and oi.order_status not in (4,0)
        and oi.pay_time >= #{rule.timeStart}
        and oi.pay_time <![CDATA[<=]]> #{rule.timeEnd}
        <if test="rule.targetIds != null and rule.targetIds.size() > 0">
            and ipg.group_id in
            <foreach collection="rule.targetIds" item="item" open="(" separator="," close=")" index="index">
                #{item}
            </foreach>
        </if>
        and EXISTS(select 1 from card c where c.user_id = cu.id and c.card_type in(1,0) and c.receive_time is not null)
    </select>
    <select id="bindBuyProductOfCategoryIdRuleCompute" resultType="java.lang.Long" timeout="900">
        select distinct cu.id
        from customer_user cu
        inner join order_info oi on oi.user_id=cu.id and oi.channel='lpk' and oi.order_type in (0,1,2,3,4)
        inner join order_sku os on os.order_no=oi.order_no
        inner join item_product ip on ip.id = os.product_id
        where oi.parent_type in (1,3) and cu.log_off = 0
        and oi.order_status not in (4,0)
        and oi.pay_time >= #{rule.timeStart}
        and oi.pay_time <![CDATA[<=]]> #{rule.timeEnd}
        <if test="rule.targetIds != null and rule.targetIds.size() > 0">
            and ip.cate_id in
            <foreach collection="rule.targetIds" item="item" open="(" separator="," close=")" index="index">
                #{item}
            </foreach>
        </if>
        and EXISTS(select 1 from card c where c.user_id = cu.id and c.card_type in(1,0) and c.receive_time is not null)
    </select>
    <select id="bindBuyProductOfCardCategoryIdRuleCompute" resultType="java.lang.Long" timeout="900">
        select distinct cu.id
        from customer_user cu
        inner join order_info oi on oi.user_id=cu.id and oi.channel='lpk' and oi.order_type in (0,1,2,3,4)
        inner join order_sku os on os.order_no=oi.order_no
        inner join order_sku_card osc on osc.order_sku_id = os.id
        inner join card c on c.card_number = osc.card_number
        where oi.parent_type in (1,3) and cu.log_off = 0
        and oi.order_status not in (4,0)
        and oi.pay_time >= #{rule.timeStart}
        and oi.pay_time <![CDATA[<=]]> #{rule.timeEnd}
        <if test="rule.targetIds != null and rule.targetIds.size() > 0">
            and c.category_id in
            <foreach collection="rule.targetIds" item="item" open="(" separator="," close=")" index="index">
                #{item}
            </foreach>
        </if>
        and EXISTS(select 1 from card c where c.user_id = cu.id and c.card_type in(1,0) and c.receive_time is not null)
    </select>
    <select id="bindCardRuleCompute" resultType="java.lang.Long" timeout="900">
        select distinct cu.id
        from customer_user cu
        inner join card c on c.user_id = cu.id
        inner join card_category cc on cc.id = c.category_id
        where cu.log_off = 0
        and c.receive_time >= #{rule.timeStart}
        and c.receive_time <![CDATA[<=]]> #{rule.timeEnd}
        and cc.id in
        <foreach collection="rule.cardCategoryIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and c.card_type in (1,0)
    </select>
    <select id="privilegeNotSetRuleCompute" resultType="java.lang.Long" timeout="900">
        select distinct cu.id from customer_user cu
        inner join card c on c.user_id = cu.id
        left join (
            select card_number from milk_dispatch_plan
            where delflag = 0 and plan_status not in(5,7,9)
            and privilege_label_id = #{rule.privilegeLabelId}
            and card_number in
            <foreach close=")" collection="rule.cardNumbers" item="item" open="(" separator=", ">
                #{item}
            </foreach>
        )t on c.card_number = t.card_number
        where cu.log_off = 0 and c.card_number in
        <foreach close=")" collection="rule.cardNumbers" item="item" open="(" separator=", ">
        #{item}
        </foreach>
          and t.card_number is  null
    </select>
    <select id="cardTotalRemainingCountRuleCompute" resultType="java.lang.Long" timeout="900">
        select distinct cu.id
        from customer_user cu
        inner join card c on c.user_id = cu.id
        where c.card_status !=4 and c.user_id > 0 and cu.log_off = 0
        <if test="rule.cardCategoryIds != null and rule.cardCategoryIds.size() > 0">
            and c.category_id in
            <foreach collection="rule.cardCategoryIds" item="item" open="(" separator="," close=")" index="index">
                #{item}
            </foreach>
        </if>
        group by cu.id
        having 1=1
        <if test="rule.countMin != null">
            and sum(remaining_count) >= #{rule.countMin}
        </if>
        <if test="rule.countMax != null">
            and sum(remaining_count) <![CDATA[<=]]> #{rule.countMax}
        </if>
    </select>
    <select id="findUpgradeUserIdsByPrivilegeLabelId" resultType="java.lang.Long">
        select user_id from milk_dispatch_plan
        where delflag = 0
          and plan_status not in(5,7,9)
          and privilege_label_id = #{rule.privilegeLabelId}
        group by user_id
    </select>
    <select id="lastDealRuleCompute" resultType="java.lang.Long" timeout="900">
        SELECT
        distinct cu.id
        FROM
        customer_user cu
        INNER JOIN order_info oi ON oi.user_id = cu.id AND oi.channel = 'lpk' AND oi.order_type IN ( 0, 1, 2, 3, 4 ) AND oi.parent_type IN (1,2)
        where cu.log_off = 0
        <if test="rule.amountMin !=null ">
            and oi.real_amount >= #{rule.amountMin}
        </if>
        <if test="rule.amountMax !=null ">
            and oi.real_amount <![CDATA[<=]]> #{rule.amountMax}
        </if>
        group by cu.id
        having  1=1
        <if test="rule.timeStart !=null ">
            and max(oi.pay_time) >= #{rule.timeStart}
        </if>
        <if test="rule.timeEnd !=null ">
            and max(oi.pay_time) <![CDATA[<=]]> #{rule.timeEnd}
        </if>
    </select>
    <select id="findNonMember" resultType="com.hengtiansoft.user.entity.po.CustomerUser">
        SELECT
            *
        FROM
            customer_user
        WHERE
              delflag = 0
          AND log_off = 0
          AND nascent_flag = 0
          AND first_login_time >= #{timeStart}
          AND LENGTH(phone) = 11
          AND phone like '1%';
    </select>
    <select id="shopCartOfCardCategoryIdRuleCompute" resultType="java.lang.Long" timeout="900">
        select distinct cu.id
        from customer_user cu
        inner join shop_cart sc on sc.user_id=cu.id
        inner join item_product ip on ip.id = sc.product_id
        where cu.log_off=0
        <if test="rule.timeStart !=null ">
            and sc.create_time >= #{rule.timeStart}
        </if>
        <if test="rule.timeEnd !=null ">
            and sc.create_time <![CDATA[<=]]> #{rule.timeEnd}
        </if>
        <if test="rule.targetIds != null and rule.targetIds.size > 0">
            and ip.card_category_id in
            <foreach collection="rule.targetIds" item="item" open="(" separator="," close=")" index="index">
                #{item}
            </foreach>
        </if>
    </select>
    <select id="joinPrivilegeLabelActivityRuleCompute" resultType="java.lang.Long" timeout="900">
        select distinct cu.id
        from customer_user cu
        inner join milk_dispatch_plan plan on plan.user_id = cu.id
        where  cu.log_off = 0 and plan.delflag = 0 and plan.privilege_label_id is not null
        <if test="rule.timeStart !=null ">
            and plan.create_time >= #{rule.timeStart}
        </if>
        <if test="rule.timeEnd !=null ">
            and plan.create_time <![CDATA[<=]]> #{rule.timeEnd}
        </if>
    </select>
    <select id="productNotBuyOfCardCategoryIdRuleCompute" resultType="java.lang.Long" timeout="900">
        select distinct cu.id
        from customer_user cu
        left join (
            select cu.id
            from customer_user cu
            inner join order_info oi on oi.user_id=cu.id and oi.channel='lpk' and oi.order_type in (0,1,2,3,4)
            inner join order_sku os on os.order_no=oi.order_no
            inner join order_sku_card osc on osc.order_sku_id = os.id
            inner join card c on c.card_number = osc.card_number
            where oi.parent_type in (1,3) and cu.log_off = 0
            and oi.order_status not in (4,0)
            <if test="rule.timeStart !=null ">
                and oi.pay_time >= #{rule.timeStart}
            </if>
            <if test="rule.timeEnd !=null ">
                and oi.pay_time <![CDATA[<=]]> #{rule.timeEnd}
            </if>
            <if test="rule.targetIds != null and rule.targetIds.size > 0">
                and c.category_id in
                <foreach collection="rule.targetIds" item="item" open="(" separator="," close=")" index="index">
                    #{item}
                </foreach>
            </if>
        ) tmp on tmp.id=cu.id
        where cu.log_off = 0 and tmp.id is null;
    </select>
    <select id="notBindCardRuleCompute" resultType="java.lang.Long" timeout="900">
        select distinct cu.id
        from customer_user cu
        left join(
            select cu.id
            from customer_user cu
            inner join card c on c.user_id = cu.id
            where cu.log_off = 0
            <if test="rule.timeStart !=null ">
                and c.receive_time >= #{rule.timeStart}
            </if>
            <if test="rule.timeEnd !=null ">
                and c.receive_time <![CDATA[<=]]> #{rule.timeEnd}
            </if>
            <if test="rule.cardCategoryIds != null and rule.cardCategoryIds.size() > 0">
                and c.category_id in
                <foreach collection="rule.cardCategoryIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            and c.card_type in (1,0)
        ) tmp on tmp.id = cu.id
        where cu.log_off = 0 and tmp.id is null;
    </select>
    <select id="findByNickName" resultType="com.hengtiansoft.user.entity.po.CustomerUser">
        select * from customer_user where log_off = 0 and delflag = 0 and nick_name = #{nickName} and id != #{id} limit 1
    </select>
</mapper>