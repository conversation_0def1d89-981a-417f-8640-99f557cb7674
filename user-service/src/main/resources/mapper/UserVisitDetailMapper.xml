<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hengtiansoft.user.entity.mapper.UserVisitDetailMapper">
  <resultMap id="BaseResultMap" type="com.hengtiansoft.user.entity.po.UserVisitDetail">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="visit_time" jdbcType="TIMESTAMP" property="visitTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <select id="selectLastVisitByUserIds" resultType="com.hengtiansoft.user.entity.po.UserVisitDetail">
    select user_id as userId, max(visit_time) as visitTime
    from user_visit_detail
    where user_id in
    <foreach close=")" collection="userIds" item="item" open="(" separator=", ">
      #{item}
    </foreach>
    group by user_id
  </select>

  <select id="selectLastVisitByPhones" resultType="com.hengtiansoft.user.entity.po.UserVisitDetail">
    select cu.phone, max(visit_time) as visitTime
    from user_visit_detail uvd
    left join customer_user cu on cu.id = uvd.user_id
    where phone in
    <foreach close=")" collection="phones" item="item" open="(" separator=", ">
      #{item}
    </foreach>
    group by phone
  </select>
</mapper>